module git.code.oa.com/cloud-msp/migration-lister

go 1.18

require (
	git.code.oa.com/cloud-msp/huaweicloud-obs-sdk-go v0.0.0-20201102042330-5f688cb2b58f
	git.code.oa.com/cloud-msp/migration-worker v0.5.19-0.20220316133647-411dd7691747
	git.code.oa.com/pulse-line/pl_boot v1.1.16
	git.code.oa.com/pulse-line/pl_config v0.0.6
	git.code.oa.com/pulse-line/pl_prom v0.0.7
	git.code.oa.com/pulse-line/pl_qcloudapi_v3 v1.0.6
	git.code.oa.com/sec-api/go/scurl v0.2.2
	git.woa.com/internal-cos-sdk/internal-cos-go-sdk-v5 v0.0.0-20231228040948-b2f09f8d710b
	github.com/Azure/azure-storage-blob-go v0.15.0
	github.com/aliyun/aliyun-oss-go-sdk v2.1.4+incompatible
	github.com/apache/pulsar-client-go v0.9.0
	github.com/aws/aws-sdk-go v1.43.18
	github.com/baidubce/bce-sdk-go v0.9.108
	github.com/dlclark/regexp2 v1.4.0
	github.com/duke-git/lancet v1.3.4
	github.com/golang/mock v1.6.0
	github.com/json-iterator/go v1.1.12
	github.com/ks3sdklib/aws-sdk-go v1.0.7
	github.com/mattn/go-sqlite3 v1.10.0
	github.com/mitchellh/mapstructure v1.5.0
	github.com/pkg/errors v0.9.1
	github.com/prashantv/gostub v1.1.0
	github.com/qiniu/api.v7/v7 v7.8.2
	github.com/stretchr/testify v1.8.4
	github.com/syndtr/goleveldb v1.0.0
	github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common v1.0.563
	github.com/tencentyun/cos-go-sdk-v5 v0.7.49
	github.com/ufilesdk-dev/ufile-gosdk v0.0.0-20200527093105-016e75e222f6
	go.hlw.tencent.com/alego v1.0.2
	go.hlw.tencent.com/goat-genapis v0.0.0-20230928103829-f44064b1fecd
	go.uber.org/atomic v1.9.0
	go.uber.org/ratelimit v0.2.0
	go.uber.org/zap v1.21.0
	golang.org/x/net v0.0.0-20220225172249-27dd8689420f
	google.golang.org/grpc v1.46.0
	google.golang.org/protobuf v1.28.0
)

require (
	git.code.oa.com/components/l5 v0.3.0 // indirect
	git.code.oa.com/polaris/polaris-go v0.10.3 // indirect
	git.code.oa.com/pulse-line/pl_common v0.0.4 // indirect
	git.code.oa.com/pulse-line/pl_interceptor v1.1.4 // indirect
	git.code.oa.com/pulse-line/pl_logger v1.6.5 // indirect
	git.code.oa.com/pulse-line/pl_query v0.0.2 // indirect
	git.code.oa.com/pulse-line/plgrpc v0.1.0 // indirect
	git.woa.com/polaris/polaris-go/v2 v2.5.12 // indirect
	git.woa.com/polaris/polaris-server-api/api/metric v1.0.0 // indirect
	git.woa.com/polaris/polaris-server-api/api/monitor v1.0.7 // indirect
	git.woa.com/polaris/polaris-server-api/api/v1/grpc v1.0.2 // indirect
	git.woa.com/polaris/polaris-server-api/api/v1/model v1.1.3 // indirect
	git.woa.com/polaris/polaris-server-api/api/v2/grpc v1.0.0 // indirect
	git.woa.com/polaris/polaris-server-api/api/v2/model v1.0.3 // indirect
	github.com/99designs/go-keychain v0.0.0-20191008050251-8e49817e8af4 // indirect
	github.com/99designs/keyring v1.2.1 // indirect
	github.com/AthenZ/athenz v1.10.39 // indirect
	github.com/Azure/azure-pipeline-go v0.2.3 // indirect
	github.com/DataDog/zstd v1.5.0 // indirect
	github.com/andres-erbsen/clock v0.0.0-20160526145045-9e14626cd129 // indirect
	github.com/ardielle/ardielle-go v1.5.2 // indirect
	github.com/armon/go-metrics v0.3.4 // indirect
	github.com/beorn7/perks v1.0.1 // indirect
	github.com/cespare/xxhash/v2 v2.1.1 // indirect
	github.com/clbanning/mxj v1.8.4 // indirect
	github.com/danieljoos/wincred v1.1.2 // indirect
	github.com/davecgh/go-spew v1.1.1 // indirect
	github.com/deckarep/golang-set v1.7.1 // indirect
	github.com/dvsekhvalnov/jose2go v1.5.0 // indirect
	github.com/fatih/color v1.10.0 // indirect
	github.com/fsnotify/fsnotify v1.5.1 // indirect
	github.com/ghodss/yaml v1.0.0 // indirect
	github.com/godbus/dbus v0.0.0-20190726142602-4481cbc300e2 // indirect
	github.com/gogo/protobuf v1.3.2 // indirect
	github.com/golang-jwt/jwt v3.2.1+incompatible // indirect
	github.com/golang/protobuf v1.5.2 // indirect
	github.com/golang/snappy v0.0.1 // indirect
	github.com/google/go-querystring v1.1.0 // indirect
	github.com/google/uuid v1.4.0 // indirect
	github.com/gookit/color v1.3.6 // indirect
	github.com/grpc-ecosystem/go-grpc-middleware v1.2.2 // indirect
	github.com/grpc-ecosystem/grpc-gateway v1.16.0 // indirect
	github.com/grpc-ecosystem/grpc-opentracing v0.0.0-20180507213350-8e809c8a8645 // indirect
	github.com/gsterjov/go-libsecret v0.0.0-20161001094733-a6f4afe4910c // indirect
	github.com/hashicorp/consul/api v1.9.1 // indirect
	github.com/hashicorp/errwrap v1.1.0 // indirect
	github.com/hashicorp/go-cleanhttp v0.5.1 // indirect
	github.com/hashicorp/go-hclog v0.15.0 // indirect
	github.com/hashicorp/go-immutable-radix v1.3.0 // indirect
	github.com/hashicorp/go-multierror v1.1.0 // indirect
	github.com/hashicorp/go-rootcerts v1.0.2 // indirect
	github.com/hashicorp/golang-lru v0.5.4 // indirect
	github.com/hashicorp/hcl v1.0.0 // indirect
	github.com/hashicorp/serf v0.9.5 // indirect
	github.com/iancoleman/strcase v0.1.2 // indirect
	github.com/jmespath/go-jmespath v0.4.0 // indirect
	github.com/juju/errors v0.0.0-20200330140219-3fe23663418f // indirect
	github.com/klauspost/compress v1.14.4 // indirect
	github.com/konsorten/go-windows-terminal-sequences v1.0.3 // indirect
	github.com/linkedin/goavro/v2 v2.9.8 // indirect
	github.com/magiconair/properties v1.8.5 // indirect
	github.com/mattn/go-colorable v0.1.8 // indirect
	github.com/mattn/go-ieproxy v0.0.1 // indirect
	github.com/mattn/go-isatty v0.0.12 // indirect
	github.com/matttproud/golang_protobuf_extensions v1.0.2-0.20181231171920-c182affec369 // indirect
	github.com/mitchellh/go-homedir v1.1.0 // indirect
	github.com/modern-go/concurrent v0.0.0-20180306012644-bacd9c7ef1dd // indirect
	github.com/modern-go/reflect2 v1.0.2 // indirect
	github.com/mozillazg/go-httpheader v0.4.0 // indirect
	github.com/mtibben/percent v0.2.1 // indirect
	github.com/natefinch/lumberjack v2.0.0+incompatible // indirect
	github.com/opentracing/opentracing-go v1.2.0 // indirect
	github.com/pelletier/go-toml v1.9.3 // indirect
	github.com/pierrec/lz4 v2.2.6+incompatible // indirect
	github.com/pmezard/go-difflib v1.0.0 // indirect
	github.com/prometheus/client_golang v1.11.1 // indirect
	github.com/prometheus/client_model v0.2.0 // indirect
	github.com/prometheus/common v0.26.0 // indirect
	github.com/prometheus/procfs v0.6.0 // indirect
	github.com/rogpeppe/go-internal v1.12.0 // indirect
	github.com/sirupsen/logrus v1.6.0 // indirect
	github.com/spaolacci/murmur3 v1.1.0 // indirect
	github.com/spf13/afero v1.6.0 // indirect
	github.com/spf13/cast v1.4.1 // indirect
	github.com/spf13/jwalterweatherman v1.1.0 // indirect
	github.com/spf13/pflag v1.0.5 // indirect
	github.com/spf13/viper v1.8.1 // indirect
	github.com/subosito/gotenv v1.2.0 // indirect
	github.com/uber/jaeger-client-go v2.25.0+incompatible // indirect
	github.com/uber/jaeger-lib v2.4.0+incompatible // indirect
	go.uber.org/automaxprocs v1.4.0 // indirect
	go.uber.org/multierr v1.6.0 // indirect
	golang.org/x/oauth2 v0.0.0-20210402161424-2e8d93401602 // indirect
	golang.org/x/sync v0.0.0-20210220032951-036812b2e83c // indirect
	golang.org/x/sys v0.12.0 // indirect
	golang.org/x/term v0.0.0-20210927222741-03fcf44c2211 // indirect
	golang.org/x/text v0.3.7 // indirect
	golang.org/x/time v0.0.0-20191024005414-555d28b269f0 // indirect
	google.golang.org/appengine v1.6.7 // indirect
	google.golang.org/genproto v0.0.0-20210602131652-f16073e35f0c // indirect
	gopkg.in/ini.v1 v1.62.0 // indirect
	gopkg.in/natefinch/lumberjack.v2 v2.0.0 // indirect
	gopkg.in/yaml.v2 v2.4.0 // indirect
	gopkg.in/yaml.v3 v3.0.1 // indirect
)

replace (
	git.code.oa.com/cloud-msp/huaweicloud-obs-sdk-go v0.0.0-20201102042330-5f688cb2b58f => git.woa.com/cloud-msp/huaweicloud-obs-sdk-go v0.0.0-20201102042330-5f688cb2b58f
	git.code.oa.com/cloud-msp/migration-worker v0.5.19-0.20220316133647-411dd7691747 => git.woa.com/cloud-msp/migration-worker v0.5.19-0.20220316133647-411dd7691747
	git.code.oa.com/components/l5 v0.2.2 => git.woa.com/components/l5 v0.2.2
	git.code.oa.com/components/l5 v0.3.0 => git.woa.com/components/l5 v0.3.0
	git.code.oa.com/polaris/polaris-go v0.10.3 => git.woa.com/polaris/polaris-go v0.10.3
	git.code.oa.com/polaris/polaris-go v0.5.6 => git.woa.com/polaris/polaris-go v0.5.6
	git.code.oa.com/polaris/polaris-go v0.7.6 => git.woa.com/polaris/polaris-go v0.7.6
	git.code.oa.com/pulse-line/pl_boot v1.0.14 => git.woa.com/pulse-line/pl_boot v1.0.14
	git.code.oa.com/pulse-line/pl_boot v1.0.2 => git.woa.com/pulse-line/pl_boot v1.0.2
	git.code.oa.com/pulse-line/pl_boot v1.1.16 => git.woa.com/pulse-line/pl_boot v1.1.16
	git.code.oa.com/pulse-line/pl_common v0.0.1 => git.woa.com/pulse-line/pl_common v0.0.1
	git.code.oa.com/pulse-line/pl_common v0.0.2 => git.woa.com/pulse-line/pl_common v0.0.2
	git.code.oa.com/pulse-line/pl_common v0.0.4 => git.woa.com/pulse-line/pl_common v0.0.4
	git.code.oa.com/pulse-line/pl_config v0.0.0-20190711092008-5232084c05b0 => git.woa.com/pulse-line/pl_config v0.0.0-20190711092008-5232084c05b0
	git.code.oa.com/pulse-line/pl_config v0.0.1 => git.woa.com/pulse-line/pl_config v0.0.1
	git.code.oa.com/pulse-line/pl_config v0.0.2 => git.woa.com/pulse-line/pl_config v0.0.2
	git.code.oa.com/pulse-line/pl_config v0.0.5 => git.woa.com/pulse-line/pl_config v0.0.5
	git.code.oa.com/pulse-line/pl_config v0.0.6 => git.woa.com/pulse-line/pl_config v0.0.6
	git.code.oa.com/pulse-line/pl_interceptor v0.0.13 => git.woa.com/pulse-line/pl_interceptor v0.0.13
	git.code.oa.com/pulse-line/pl_interceptor v0.0.9 => git.woa.com/pulse-line/pl_interceptor v0.0.9
	git.code.oa.com/pulse-line/pl_interceptor v1.0.17 => git.woa.com/pulse-line/pl_interceptor v1.0.17
	git.code.oa.com/pulse-line/pl_interceptor v1.1.4 => git.woa.com/pulse-line/pl_interceptor v1.1.4
	git.code.oa.com/pulse-line/pl_logger v0.0.0-20190808123157-8b35c889b20b => git.woa.com/pulse-line/pl_logger v0.0.0-20190808123157-8b35c889b20b
	git.code.oa.com/pulse-line/pl_logger v1.1.0 => git.woa.com/pulse-line/pl_logger v1.1.0
	git.code.oa.com/pulse-line/pl_logger v1.4.0 => git.woa.com/pulse-line/pl_logger v1.4.0
	git.code.oa.com/pulse-line/pl_logger v1.4.3 => git.woa.com/pulse-line/pl_logger v1.4.3
	git.code.oa.com/pulse-line/pl_logger v1.6.5 => git.woa.com/pulse-line/pl_logger v1.6.5
	git.code.oa.com/pulse-line/pl_prom v0.0.0-20190729061236-d23c1abdf29f => git.woa.com/pulse-line/pl_prom v0.0.0-20190729061236-d23c1abdf29f
	git.code.oa.com/pulse-line/pl_prom v0.0.3 => git.woa.com/pulse-line/pl_prom v0.0.3
	git.code.oa.com/pulse-line/pl_prom v0.0.7 => git.woa.com/pulse-line/pl_prom v0.0.7
	git.code.oa.com/pulse-line/pl_qcloudapi_v3 v0.0.2 => git.woa.com/pulse-line/pl_qcloudapi_v3 v0.0.2
	git.code.oa.com/pulse-line/pl_qcloudapi_v3 v0.0.4 => git.woa.com/pulse-line/pl_qcloudapi_v3 v0.0.4
	git.code.oa.com/pulse-line/pl_qcloudapi_v3 v0.0.5-0.20200428051346-c95284a373b3 => git.woa.com/pulse-line/pl_qcloudapi_v3 v0.0.5-0.20200428051346-c95284a373b3
	git.code.oa.com/pulse-line/pl_qcloudapi_v3 v1.0.0 => git.woa.com/pulse-line/pl_qcloudapi_v3 v1.0.0
	git.code.oa.com/pulse-line/pl_qcloudapi_v3 v1.0.6 => git.woa.com/pulse-line/pl_qcloudapi_v3 v1.0.6
	git.code.oa.com/pulse-line/pl_query v0.0.0-20190716143206-99cf60d1bf2a => git.woa.com/pulse-line/pl_query v0.0.0-20190716143206-99cf60d1bf2a
	git.code.oa.com/pulse-line/pl_query v0.0.2 => git.woa.com/pulse-line/pl_query v0.0.2
	git.code.oa.com/pulse-line/plgrpc v0.0.0-20190921133608-0640b1f7a57f => git.woa.com/pulse-line/plgrpc v0.0.0-20190921133608-0640b1f7a57f
	git.code.oa.com/pulse-line/plgrpc v0.0.4 => git.woa.com/pulse-line/plgrpc v0.0.4
	git.code.oa.com/pulse-line/plgrpc v0.0.9 => git.woa.com/pulse-line/plgrpc v0.0.9
	git.code.oa.com/pulse-line/plgrpc v0.1.0 => git.woa.com/pulse-line/plgrpc v0.1.0
	git.code.oa.com/sec-api/go/scurl v0.1.4 => git.woa.com/sec-api/go/scurl v0.1.4
	git.code.oa.com/sec-api/go/scurl v0.2.2 => git.woa.com/sec-api/go/scurl v0.2.2
	github.com/coreos/go-systemd => github.com/coreos/go-systemd/v22 v22.0.0
	github.com/hashicorp/consul => github.com/hashicorp/consul v1.5.1
	go.hlw.tencent.com/alego v0.0.0-20200410061044-36fd11a36ec2 => git.woa.com/goat/alego v0.0.0-20200410061044-36fd11a36ec2
	go.hlw.tencent.com/alego v1.0.2 => git.woa.com/goat/alego v1.0.2
	go.hlw.tencent.com/goat v0.0.0-20191129112600-d509b4622ec2 => git.woa.com/goat/goat v0.0.0-20191129112600-d509b4622ec2
	go.hlw.tencent.com/goat v1.0.0 => git.woa.com/goat/goat v1.0.0
	go.hlw.tencent.com/goat v1.0.1 => git.woa.com/goat/goat v1.0.1
	go.hlw.tencent.com/goat-genapis v0.0.0-20190814095158-64cc06bc3bc0 => git.woa.com/goat/goat-genapis v0.0.0-20190814095158-64cc06bc3bc0
	go.hlw.tencent.com/goat-genapis v0.0.0-20191128121805-3cca05a14b4b => git.woa.com/goat/goat-genapis v0.0.0-20191128121805-3cca05a14b4b
	go.hlw.tencent.com/goat-genapis v0.0.0-20210708032944-30b42573f576 => git.woa.com/goat/goat-genapis v0.0.0-20210708032944-30b42573f576
	go.hlw.tencent.com/goat-genapis v0.0.0-20230928103829-f44064b1fecd => git.woa.com/goat/goat-genapis v0.0.0-20230928103829-f44064b1fecd
	go.hlw.tencent.com/goat-grpc-l5 v0.0.0 => git.woa.com/goat/goat-grpc-l5 v0.0.0
	go.hlw.tencent.com/qbaseId v0.0.0-20191217122605-76137ef7eda7 => git.woa.com/goat/qbaseId v0.0.0-20191217122605-76137ef7eda7
	go.hlw.tencent.com/version v0.0.0-20191231091115-1f7c4bdd9b2b => git.woa.com/goat/version v0.0.0-20191231091115-1f7c4bdd9b2b
	labix.org/v2/mgo => github.com/go-mgo/mgo v0.0.0-20160801194620-b6121c6199b7
	launchpad.net/gocheck => github.com/go-check/check v0.0.0-20180628173108-788fd7840127
)
