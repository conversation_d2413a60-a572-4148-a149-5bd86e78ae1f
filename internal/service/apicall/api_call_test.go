package apicall

import (
	"testing"

	"git.code.oa.com/cloud-msp/migration-lister/tools"
	"git.code.oa.com/pulse-line/pl_boot"
	"github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common"
	"github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common/profile"
)

func boot() {
	boot := pl_boot.NewBootstrapper()
	boot.RegisterAppContextWithConfigPath("../../configs", "test")
}

func getAPIClient(secretId, secretKey, host string) *ApiCall {
	credential := common.NewCredential(secretId, secretKey)

	cpf := profile.NewClientProfile()
	cpf.HttpProfile.Endpoint = host
	client, _ := tools.NewClient(credential, "", cpf)
	return &ApiCall{
		Client: client,
	}
}

func Test_GetStatus(t *testing.T) {
	boot()

	host := "msp.tencentcloudapi.com"
	secretId := ""
	secretKey := ""
	api := getAPIClient(secretId, secretKey, host)

	jobId := "fm-dh2n7b3h"
	status, err := api.GetStatus(jobId)
	if err != nil {
		t.Error(err)
	}
	t.Log(status)
}
