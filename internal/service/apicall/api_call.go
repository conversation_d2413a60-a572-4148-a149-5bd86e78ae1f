package apicall

import (
	"strconv"
	"sync"
	"time"

	"git.code.oa.com/cloud-msp/migration-lister/api"
	"git.code.oa.com/cloud-msp/migration-lister/internal/service/models"
	"git.code.oa.com/cloud-msp/migration-lister/pkg/conf"
	"git.code.oa.com/cloud-msp/migration-lister/pkg/metrics"
	"git.code.oa.com/cloud-msp/migration-lister/tools"
	"git.code.oa.com/pulse-line/pl_boot"
	"git.code.oa.com/pulse-line/pl_prom"
	"github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common"
	"github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common/errors"
	"github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common/profile"
	"go.hlw.tencent.com/goat-genapis/goatapis/msp"
	"go.uber.org/zap"
)

const (
	// 任务结果上传到cos的状态
	UploadBeginning = "1" // 开始上传
	UPLOADING       = "2" // 上传中
	UploadSuccess   = "3" // 上传成功
)

const (
	RetryInterval = 3 * time.Second
)

var (
	apiCall *ApiCall
	once    sync.Once
)

// ApiCall 调用云平台借口的 Http 客户端
// 接口 RUL 从配置文件中加在
type ApiCall struct {
	Client *tools.Client
}

// GetApiCall 单例模式初始化 ApiCall
// 由于go的初始化机制，需要手动调用初始化
var GetApiCall = func() ApiCallIf {
	once.Do(func() {
		credential := common.NewCredential(
			conf.ListerConfig.TencentCloud.SecretId,
			conf.ListerConfig.TencentCloud.SecretKey,
		)
		cpf := profile.NewClientProfile()
		cpf.HttpProfile.Endpoint = conf.ListerConfig.TencentCloud.Host
		client, _ := tools.NewClient(credential, "", cpf)
		apiCall = &ApiCall{
			Client: client,
		}
		pl_boot.AppCtx.GetAppLogger().Info("init ApiCall success", zap.Any("conf", conf.ListerConfig.TencentCloud))
	})
	return apiCall
}

// 接受到 worker 返回的 fragResult 后上报任务进度, 包括成功/失败文件的数量和总字节数
func (a *ApiCall) ReportTaskProgress(task *models.Task, logger *zap.Logger) bool {
	workersInfo := make([]*msp.WorkerInfo, 0)
	agentInfos := task.AgentIpTaskResultMap
	// work := scheduler.GetWorkerAdmin()
	f := func(k, v interface{}) bool {
		agentIp := k.(string)
		agentTaskResult := v.(*models.AgentTaskResult)
		// agentWorkerInfo := work.GetAgentWorkerInfo(agentIp, logger)

		speedLimit := int64(conf.ListerConfig.GlobalConf.SpeedPodUnit)
		if v, ok := task.WorkSpeedInfo.Load(agentIp); ok {
			wi := v.(*models.WorkerSpeedInfo)
			speedLimit = wi.SpeedLimit
		}
		speedLimit = speedLimit / 1000 / 1000 // Mbps

		agentStatus := 1
		//if agentWorkerInfo != nil && agentWorkerInfo.IsAlive() {
		//	agentStatus = 1
		//}

		w := &msp.WorkerInfo{
			Ip:                   agentIp,
			Port:                 "0", // TODO
			AgentStatus:          int32(agentStatus),
			LimitSpeed:           int64(speedLimit),
			IsNew:                0,
			FileSuccessNumTotal:  agentTaskResult.BaseInfo.SuccessfulFileNum.Load(),
			FileSuccessSizeTotal: agentTaskResult.BaseInfo.SuccessfulSize.Load(),
			FileFailNumTotal:     agentTaskResult.BaseInfo.FailedFileNum.Load(),
			FileFailSizeTotal:    agentTaskResult.BaseInfo.FailedSize.Load(),
		}
		workersInfo = append(workersInfo, w)
		return true
	}
	agentInfos.Range(f)
	//	调用云平接口上报
	appLogger := pl_boot.AppCtx.GetAppLogger().With(zap.String("jobId", task.Id))
	appLogger.Info(
		"ReportTaskProgress",
		zap.Int64("SuccessfulFileNum", task.SuccessfulFileNum.Load()),
		zap.Int64("SuccessfulSize", task.SuccessfulSize.Load()),
		zap.Int64("ExistFileSuccessNum", task.ExistFileSuccessNum.Load()),
		zap.Int64("FailedFileNum", task.FailedFileNum.Load()),
		zap.Int64("FailedSize", task.FailedSize.Load()),
		zap.Int64("ExistFileSuccessSize", task.ExistFileSuccessSize.Load()))

	request := tools.NewMAReportJobProgressRequest()
	request.JobId = &task.Id
	fileSuccessNumTotal := task.SuccessfulFileNum.Load() + task.ExistFileSuccessNum.Load()
	fileSuccessSizeTotal := task.SuccessfulSize.Load() + task.ExistFileSuccessSize.Load()
	fileFailNumTotal := task.FailedFileNum.Load()
	fileFailSizeTotal := task.FailedSize.Load()
	request.Summary = &tools.WorkerSummary{
		FileSuccessNumTotal:  &fileSuccessNumTotal,
		FileSuccessSizeTotal: &fileSuccessSizeTotal,
		FileFailNumTotal:     &fileFailNumTotal,
		FileFailSizeTotal:    &fileFailSizeTotal,
	}
	request.WorkersInfo = workersInfo
	// 统计TP性能
	startTime := time.Now()
	// mspServer MAReportJobProgress 接口TP99 耗时 70ms以下，考虑并发这里需要休息70ms
	// TODO: 这个接口先读后写, 是否改成加锁更好?
	time.Sleep(50 * time.Millisecond)
	// 出错则重试
	for {
		resp, err := a.Client.MAReportJobProgress(request)
		pl_prom.GetHistogramWithValues(metrics.RpcHistogram, metrics.Lister, "ReportTaskResultUploadState", task.Id, "").
			Observe(float64(time.Now().Sub(startTime).Microseconds()))
		if err != nil {
			appLogger.Error(
				"MAReportJobProgress err", zap.Any("req", request), zap.Error(err))
			// 失败上报
			pl_prom.GetCounterWithValues(metrics.ErrorCount, metrics.Lister, "GetStatus", task.Id, "")
			time.Sleep(RetryInterval)
			continue
		}
		if resp != nil {
			break
		}
		time.Sleep(RetryInterval)
	}

	if conf.ListerConfig.ListerInfo.Mode == conf.OuterMode {
		// mspServer MAReportJobHeart 更新 worker 节点数据
		if len(workersInfo) > 0 {
			request := tools.NewMAReportJobHeartRequest()
			request.JobId = &task.Id
			request.WorkersInfo = workersInfo
			_, err := a.Client.MAReportJobHeart(request)
			if err != nil {
				appLogger.Error(
					"MAReportJobHeart err", zap.Any("req", request), zap.Error(err))
			}
		}
	}
	return true
}

// ReportTaskResultUploadState 上报 `任务失败文件上传到 COS` 的状态
// 失败的文件会上传到 COS
func (a *ApiCall) ReportTaskResultUploadState(taskId string, taskResultUploadState string) bool {
	request := tools.NewMAReportJobResultStoreStatusRequest()
	request.JobId = taskId
	request.Status = taskResultUploadState
	for count := 0; count < 2; count++ {
		// 统计TP性能
		startTime := time.Now()
		res, err := a.Client.MAReportJobResultStoreStatus(request)
		pl_prom.GetHistogramWithValues(metrics.RpcHistogram, metrics.Lister, "ReportTaskResultUploadState", taskId, "").
			Observe(float64(time.Now().Sub(startTime).Microseconds()))
		if err != nil {
			pl_boot.AppCtx.GetAppLogger().Error("ReportTaskResultUploadState err. retry times", zap.Int("count", count))
			count++
		} else {
			pl_boot.AppCtx.GetAppLogger().Info("ReportTaskResultUploadState success", zap.Any("resp", res))
			return true
		}
	}
	return false
}

// ReportTaskState 上报任务状态
// 注意状态和进度是不同概念
func (a *ApiCall) ReportTaskState(taskId string, taskState string) (bool, error) {
	logger := pl_boot.AppCtx.GetAppLogger().With(zap.String("jobId", taskId))
	logger.Info("ReportTaskState request", zap.String("taskState", taskState))

	request := tools.NewMAReportJobStatusRequest()
	request.JobId = &taskId
	request.Status = &taskState
	logger.Info("MAReportJobStatus ", zap.Any("req", request))

	// 统计TP性能
	startTime := time.Now()
	_, err := a.Client.MAReportJobStatus(request)
	pl_prom.GetHistogramWithValues(metrics.RpcHistogram, metrics.Lister, "ReportTaskState", taskId, "").
		Observe(float64(time.Now().Sub(startTime).Microseconds()))
	if _, ok := err.(*errors.TencentCloudSDKError); ok {
		logger.Error(
			"reportJobStatus MAReportJobStatus err", zap.Any("req", request), zap.Error(err))
		pl_prom.GetCounterWithValues(metrics.ErrorCount, metrics.Lister, "ReportTaskState", taskId, "")
		return false, err
	}
	if err != nil {
		logger.Error("reportJobStatus MAReportJobStatus err", zap.Error(err))
		pl_prom.GetCounterWithValues(metrics.ErrorCount, metrics.Lister, "ReportTaskState", taskId, "")
		return false, err
	}
	return true, nil
}

// GetTask 拉取指定任务
func (a *ApiCall) GetTask(taskId string) (*tools.JobInfo, error) {
	logger := pl_boot.AppCtx.GetAppLogger().With(zap.String("jobId", taskId))

	request := tools.NewMAListMigrationJobsV2Request()
	request.JobId = &taskId
	logger.Info("GetTask MAReportJobStatus", zap.Any("req", request))

	// 统计TP性能
	startTime := time.Now()
	response, err := a.Client.MAListMigrationJobsV2(request)
	pl_prom.GetHistogramWithValues(metrics.RpcHistogram, metrics.Lister, "GetTask", taskId, "").
		Observe(float64(time.Now().Sub(startTime).Microseconds()))
	if _, ok := err.(*errors.TencentCloudSDKError); ok {
		logger.Error(
			"reportJobStatus MAReportJobStatus err", zap.Any("req", request), zap.Error(err))
		pl_prom.GetCounterWithValues(metrics.ErrorCount, metrics.Lister, "GetTask", taskId, "")
		return nil, err
	}
	if err != nil {
		logger.Error("listMigrationJobsV2 MAListMigrationJobsV2 err", zap.Error(err))
		pl_prom.GetCounterWithValues(metrics.ErrorCount, metrics.Lister, "GetTask", taskId, "")
		return nil, err
	}
	logger.Info("GetTask MAReportJobStatus", zap.Any("response", response))
	if len(response.Response.JobsInfo) == 0 {
		return nil, nil
	}

	// FIXME: 正式上线后删除
	for _, job := range response.Response.JobsInfo {
		if *job.JobID == taskId {
			return job, nil
		}
	}

	return nil, nil
}

// GetStatus 查询指定任务的状态
func (a *ApiCall) GetStatus(taskId string) (api.TaskState, error) {
	logger := pl_boot.AppCtx.GetAppLogger().With(zap.String("jobId", taskId))

	request := tools.NewMADescribeJobInfoRequest()
	request.JobId = &taskId
	logger.Info("MADescribeJobInfo ", zap.Any("req", request))
	// 统计TP性能
	startTime := time.Now()
	response, err := a.Client.MADescribeJobInfo(request)
	pl_prom.GetHistogramWithValues(metrics.RpcHistogram, metrics.Lister, "GetStatus", taskId, "").
		Observe(float64(time.Now().Sub(startTime).Microseconds()))
	if _, ok := err.(*errors.TencentCloudSDKError); ok {
		logger.Error(
			"GetStatus MADescribeJobInfo err", zap.Any("req", request), zap.Error(err))
		// 失败上报
		pl_prom.GetCounterWithValues(metrics.ErrorCount, metrics.Lister, "GetStatus", taskId, "")
		return api.TaskState_ABSTAIN_STATUS, err
	}
	if err != nil {
		logger.Error("GetStatus MADescribeJobInfo err", zap.Error(err))
		// 失败上报
		pl_prom.GetCounterWithValues(metrics.ErrorCount, metrics.Lister, "GetStatus", taskId, "")
		return api.TaskState_ABSTAIN_STATUS, err
	}
	logger.Info("GetStatus MADescribeJobInfo", zap.Any("response", response))

	info := response.Response
	if info == nil {
		return api.TaskState_ABSTAIN_STATUS, nil
	}

	state := info.JobInfo.Status
	return api.TaskState(tools.StrConInt32(*state)), nil
}

// ReportJobSliceInfo 上报存储遍历(listing)的结果
// NOTE: 如果是重试任务, 计数应该合并 ExistingFile
func (a *ApiCall) ReportJobSliceInfo(
	taskId, jobName string, pidNum int64, fileNum, size int64,
) error {

	logger := pl_boot.AppCtx.GetAppLogger().With(zap.String("jobId", taskId))
	logger.Info(
		"ApiCall.ReportJobSliceInfo",
		zap.Int64("pidNum", pidNum),
		zap.Int64("fileNum", fileNum),
		zap.Int64("size", size))

	request := tools.NewMAReportJobSliceInfoRequest()
	request.JobId = &taskId
	request.JobName = jobName
	request.PidNum = strconv.FormatInt(pidNum, 10)
	request.FileNum = strconv.FormatInt(fileNum, 10)
	request.Size = strconv.FormatInt(size, 10)

	// 统计TP性能
	startTime := time.Now()
	// 出错则重试
	for {
		response, err := a.Client.MAReportJobSliceInfo(request)
		pl_prom.GetHistogramWithValues(metrics.RpcHistogram, metrics.Lister, "GetStatus", taskId, "").
			Observe(float64(time.Now().Sub(startTime).Microseconds()))
		if _, ok := err.(*errors.TencentCloudSDKError); ok {
			logger.Error(
				"GetStatus MAReportJobSliceInfo err", zap.Any("req", request), zap.Error(err))
			// 失败上报
			pl_prom.GetCounterWithValues(metrics.ErrorCount, metrics.Lister, "GetStatus", taskId, "")
			time.Sleep(RetryInterval)
			continue
		}
		if err != nil {
			logger.Error("GetStatus MAReportJobSliceInfo err", zap.Error(err))
			// 失败上报
			pl_prom.GetCounterWithValues(metrics.ErrorCount, metrics.Lister, "GetStatus", taskId, "")
			time.Sleep(RetryInterval)
			continue
		}
		logger.Info("GetStatus MAReportJobSliceInfo", zap.Any("response", response))
		break
	}
	return nil
}

// 遍历过程中上报计数
func (a *ApiCall) ReportJobScanInfo(
	taskId string, fitNum, scanNum int64,
) error {

	logger := pl_boot.AppCtx.GetAppLogger().With(zap.String("jobId", taskId))
	logger.Info(
		"ApiCall.ReportJobScanInfo",
		zap.Int64("fitNum", fitNum),
		zap.Int64("scanNum", scanNum))

	request := tools.NewMAReportJobScanInfoReq()
	request.JobId = taskId
	request.FitNum = strconv.FormatInt(fitNum, 10)
	request.ScanNum = strconv.FormatInt(scanNum, 10)

	// 统计TP性能
	startTime := time.Now()
	// 出错则重试
	for {
		response, err := a.Client.MAReportJobScanInfo(request)
		pl_prom.GetHistogramWithValues(metrics.RpcHistogram, metrics.Lister, "GetStatus", taskId, "").
			Observe(float64(time.Now().Sub(startTime).Microseconds()))
		if _, ok := err.(*errors.TencentCloudSDKError); ok {
			logger.Error(
				"GetStatus MAReportJobScanInfo err", zap.Any("req", request), zap.Error(err))
			// 失败上报
			pl_prom.GetCounterWithValues(metrics.ErrorCount, metrics.Lister, "GetStatus", taskId, "")
			time.Sleep(RetryInterval)
			continue
		}
		if err != nil {
			logger.Error("GetStatus MAReportJobScanInfo err", zap.Error(err))
			// 失败上报
			pl_prom.GetCounterWithValues(metrics.ErrorCount, metrics.Lister, "GetStatus", taskId, "")
			time.Sleep(RetryInterval)
			continue
		}
		logger.Info("GetStatus MAReportJobScanInfo", zap.Any("response", response))
		break
	}
	return nil
}

// ReportJobStopReason 上报任务停止执行的原因
func (a *ApiCall) ReportJobStopReason(taskId, stopReason string) error {
	logger := pl_boot.AppCtx.GetAppLogger().With(zap.String("jobId", taskId))
	logger.Info("ApiCall.ReportJobStopReason", zap.String("stopReason", stopReason))

	request := tools.NewReportJobStopReasonReq()
	request.JobId = &taskId
	request.StopReason = &stopReason
	logger.Info("ReportJobStopReason ", zap.Any("req", request))
	// 统计TP性能
	startTime := time.Now()
	response, err := a.Client.ReportJobStopReason(request)
	pl_prom.GetHistogramWithValues(metrics.RpcHistogram, metrics.Lister, "ReportJobStopReason", taskId, "").
		Observe(float64(time.Now().Sub(startTime).Microseconds()))
	if _, ok := err.(*errors.TencentCloudSDKError); ok {
		logger.Error(
			"ReportJobStopReason err", zap.Any("req", request), zap.Error(err))
		// 失败上报
		pl_prom.GetCounterWithValues(metrics.ErrorCount, metrics.Lister, "ReportJobStopReason", taskId, "")
		return err
	}
	if err != nil {
		logger.Error("ReportJobStopReason err", zap.Error(err))
		// 失败上报
		pl_prom.GetCounterWithValues(metrics.ErrorCount, metrics.Lister, "ReportJobStopReason", taskId, "")
		return err
	}
	logger.Info("ReportJobStopReason", zap.Any("response", response))
	return nil
}

// DescribeTmpSecret 获取临时密钥
func (a *ApiCall) DescribeTmpSecret(taskId string) (*tools.DescribeTmpSecretResp, error) {
	logger := pl_boot.AppCtx.GetAppLogger().With(zap.String("jobId", taskId))

	request := tools.NewDescribeTmpSecretReq()
	request.JobId = &taskId
	logger.Info("NewDescribeTmpSecretReq", zap.Any("req", request))
	// 统计TP性能
	startTime := time.Now()
	response, err := a.Client.DescribeTmpSecret(request)
	pl_prom.GetHistogramWithValues(metrics.RpcHistogram, metrics.Lister, "GetStatus", taskId, "").
		Observe(float64(time.Now().Sub(startTime).Microseconds()))
	if _, ok := err.(*errors.TencentCloudSDKError); ok {
		logger.Error(
			"GetStatus DescribeTmpSecret err", zap.Any("req", request), zap.Error(err))
		// 失败上报
		pl_prom.GetCounterWithValues(metrics.ErrorCount, metrics.Lister, "GetStatus", taskId, "")
		return nil, err
	}
	if err != nil {
		logger.Error("GetStatus DescribeTmpSecret err", zap.Error(err))
		// 失败上报
		pl_prom.GetCounterWithValues(metrics.ErrorCount, metrics.Lister, "GetStatus", taskId, "")
		return nil, err
	}
	logger.Info("GetStatus DescribeTmpSecret", zap.Any("response", response))
	return response, nil
}

// DescribeTmpSecret 获取临时密钥
func (a *ApiCall) DescribeAccountInfo() (*tools.DescribeAccountInfoResp, error) {
	request := tools.NewDescribeAccountInfoReq()
	logger := pl_boot.AppCtx.GetAppLogger()
	// 统计TP性能
	response, err := a.Client.DescribeAccountInfo(request)

	if _, ok := err.(*errors.TencentCloudSDKError); ok {
		logger.Error(
			"GetStatus DescribeAccountInfo err", zap.Any("req", request), zap.Error(err))
		return nil, err
	}
	if err != nil {
		logger.Error("DescribeAccountInfo err", zap.Error(err))
		return nil, err
	}
	logger.Info("GetStatus DescribeAccountInfo", zap.Any("response", response))
	return response, nil
}
