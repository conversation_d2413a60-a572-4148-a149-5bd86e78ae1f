package apicall

import (
	"git.code.oa.com/cloud-msp/migration-lister/api"
	"git.code.oa.com/cloud-msp/migration-lister/internal/service/models"
	"git.code.oa.com/cloud-msp/migration-lister/tools"
	"go.uber.org/zap"
)

// ApiCall 接口类型, 为了方便单元测试
type ApiCallIf interface {
	GetStatus(taskId string) (api.TaskState, error)
	ReportTaskResultUploadState(taskId string, taskResultUploadState string) bool
	ReportTaskProgress(task *models.Task, logger *zap.Logger) bool
	ReportJobScanInfo(taskId string, fitNum, scanNum int64) error
	ReportJobSliceInfo(
		taskId, jobName string, pidNum int64, fileNum, size int64,
	) error
	ReportJobStopReason(taskId, stopReason string) error
	ReportTaskState(taskId string, taskState string) (bool, error)
	GetTask(taskId string) (*tools.JobInfo, error)
	DescribeTmpSecret(taskId string) (*tools.DescribeTmpSecretResp, error)
	DescribeAccountInfo() (*tools.DescribeAccountInfoResp, error)
}
