package service

import (
	"context"

	"git.code.oa.com/cloud-msp/migration-lister/api"
	"git.code.oa.com/cloud-msp/migration-lister/internal/service/apicall"
	"git.code.oa.com/cloud-msp/migration-lister/internal/service/models"
	"git.code.oa.com/cloud-msp/migration-lister/internal/service/scheduler"
	"git.code.oa.com/cloud-msp/migration-lister/internal/service/tasks"
	"git.code.oa.com/pulse-line/pl_boot"
	"git.code.oa.com/pulse-line/pl_qcloudapi_v3/qcloud/apiv3"
	v3 "git.code.oa.com/pulse-line/pl_qcloudapi_v3/qcloud/v3"
	"github.com/pkg/errors"
	"go.uber.org/zap"
)

/**
 *对应Python：cos_migrate_tool agent类
 */
type MigrationServiceImpl struct {
}

// NewMigrationService 构造 MigrationServiceServer 接口实例
func NewMigrationService() api.MigrationServiceServer {
	return &MigrationServiceImpl{}
}

/**
 *对应Python：cos_migrate_tool _receive_worker 方法
 */
func (service *MigrationServiceImpl) ReceiveWork(
	ctx context.Context, request *api.ReceiveWorkRequest,
) (*api.ReceiveWorkResponse, error) {

	// logger := ctxzap.ExtractLogger(ctx)
	logger := pl_boot.AppCtx.GetAppLogger()
	resp := new(api.ReceiveWorkResponse)
	resp.Res = false
	work := request.Work

	logger.Info("ReceiveWork request", zap.Any("work", work))

	sch := scheduler.GetWorkerAdmin()
	sch.UpdateWorker(work.WorkerIp, int(work.WorkerPort), work.BaseInfo, work.TaskInfo,
		work.IsStke, work.Region,
	)
	taskAdmin := tasks.GetTaskAdmin()
	// work TaskAndFragIdSet rpc 结构体定义一个数组 {"taskId,fragId"}
	for _, v := range work.TaskInfo.FragIdSet {
		taskAdmin.UpdateFragHeart(work.TaskInfo.TaskId, v, logger)
	}
	resp.Res = true
	return resp, nil
}

// 对应Python：cos_migrate_tool _receive_frag_result 方法
func (service *MigrationServiceImpl) ReceiveFragResult(
	ctx context.Context, request *api.ReceiveFragResultRequest) (*api.ReceiveFragResultResponse, error) {

	// logger := ctxzap.ExtractLogger(ctx)
	logger := pl_boot.AppCtx.GetAppLogger()
	resp := new(api.ReceiveFragResultResponse)
	taskResult := request.FragResult
	if nil == taskResult {
		return nil, v3.Error(apiv3.ErrCode_InvalidParameter, "taskResult cant empty")
	}
	if taskResult.TaskId == "" {
		return nil, v3.Error(apiv3.ErrCode_InvalidParameter, "taskId cant empty")
	}

	logger.Info(
		"ReceiveFragResult request param",
		zap.Any("fragId", request.FragResult.FragId),
		zap.String("taskId", request.FragResult.TaskId),
		zap.Int64("RetryNum", request.FragResult.RetryNum),
		zap.Int64("WaitMigrateNum", taskResult.WaitMigrateNum),
		zap.Int64("FailedSize", taskResult.FailedSize),
		zap.Int64("FailedNum", taskResult.FailedNum),
		zap.Int64("SuccessfulNum", taskResult.SuccessfulNum),
		zap.Int64("SuccessfulSize", taskResult.SuccessfulSize),
		zap.Int64("FailedNum", taskResult.FailedNum))
	// 获取全局task admin
	admin := tasks.GetTaskAdmin()
	result := models.ConvertToFragResult(request.FragResult)
	logger.Info(
		"ReceiveFragResult.ConvertToFragResult",
		zap.Any("fragId", request.FragResult.FragId),
		zap.String("taskId", request.FragResult.TaskId),
		zap.Any("request.SuccessfulNum", request.FragResult.SuccessfulNum),
		zap.Any("request.SuccessfulSize", request.FragResult.SuccessfulSize),
		zap.Any("result.SuccessfulSize", result.BaseInfo.SuccessfulSize),
		zap.Any("result.SuccessfulNum", result.BaseInfo.SuccessfulNum))
	res := admin.AddFragResult(result, logger)
	resp.Res = res
	return resp, nil
}

/**
 *对应Python：cos_migrate_tool _receive_task_state_query 方法
 */
func (service *MigrationServiceImpl) ReceiveTaskState(ctx context.Context,
	request *api.ReceiveTaskStateRequest) (*api.ReceiveTaskStateResponse, error) {
	resp := new(api.ReceiveTaskStateResponse)
	if request.TaskId == "" {
		return nil, v3.Error(apiv3.ErrCode_InvalidParameter, "JobId cant empty")
	}
	// logger := ctxzap.ExtractLogger(ctx)
	logger := pl_boot.AppCtx.GetAppLogger().With(zap.String("taskId", request.TaskId))
	logger.Info("ReceiveTaskState request",
		zap.Any("req", request))

	state := service.queryTaskState(request.TaskId, logger)
	qpsLimit := -1
	task := tasks.GetTaskAdmin().GetTask(request.TaskId, logger)
	if task != nil {
		qpsLimit = int(task.QpsLimit)
	}
	resp.QpsLimit = int64(qpsLimit)
	resp.TaskState = state
	logger.Info("ReceiveTaskState response", zap.Any("resp", resp))
	return resp, nil
}

/**
 *对应Python：cos_migrate_tool _receive_task_qps_token_consume  方法
 */
func (service *MigrationServiceImpl) ReceiveTaskQpsConsume(ctx context.Context,
	request *api.ReceiveTaskQpsConsumeRequest) (*api.ReceiveTaskQpsConsumeResponse, error) {
	resp := new(api.ReceiveTaskQpsConsumeResponse)
	if request.TaskId == "" {
		return nil, v3.Error(apiv3.ErrCode_InvalidParameter, "TaskId cant empty")
	}
	logger := pl_boot.AppCtx.GetAppLogger().With(zap.String("taskId", request.TaskId))
	logger.Info("ReceiveTaskQpsConsume request",
		zap.Any("req", request))
	token := service.consumeTaskQpsToken(request.TaskId, int32(request.NeedQpsToken))
	resp.TaskQpsLimit = int64(token)
	logger.Info("ReceiveTaskQpsConsume response",
		zap.Any("resp", resp))
	return resp, nil
}

/**
 *对应Python：cos_migrate_tool _receive_task_flow_token_consume 方法
 */
func (service *MigrationServiceImpl) ReceiveTaskFlowConsume(ctx context.Context,
	request *api.ReceiveTaskFlowConsumeRequest) (*api.ReceiveTaskFlowConsumeResponse, error) {
	if request.TaskId == "" {
		return nil, v3.Error(apiv3.ErrCode_InvalidParameter, "TaskId cant empty")
	}
	// logger := ctxzap.ExtractLogger(ctx)
	logger := pl_boot.AppCtx.GetAppLogger()
	logger.Info("ReceiveTaskFlowConsume request param", zap.Any("TaskId", request.TaskId),
		zap.Int64("NeedFlowToken", request.NeedFlowToken))
	resp := new(api.ReceiveTaskFlowConsumeResponse)
	token := service.consumeTaskFlowToken(request.TaskId, int32(request.NeedFlowToken))
	resp.TaskFlowLimit = int64(token)
	return resp, nil
}

// ReceiveTaskInfo
func (service *MigrationServiceImpl) ReceiveTaskInfo(ctx context.Context,
	request *api.ReceiveTaskInfoRequest) (*api.ReceiveTaskInfoResponse, error) {
	if request.TaskId == "" {
		return nil, v3.Error(apiv3.ErrCode_InvalidParameter, "TaskId cant empty")
	}
	var (
		resp          = &api.ReceiveTaskInfoResponse{}
		listerService = tasks.GetTaskAdmin()
	)
	logger := pl_boot.AppCtx.GetAppLogger().With(zap.String("taskId", request.TaskId))
	logger.Info("ReceiveTaskInfo request",
		zap.Any("req", request))
	task := listerService.GetTask(request.TaskId, logger)
	if task == nil {
		logger.Warn("ReceiveTaskInfo task is nil")
		return nil, errors.New("no running task")
	}
	resp.JobInfo = &api.JobInfo{}
	listerService.FillJobInfo(resp.JobInfo, task)
	return resp, nil
}

// ReceiveJobNextSpeed
func (service *MigrationServiceImpl) ReceiveJobNextSpeed(ctx context.Context,
	request *api.NextSpeedRequest) (*api.NextSpeedResponse, error) {
	var ()
	logger := pl_boot.AppCtx.GetAppLogger().With(zap.String("taskId", request.JobId))
	logger.Info("ReceiveJobNextSpeed request",
		zap.Any("req", request))
	ns, err := scheduler.SpeedAdminV2.NextSpeed(request)
	if err != nil {
		logger.Error("ReceiveJobNextSpeed err", zap.Error(err), zap.Any("request", request))
		return nil, err
	}

	task := tasks.GetTaskAdmin().GetTask(request.JobId, logger)
	task.WorkSpeedInfo.Store(request.WorkerIp, &models.WorkerSpeedInfo{
		SpeedInUse: request.RealSpeed,
		SpeedLimit: request.AllocatedSpeed,
	})
	resp := &api.NextSpeedResponse{
		NextSpeed: ns,
	}
	logger.Info("ReceiveJobNextSpeed response", zap.Any("resp", resp))

	return resp, err
}

// ReceiveExecutedJobInfo
func (service *MigrationServiceImpl) ReceiveExecutedJobInfo(ctx context.Context,
	request *api.ReceiveExecutedJobInfoRequest) (*api.ReceiveExecutedJobInfoResponse, error) {
	var (
		logger = pl_boot.AppCtx.GetAppLogger()
	)
	logger.Info("ReceiveExecutedJobInfo request",
		zap.Any("req", request))
	info, err := scheduler.SpeedAdminV2.ExecutedJobInfo(request)
	if err != nil {
		logger.Error("ReceiveExecutedJobInfo err", zap.Error(err), zap.Any("request", request))
		return nil, err
	}
	resp := &api.ReceiveExecutedJobInfoResponse{
		NewJobs: info,
	}
	logger.Info("ReceiveExecutedJobInfo response", zap.Any("resp", resp))
	return resp, err
}

// ReceiveFrag
func (service *MigrationServiceImpl) ReceiveFrag(ctx context.Context,
	request *api.ReceiveFragRequest) (*api.ReceiveFragResponse, error) {
	logger := pl_boot.AppCtx.GetAppLogger().With(zap.Any("taskId", request.JobId))
	logger.Info("ReceiveFrag request",
		zap.Any("req", request))
	frag, err := scheduler.Sch.ConsumerFragByApi(ctx, request.JobId)
	if err != nil {
		logger.Error("ReceiveFrag err", zap.Error(err), zap.Any("request", request))
		return nil, err
	}
	resp := &api.ReceiveFragResponse{
		Frag: frag,
	}
	logger.Info("ReceiveFrag response", zap.Any("resp", resp))
	return resp, err
}

// queryTaskState 获取指定任务状态
func (service *MigrationServiceImpl) queryTaskState(taskId string, logger *zap.Logger) api.TaskState {
	task := tasks.GetTaskAdmin().GetTask(taskId, logger)
	if task == nil {
		logger.Info("queryTaskState task is nil, invoke api")
		// 调用远程获取状态
		taskState, err := apicall.GetApiCall().GetStatus(taskId)
		if nil != err {
			logger.Error("queryTaskState err", zap.Error(err))
			return api.TaskState_UNKNOWN
		}
		return taskState
	}
	return task.GetState()
}

func (service *MigrationServiceImpl) consumeTaskQpsToken(taskId string, needQpsToken int32) int32 {
	logger := pl_boot.AppCtx.GetAppLogger().With(zap.String("jobId", taskId))
	logger.Info("consumeTaskQpsTokenReq", zap.Any("needQpsToken", needQpsToken))
	result := tasks.ConsumeToken(taskId, int(needQpsToken))
	defer func() {
		logger.Info("consumeTaskQpsTokenResp",
			zap.Any("needQpsToken", needQpsToken),
			zap.Any("result", result),
		)
	}()
	return int32(result)
}

func (service *MigrationServiceImpl) consumeTaskFlowToken(taskId string, needFlowToken int32) int32 {
	logger := pl_boot.AppCtx.GetAppLogger().With(zap.String("jobId", taskId))
	logger.Info("consumeTaskFlowTokenReq", zap.Any("needQpsToken", needFlowToken))
	result := needFlowToken
	defer func() {
		logger.Info("consumeTaskFlowTokenResp",
			zap.Any("needQpsToken", needFlowToken),
			zap.Any("result", result),
		)
	}()
	return result
}
