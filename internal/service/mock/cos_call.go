// Code generated by MockGen. DO NOT EDIT.
// Source: git.code.oa.com/cloud-msp/migration-lister/internal/service/coscall (interfaces: MspCosCallIF)

// Package mock is a generated GoMock package.
package mock

import (
	api "git.code.oa.com/cloud-msp/migration-lister/api"
	gomock "github.com/golang/mock/gomock"
	reflect "reflect"
)

// MockMspCosCallIF is a mock of MspCosCallIF interface.
type MockMspCosCallIF struct {
	ctrl     *gomock.Controller
	recorder *MockMspCosCallIFMockRecorder
}

// MockMspCosCallIFMockRecorder is the mock recorder for MockMspCosCallIF.
type MockMspCosCallIFMockRecorder struct {
	mock *MockMspCosCallIF
}

// NewMockMspCosCallIF creates a new mock instance.
func NewMockMspCosCallIF(ctrl *gomock.Controller) *MockMspCosCallIF {
	mock := &MockMspCosCallIF{ctrl: ctrl}
	mock.recorder = &MockMspCosCallIFMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockMspCosCallIF) EXPECT() *MockMspCosCallIFMockRecorder {
	return m.recorder
}

// EndReportFailedFiles mocks base method.
func (m *MockMspCosCallIF) EndReportFailedFiles(arg0 string) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "EndReportFailedFiles", arg0)
}

// EndReportFailedFiles indicates an expected call of EndReportFailedFiles.
func (mr *MockMspCosCallIFMockRecorder) EndReportFailedFiles(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "EndReportFailedFiles", reflect.TypeOf((*MockMspCosCallIF)(nil).EndReportFailedFiles), arg0)
}

// ReportFailedFiles mocks base method.
func (m *MockMspCosCallIF) ReportFailedFiles(arg0 *api.FragResult) bool {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ReportFailedFiles", arg0)
	ret0, _ := ret[0].(bool)
	return ret0
}

// ReportFailedFiles indicates an expected call of ReportFailedFiles.
func (mr *MockMspCosCallIFMockRecorder) ReportFailedFiles(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ReportFailedFiles", reflect.TypeOf((*MockMspCosCallIF)(nil).ReportFailedFiles), arg0)
}
