// Code generated by MockGen. DO NOT EDIT.
// Source: git.code.oa.com/cloud-msp/migration-lister/internal/service/apicall (interfaces: ApiCallIf)

// Package mock is a generated GoMock package.
package mock

import (
	api "git.code.oa.com/cloud-msp/migration-lister/api"
	tasks "git.code.oa.com/cloud-msp/migration-lister/internal/service/models"
	tools "git.code.oa.com/cloud-msp/migration-lister/tools"
	gomock "github.com/golang/mock/gomock"
	zap "go.uber.org/zap"
	reflect "reflect"
)

// MockApiCallIf is a mock of ApiCallIf interface.
type MockApiCallIf struct {
	ctrl     *gomock.Controller
	recorder *MockApiCallIfMockRecorder
}

func (m *MockApiCallIf) DescribeAccountInfo() (*tools.DescribeAccountInfoResp, error) {
	panic("implement me")
}

// MockApiCallIfMockRecorder is the mock recorder for MockApiCallIf.
type MockApiCallIfMockRecorder struct {
	mock *MockApiCallIf
}

// NewMockApiCallIf creates a new mock instance.
func NewMockApiCallIf(ctrl *gomock.Controller) *MockApiCallIf {
	mock := &MockApiCallIf{ctrl: ctrl}
	mock.recorder = &MockApiCallIfMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockApiCallIf) EXPECT() *MockApiCallIfMockRecorder {
	return m.recorder
}

// DescribeTmpSecret mocks base method.
func (m *MockApiCallIf) DescribeTmpSecret(arg0 string) (*tools.DescribeTmpSecretResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DescribeTmpSecret", arg0)
	ret0, _ := ret[0].(*tools.DescribeTmpSecretResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DescribeTmpSecret indicates an expected call of DescribeTmpSecret.
func (mr *MockApiCallIfMockRecorder) DescribeTmpSecret(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DescribeTmpSecret", reflect.TypeOf((*MockApiCallIf)(nil).DescribeTmpSecret), arg0)
}

// GetStatus mocks base method.
func (m *MockApiCallIf) GetStatus(arg0 string) (api.TaskState, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetStatus", arg0)
	ret0, _ := ret[0].(api.TaskState)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetStatus indicates an expected call of GetStatus.
func (mr *MockApiCallIfMockRecorder) GetStatus(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetStatus", reflect.TypeOf((*MockApiCallIf)(nil).GetStatus), arg0)
}

// GetTask mocks base method.
func (m *MockApiCallIf) GetTask(arg0 string) (*tools.JobInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTask", arg0)
	ret0, _ := ret[0].(*tools.JobInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTask indicates an expected call of GetTask.
func (mr *MockApiCallIfMockRecorder) GetTask(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTask", reflect.TypeOf((*MockApiCallIf)(nil).GetTask), arg0)
}

// ReportJobScanInfo mocks base method.
func (m *MockApiCallIf) ReportJobScanInfo(arg0 string, arg1, arg2 int64) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ReportJobScanInfo", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// ReportJobScanInfo indicates an expected call of ReportJobScanInfo.
func (mr *MockApiCallIfMockRecorder) ReportJobScanInfo(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ReportJobScanInfo", reflect.TypeOf((*MockApiCallIf)(nil).ReportJobScanInfo), arg0, arg1, arg2)
}

// ReportJobSliceInfo mocks base method.
func (m *MockApiCallIf) ReportJobSliceInfo(arg0, arg1 string, arg2, arg3, arg4 int64) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ReportJobSliceInfo", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(error)
	return ret0
}

// ReportJobSliceInfo indicates an expected call of ReportJobSliceInfo.
func (mr *MockApiCallIfMockRecorder) ReportJobSliceInfo(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ReportJobSliceInfo", reflect.TypeOf((*MockApiCallIf)(nil).ReportJobSliceInfo), arg0, arg1, arg2, arg3, arg4)
}

// ReportJobStopReason mocks base method.
func (m *MockApiCallIf) ReportJobStopReason(arg0, arg1 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ReportJobStopReason", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// ReportJobStopReason indicates an expected call of ReportJobStopReason.
func (mr *MockApiCallIfMockRecorder) ReportJobStopReason(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ReportJobStopReason", reflect.TypeOf((*MockApiCallIf)(nil).ReportJobStopReason), arg0, arg1)
}

// ReportTaskProgress mocks base method.
func (m *MockApiCallIf) ReportTaskProgress(arg0 *tasks.Task, arg1 *zap.Logger) bool {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ReportTaskProgress", arg0, arg1)
	ret0, _ := ret[0].(bool)
	return ret0
}

// ReportTaskProgress indicates an expected call of ReportTaskProgress.
func (mr *MockApiCallIfMockRecorder) ReportTaskProgress(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ReportTaskProgress", reflect.TypeOf((*MockApiCallIf)(nil).ReportTaskProgress), arg0, arg1)
}

// ReportTaskResultUploadState mocks base method.
func (m *MockApiCallIf) ReportTaskResultUploadState(arg0, arg1 string) bool {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ReportTaskResultUploadState", arg0, arg1)
	ret0, _ := ret[0].(bool)
	return ret0
}

// ReportTaskResultUploadState indicates an expected call of ReportTaskResultUploadState.
func (mr *MockApiCallIfMockRecorder) ReportTaskResultUploadState(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ReportTaskResultUploadState", reflect.TypeOf((*MockApiCallIf)(nil).ReportTaskResultUploadState), arg0, arg1)
}

// ReportTaskState mocks base method.
func (m *MockApiCallIf) ReportTaskState(arg0, arg1 string) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ReportTaskState", arg0, arg1)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ReportTaskState indicates an expected call of ReportTaskState.
func (mr *MockApiCallIfMockRecorder) ReportTaskState(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ReportTaskState", reflect.TypeOf((*MockApiCallIf)(nil).ReportTaskState), arg0, arg1)
}
