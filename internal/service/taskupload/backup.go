package taskupload

import (
	"sync"

	"go.uber.org/atomic"
)

// 任务失败文件列表分块上传断点备份
type UploadBackup struct {
	FailPathMap   map[string]string
	UploadInfoMap map[string]TaskUploadInfoBackup
}

// 失败文件列表分块上传断点备份
type taskUploadBackupIf interface {
	LoadUploadBackup(uploadBackup *UploadBackup)
	CreateUploadBackup(taskId string) UploadBackup
}

// 从 backup 对象加载任务的失败文件上传断点
// NOTE: 任务有可能不存在失败文件, 也就没有路径和断点
// TODO: 测试覆盖恢复的任务没有失败文件列表的情况
func (t *taskUploadServiceImpl) LoadUploadBackup(uploadBackup *UploadBackup) {
	for k, v := range uploadBackup.UploadInfoMap {
		backup := v
		info := backup2uploadInfo(backup)
		t.TaskIdToTaskUploadInfo.Store(k, info)
	}

	for k, v := range uploadBackup.FailPathMap {
		value := v
		t.TaskIdToFailedFilePath.Store(k, &value)
	}
	t.TaskUploadLockMap = sync.Map{}
}

// 单个任务失败文件列表路径和断点备份
func (t *taskUploadServiceImpl) CreateUploadBackup(taskId string) UploadBackup {
	var backup UploadBackup
	// 失败文件路径
	failFilePath := t.GetTaskToFailedPathById(taskId)
	if failFilePath != nil {
		backup.FailPathMap = map[string]string{taskId: *failFilePath}
	}

	// 失败文件上传断点
	unloadInfo := t.GetTaskUploadInfoById(taskId)
	if unloadInfo != nil {
		backup.UploadInfoMap = map[string]TaskUploadInfoBackup{
			taskId: uploadInfo2Backup(unloadInfo),
		}
	}
	return backup
}

func uploadInfo2Backup(uploadInfo *TaskUploadInfo) TaskUploadInfoBackup {
	var uploadInfoBackup = TaskUploadInfoBackup{}
	if uploadInfo != nil {
		uploadInfoBackup.UploadId = uploadInfo.UploadId
		uploadInfoBackup.PartNum = uploadInfo.PartNum.Load()
		uploadInfoBackup.PartsUpload = uploadInfo.PartsUpload
		uploadInfoBackup.AccumulatePart = uploadInfo.AccumulatePart.Load()
		uploadInfoBackup.Over = uploadInfo.Over.Load()
	}
	return uploadInfoBackup
}

func backup2uploadInfo(backup TaskUploadInfoBackup) *TaskUploadInfo {
	info := &TaskUploadInfo{
		UploadId:        backup.UploadId,
		PartNum:         atomic.NewInt32(backup.PartNum),
		PartsUpload:     backup.PartsUpload,
		AccumulatePart:  atomic.NewString(backup.AccumulatePart),
		Over:            atomic.NewBool(backup.Over),
		ChildUploadOver: atomic.NewBool(false),
	}
	return info
}
