package taskupload

import (
	"encoding/json"
	"sync"
	"time"

	"git.code.oa.com/cloud-msp/migration-lister/internal/service/models"
	"git.code.oa.com/cloud-msp/migration-lister/pkg/utils"
	"git.code.oa.com/pulse-line/pl_boot"
	"go.uber.org/atomic"
	"go.uber.org/zap"
)

var (
	uploadImpl *taskUploadServiceImpl
	once       sync.Once
)

// 任务失败文件列表上传管理
type TaskUploadService interface {
	GetTaskUploadInfoById(taskId string) *TaskUploadInfo
	GetTaskToFailedPathById(taskId string) *string
	GetTaskLockByTaskId(taskId string) *sync.Mutex
	PutTaskUploadInfo(taskId string, info *TaskUploadInfo)
	PutTaskUploadAndGet(taskId string, info *TaskUploadInfo) *TaskUploadInfo
	PutTaskIdFailedMap(taskId string, value *string)
	InitTaskUploadInfo() *TaskUploadInfo
	RemoveTaskInfoById(taskId string)
	RemoveTaskFailedPathById(taskId string)
	RemoveUploadCache(taskId string)
	RemoveUploadLock(taskId string)
	taskUploadBackupIf
}

func init() {
	once.Do(func() {
		uploadImpl = &taskUploadServiceImpl{
			TaskIdToFailedFilePath: sync.Map{},
			TaskIdToTaskUploadInfo: sync.Map{},
			TaskUploadLockMap:      sync.Map{},
		}
	})
}

// GetUploadService 单例模式获取 TaskUploadService 实例
func GetUploadService() TaskUploadService {
	return uploadImpl
}

// taskUploadServiceImpl 实现任务上报数据管理接口 TaskUploadService
type taskUploadServiceImpl struct {
	// {TaskId: TaskUploadInfo} map[string]*TaskUploadInfo
	TaskIdToTaskUploadInfo sync.Map
	// {TaskId: FailedFilePath} map[string]*string
	TaskIdToFailedFilePath sync.Map
	// {taskId:lock} map[string]sync.Mutex
	TaskUploadLockMap sync.Map
}

// GetTaskLockByTaskId 获取指定任务的 mutex lock
func (t *taskUploadServiceImpl) GetTaskLockByTaskId(taskId string) *sync.Mutex {
	actual, _ := t.TaskUploadLockMap.LoadOrStore(taskId, &sync.Mutex{})
	return actual.(*sync.Mutex)
}

// RemoveTaskInfoById 删除指定任务
func (t *taskUploadServiceImpl) RemoveTaskInfoById(taskId string) {
	t.TaskIdToTaskUploadInfo.Delete(taskId)
}

// RemoveTaskFailedPathById 删除失败 task 的本地文件系统路径
func (t *taskUploadServiceImpl) RemoveTaskFailedPathById(taskId string) {
	t.TaskIdToFailedFilePath.Delete(taskId)
}

// GetTaskLockByTaskId 删除指定任务的 mutex lock
func (t *taskUploadServiceImpl) RemoveUploadLock(taskId string) {
	t.TaskUploadLockMap.Delete(taskId)
}

// InitTaskUploadInfo 初始化 *TaskUploadInfo 实例
func (t *taskUploadServiceImpl) InitTaskUploadInfo() *TaskUploadInfo {
	return &TaskUploadInfo{
		PartNum:         atomic.NewInt32(1),
		PartsUpload:     make(map[string][]map[string]string),
		AccumulatePart:  atomic.NewString(""),
		Over:            atomic.NewBool(false),
		ChildUploadOver: atomic.NewBool(false),
	}
}

func (t *taskUploadServiceImpl) PutTaskUploadInfo(taskId string, info *TaskUploadInfo) {
	t.TaskIdToTaskUploadInfo.Store(taskId, info)
	syncTaskUploadInfo(taskId, info)
}

// PutTaskUploadAndGet 存储后读取 *TaskUploadInfo
func (t *taskUploadServiceImpl) PutTaskUploadAndGet(taskId string, info *TaskUploadInfo) *TaskUploadInfo {
	t.PutTaskUploadInfo(taskId, info)
	pl_boot.AppCtx.GetAppLogger().Info("PutTaskUploadAndGet params", zap.String("jobId", taskId),
		zap.Any("info", info))
	if value, ok := t.TaskIdToTaskUploadInfo.Load(taskId); ok {
		return value.(*TaskUploadInfo)
	}
	pl_boot.AppCtx.GetAppLogger().Info("PutTaskUploadAndGet fail", zap.String("taskId", taskId))
	return nil
}

// PutTaskIdFailedMap 存储 task 失败文件路径
func (t *taskUploadServiceImpl) PutTaskIdFailedMap(taskId string, value *string) {
	t.TaskIdToFailedFilePath.Store(taskId, value)
	syncTaskFailPath(taskId, value)
}

// GetTaskUploadInfoById 获取 task upload info
func (t *taskUploadServiceImpl) GetTaskUploadInfoById(taskId string) *TaskUploadInfo {
	if value, ok := t.TaskIdToTaskUploadInfo.Load(taskId); ok {
		return value.(*TaskUploadInfo)
	}
	pl_boot.AppCtx.GetAppLogger().Info("GetTaskUploadInfoById fail", zap.String("taskId", taskId))
	return nil
}

// GetTaskToFailedPathById 获取 task 失败文件路径
func (t *taskUploadServiceImpl) GetTaskToFailedPathById(taskId string) *string {
	if value, ok := t.TaskIdToFailedFilePath.Load(taskId); ok {
		return value.(*string)
	}
	pl_boot.AppCtx.GetAppLogger().Info("GetTaskToFailedPathById fail", zap.String("taskId", taskId))
	return nil
}

// TaskUploadInfo task 上报数据详情
type TaskUploadInfo struct {
	UploadId string
	PartNum  *atomic.Int32
	//  记录上传分片信息，用于请求分片服务端组合
	PartsUpload     map[string][]map[string]string
	AccumulatePart  *atomic.String
	Over            *atomic.Bool
	ChildUploadOver *atomic.Bool // 子分块上传是否还在进行
}

/**
* 备份失败文件上传
* 保持与Python 逻辑一致
**/
type TaskUploadInfoBackup struct {
	UploadId string
	PartNum  int32
	//  记录上传分片信息，用于请求分片服务端组合
	PartsUpload     map[string][]map[string]string
	AccumulatePart  string
	Over            bool
	ChildUploadOver bool
}

// Update 使用另一个 *TaskUploadInfo 实例更新自身数据
func (t *TaskUploadInfo) Update(info *TaskUploadInfo) {
	t.UploadId = info.UploadId
	t.PartNum.Store(info.PartNum.Load())
	t.PartsUpload = info.PartsUpload
	t.AccumulatePart.Store(info.AccumulatePart.Load())
	t.Over.Store(info.Over.Load())
}

// SetOver 标记为结束
func (t *TaskUploadInfo) SetOver(value bool) {
	go func() {
		for {
			if t.Over.CAS(t.Over.Load(), value) {
				break
			}
		}
	}()
	time.Sleep(5 * time.Millisecond)
}

// RemoveUploadCache 清理全部 task upload info
func (t *taskUploadServiceImpl) RemoveUploadCache(taskId string) {
	pl_boot.AppCtx.GetAppLogger().Info("RemoveUploadCache params", zap.String("jobId", taskId))
	t.TaskIdToFailedFilePath.Delete(taskId)
	t.TaskIdToTaskUploadInfo.Delete(taskId)
	t.TaskUploadLockMap.Delete(taskId)

	syncTaskFailPath(taskId, nil)
	syncTaskUploadInfo(taskId, nil)
}

func syncTaskUploadInfo(taskId string, info *TaskUploadInfo) error {
	db, _ := utils.DBOpen(models.BackupDir + taskId)
	key := "UploadBreakPoint.UploadInfoMap." + taskId

	if info != nil { // Put
		if value, err := json.Marshal(info); err == nil {
			utils.DBPut(db, key, string(value))
		} else {
			return err
		}
	} else { // Delete
		utils.DBDelete(db, key)
	}

	return nil
}

func syncTaskFailPath(taskId string, path *string) error {
	db, _ := utils.DBOpen(models.BackupDir + taskId)
	key := "UploadBreakPoint.FailPath." + taskId

	if path != nil { // Put
		utils.DBPut(db, key, *path)
	} else { // Delete
		utils.DBDelete(db, key)
	}

	return nil
}
