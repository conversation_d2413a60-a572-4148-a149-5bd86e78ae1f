package deal

import (
	"encoding/json"
	"strings"
	"time"

	"git.code.oa.com/cloud-msp/migration-lister/internal/service/consts"
	"git.code.oa.com/cloud-msp/migration-lister/internal/service/models"
	"git.code.oa.com/cloud-msp/migration-lister/pkg/conf"
	"git.code.oa.com/cloud-msp/migration-lister/pkg/utils"
	"git.code.oa.com/pulse-line/pl_boot"
	"go.uber.org/zap"
)

//对应  *msp.CreateFileJobReq.SpeedLimitInfo
//速度限制相关系信息
type SpeedLimitInfo struct {
	IsLimitSpeed     bool   `json:"isLimitSpeed"`
	LimitSpeedType   string `json:"limitSpeedType"`
	GlobalSpeedLimit struct {
		QPSLimit  int `json:"QPSLimit"`
		MbpsLimit int `json:"MbpsLimit"`
	} `json:"globalSpeedLimit"`
	TimePeriodSpeedLimit struct {
		Others []struct {
			StartTime string `json:"startTime"`
			EndTime   string `json:"endTime"`
			QPSLimit  int    `json:"QPSLimit"`
			MbpsLimit int    `json:"MbpsLimit"`
		} `json:"others"`
		Default struct {
			QPSLimit  int `json:"QPSLimit"`
			MbpsLimit int `json:"MbpsLimit"`
		} `json:"default"`
	} `json:"timePeriodSpeedLimit"`
}

//croservice 细节处理
type CroServiceDeal struct {
	logger *zap.Logger
}

//NEWCroServiceDeal
func NEWCroServiceDeal() *CroServiceDeal {
	return &CroServiceDeal{
		logger: pl_boot.AppCtx.GetAppLogger(),
	}
}

//获取qps限速相关信息
func (csd *CroServiceDeal) GetQpsLimit(task *models.Task) (limit int64) {
	defer func() {
		csd.logger.Info("GetQpsLimit Result",
			zap.Any("SpeedLimitInfo", task.SpeedLimitInfo),
			zap.Any("limit", limit),
			zap.Any("jobId", task.Id),
		)
	}()

	speedLimitInfoStr := task.SpeedLimitInfo

	if speedLimitInfoStr != "" {
		csd.logger.Info("new qpsLimit",
			zap.Any("SpeedLimitInfo", task.SpeedLimitInfo), zap.Any("jobId", task.Id))
		speedLimitInfo := &SpeedLimitInfo{}
		err := json.Unmarshal([]byte(speedLimitInfoStr), speedLimitInfo)
		if err != nil {
			csd.logger.Error("SpeedLimitInfo parse err", zap.Error(err), zap.Any("jobId", task.Id))
			return 0
		}

		if !speedLimitInfo.IsLimitSpeed {
			return conf.ListerConfig.GlobalConf.DefaultNoLimitQps
		}

		//全局
		if speedLimitInfo.LimitSpeedType == consts.SPEED_LIMIT_TYPE_GLOBAL {
			csd.logger.Info("global qpsLimit",
				zap.Int("QPSLimit", speedLimitInfo.GlobalSpeedLimit.QPSLimit),
				zap.Any("jobId", task.Id),
			)
			return int64(speedLimitInfo.GlobalSpeedLimit.QPSLimit)
		}

		//分段
		return dealIsNowInTimePeriodQps(csd, speedLimitInfo, task)
	} else {
		csd.logger.Info("old qpsLimit", zap.Any("task.QpsLimit", task.QpsLimit))
		return int64(task.QpsLimit)
	}
}

func (csd *CroServiceDeal) GetSpeedLimit(task *models.Task) (limit int64) {
	defer func() {
		csd.logger.Info("GetSpeedLimit Result",
			zap.Any("SpeedLimitInfo", task.SpeedLimitInfo),
			zap.Any("limit", limit),
			zap.Any("jobId", task.Id),
		)
		// 转换单位，limit应该为bps
		limit = limit * 1000 * 1000
	}()

	speedLimitInfoStr := task.SpeedLimitInfo

	if speedLimitInfoStr != "" {
		csd.logger.Info("new speedLimit",
			zap.Any("SpeedLimitInfo", task.SpeedLimitInfo), zap.Any("jobId", task.Id))
		speedLimitInfo := &SpeedLimitInfo{}
		err := json.Unmarshal([]byte(speedLimitInfoStr), speedLimitInfo)
		if err != nil {
			csd.logger.Error("SpeedLimitInfo parse err", zap.Error(err), zap.Any("jobId", task.Id))
			return 0
		}

		if !speedLimitInfo.IsLimitSpeed {
			return conf.ListerConfig.GlobalConf.DefaultNoLimitSpeed
		}

		//全局
		if speedLimitInfo.LimitSpeedType == consts.SPEED_LIMIT_TYPE_GLOBAL {
			csd.logger.Info("global speedLimit",
				zap.Int("MbpsLimit", speedLimitInfo.GlobalSpeedLimit.MbpsLimit),
				zap.Int("QPSLimit", speedLimitInfo.GlobalSpeedLimit.QPSLimit),
				zap.Any("jobId", task.Id),
			)
			// 存量任务将QPSLimit作为MbpsLimit使用, 而MbpsLimit=0, 这里要保证新agent上线后存量任务的限速不受影响
			if speedLimitInfo.GlobalSpeedLimit.MbpsLimit == 0 {
				return int64(speedLimitInfo.GlobalSpeedLimit.QPSLimit)
			}
			return int64(speedLimitInfo.GlobalSpeedLimit.MbpsLimit)
		}

		//分段
		return dealIsNowInTimePeriodSpeed(csd, speedLimitInfo, task)
	} else {
		csd.logger.Info("old speedLimit", zap.Any("task.SpeedLimit", task.SpeedLimit), zap.Any("jobId", task.Id))
		return int64(task.SpeedLimit)
	}
}

//判断是否是分时段限速，是的话返回qps大小
func dealIsNowInTimePeriodQps(csd *CroServiceDeal, speedLimitInfo *SpeedLimitInfo, task *models.Task) int64 {
	nowTime := time.Now()
	nowDateStr := (strings.Split(nowTime.String(), " "))[0] + " "
	timePeriodSpeedLimit := speedLimitInfo.TimePeriodSpeedLimit.Others
	for _, value := range timePeriodSpeedLimit {
		startTime, err := utils.ParseToBaseTime(nowDateStr + value.StartTime)
		endTime, err1 := utils.ParseToBaseTime(nowDateStr + value.EndTime)

		if err != nil || err1 != nil {
			csd.logger.Error("SpeedLimitInfo parse err", zap.Error(err),
				zap.Any("taskid", task.Id), zap.String("startTime", value.StartTime),
				zap.String("endTime", value.EndTime), zap.Error(err),
				zap.Error(err1))

			continue
		}

		if nowTime.After(startTime) && nowTime.Before(endTime) {
			csd.logger.Info("current time period speed limit", zap.String("taskid", task.Id),
				zap.Int("qpsLimit", value.QPSLimit))
			return convNoLimitQps(value.QPSLimit)
		}
	}

	csd.logger.Info("speed limit is on, but this time period no limit , use default",
		zap.String("taskid", task.Id), zap.Int("Default.QPSLimit", speedLimitInfo.TimePeriodSpeedLimit.Default.QPSLimit))
	return convNoLimitQps(speedLimitInfo.TimePeriodSpeedLimit.Default.QPSLimit)
}

func dealIsNowInTimePeriodSpeed(csd *CroServiceDeal, speedLimitInfo *SpeedLimitInfo, task *models.Task) int64 {
	nowTime := time.Now()
	nowDateStr := (strings.Split(nowTime.String(), " "))[0] + " "
	timePeriodSpeedLimit := speedLimitInfo.TimePeriodSpeedLimit.Others
	for _, value := range timePeriodSpeedLimit {
		startTime, err := utils.ParseToBaseTime(nowDateStr + value.StartTime)
		endTime, err1 := utils.ParseToBaseTime(nowDateStr + value.EndTime)

		if err != nil || err1 != nil {
			csd.logger.Error("SpeedLimitInfo parse err", zap.Error(err),
				zap.Any("taskid", task.Id), zap.String("startTime", value.StartTime),
				zap.String("endTime", value.EndTime), zap.Error(err),
				zap.Error(err1))

			continue
		}

		if nowTime.After(startTime) && nowTime.Before(endTime) {
			csd.logger.Info("current time period speed limit", zap.String("taskid", task.Id),
				zap.Int("speedLimit", value.MbpsLimit))
			// 存量任务将QPSLimit作为MbpsLimit使用, 而MbpsLimit=0, 这里要保证新agent上线后存量任务的限速不受影响
			if value.MbpsLimit == 0 {
				return convNoLimitSpeed(value.QPSLimit)
			}
			return convNoLimitSpeed(value.MbpsLimit)
		}
	}

	csd.logger.Info("speed limit is on, but this time period no limit , use default",
		zap.String("taskid", task.Id), zap.Int("Default.MbpsLimit", speedLimitInfo.TimePeriodSpeedLimit.Default.MbpsLimit))
	// 存量任务将QPSLimit作为MbpsLimit使用, 而MbpsLimit=0, 这里要保证新agent上线后存量任务的限速不受影响
	if speedLimitInfo.TimePeriodSpeedLimit.Default.MbpsLimit == 0 {
		return convNoLimitSpeed(speedLimitInfo.TimePeriodSpeedLimit.Default.QPSLimit)
	}
	return convNoLimitSpeed(speedLimitInfo.TimePeriodSpeedLimit.Default.MbpsLimit)
}

func convNoLimitQps(v int) int64 {
	if v <= 0 {
		return conf.ListerConfig.GlobalConf.DefaultNoLimitQps
	}
	return int64(v)
}

func convNoLimitSpeed(v int) int64 {
	if v <= 0 {
		return conf.ListerConfig.GlobalConf.DefaultNoLimitSpeed
	}
	return int64(v)
}

//{"ListerIp":"*************","LimitAgentSpeed":"200"}
type AgentListerAndSpeed struct {
	AgentListerIp string `json:"ListerIp"`
	AgentSpeed    string `json:"LimitAgentSpeed"`
}

//获取agent信息
func GetAgentListerAndSpeed(str string) (*AgentListerAndSpeed, error) {
	if str == "" {
		return nil, nil
	}
	td := &AgentListerAndSpeed{}
	if err := json.Unmarshal([]byte(str), td); err != nil {
		return nil, err
	}

	return td, nil
}
