package backup

import (
	"sync"

	"git.code.oa.com/cloud-msp/migration-lister/internal/service/models"
	"git.code.oa.com/pulse-line/pl_boot"
	"go.uber.org/atomic"
	"go.uber.org/zap"
)

var (
	initOnce sync.Once
	BackImpl *backupStateImpl
)

// backupStateImpl 实现状态备份管理接口 IBackupState
type backupStateImpl struct {
}

// GetBackupStateService 单例模式获取 state 备份管理接口的实例
func GetBackupStateService() IBackupState {
	initOnce.Do(func() {
		BackImpl = &backupStateImpl{}
	})
	return BackImpl
}

// 将 task 字段对应存入 back
func populateBackup(task *models.Task, back *TaskBackup) {
	task.WorkUrlTaskResultMap.Range(func(k, v interface{}) bool {
		key := k.(string)
		value := v.(*models.WorkTaskResult)
		result := WorkTaskResultBackup{
			BaseInfo: BaseInfoBackup{
				TaskId:            value.BaseInfo.TaskId,
				FragNum:           value.BaseInfo.FragNum.Load(),
				SuccessfulFileNum: value.BaseInfo.SuccessfulFileNum.Load(),
				FailedFileNum:     value.BaseInfo.FailedFileNum.Load(),
				SuccessfulSize:    value.BaseInfo.SuccessfulSize.Load(),
				FailedSize:        value.BaseInfo.FailedSize.Load(),
			},
			WorkerIp:   value.WorkerIp,
			WorkerPort: value.WorkerPort,
		}
		back.WorkUrlTaskResultMap[key] = result
		return true
	})

	task.FragIdSet.Range(func(k, v interface{}) bool {
		key := k.(int64)
		back.FragIdSet[key] = 1
		return true
	})

	task.AgentIpTaskResultMap.Range(func(k, v interface{}) bool {
		key := k.(string)
		value := v.(*models.AgentTaskResult)
		result := AgentTaskResultBackup{
			AgentIp: value.AgentIp,
			BaseInfo: BaseInfoBackup{
				TaskId:            value.BaseInfo.TaskId,
				FragNum:           value.BaseInfo.FragNum.Load(),
				SuccessfulFileNum: value.BaseInfo.SuccessfulFileNum.Load(),
				FailedFileNum:     value.BaseInfo.FailedFileNum.Load(),
				SuccessfulSize:    value.BaseInfo.SuccessfulSize.Load(),
				FailedSize:        value.BaseInfo.FailedSize.Load(),
			},
		}
		back.AgentIpTaskResultMap[key] = result
		return true
	})

	task.SlowCurMarkers.Range(func(k, v interface{}) bool {
		key := k.(string)
		value := v.(string)
		back.SlowCurMarkers[key] = value
		return true
	})

	task.QuickCurMarkers.Range(func(k, v interface{}) bool {
		key := k.(string)
		value := v.(string)
		back.QuickCurMarkers[key] = value
		return true
	})

}

// ConvertTaskToBackup 将 task 对象转换成 taskBackup 对象
func (i *backupStateImpl) ConvertTaskToBackup(task *models.Task) TaskBackup {

	back := &TaskBackup{}
	logger := pl_boot.AppCtx.GetAppLogger().With(
		zap.String("jobId", task.Id),
		zap.String("Name", task.Name))

	back = i.backupFromTask(back, task)
	// NOTE: 先处理耗时的部分
	populateBackup(task, back)
	back = i.setBackupListingCnt(back, task)

	logger.Info("backup state snapshot", zap.Any("backup_stat", back.StatSnapshot()))
	return *back
}

// ConvertTaskToBackup 将 taskBackup object 转换为 task object
func (i *backupStateImpl) ConvertBackupToTask(back TaskBackup) *models.Task {
	task := i.taskFromBackup(back)

	for k := range back.FragIdSet {
		task.FragIdSet.Store(k, 1)
	}

	for k, v := range back.WorkUrlTaskResultMap {
		key := k
		wr := &models.WorkTaskResult{
			BaseInfo: &models.BaseInfo{
				TaskId:            v.BaseInfo.TaskId,
				FragNum:           atomic.NewInt64(v.BaseInfo.FragNum),
				SuccessfulFileNum: atomic.NewInt64(v.BaseInfo.SuccessfulFileNum),
				FailedFileNum:     atomic.NewInt64(v.BaseInfo.FailedFileNum),
				SuccessfulSize:    atomic.NewInt64(v.BaseInfo.SuccessfulSize),
				FailedSize:        atomic.NewInt64(v.BaseInfo.FailedSize),
			},
			WorkerIp:   v.WorkerIp,
			WorkerPort: v.WorkerPort,
		}
		task.WorkUrlTaskResultMap.Store(key, wr)
	}

	for k, v := range back.AgentIpTaskResultMap {
		key := k
		ar := &models.AgentTaskResult{
			AgentIp: v.AgentIp,
			BaseInfo: &models.BaseInfo{
				TaskId:            v.BaseInfo.TaskId,
				FragNum:           atomic.NewInt64(v.BaseInfo.FragNum),
				SuccessfulFileNum: atomic.NewInt64(v.BaseInfo.SuccessfulFileNum),
				FailedFileNum:     atomic.NewInt64(v.BaseInfo.FailedFileNum),
				SuccessfulSize:    atomic.NewInt64(v.BaseInfo.SuccessfulSize),
				FailedSize:        atomic.NewInt64(v.BaseInfo.FailedSize),
			},
		}

		task.AgentIpTaskResultMap.Store(key, ar)
	}

	for k, v := range back.SlowCurMarkers {
		task.SlowCurMarkers.Store(k, v)
	}

	for k, v := range back.QuickCurMarkers {
		task.QuickCurMarkers.Store(k, v)
	}

	return task
}

// 从 task object 构造/初始化 task backup object
// NOTE: 只处理基本类型属性 string, int, etc
func (i *backupStateImpl) backupFromTask(back *TaskBackup, task *models.Task) *TaskBackup {

	back.Id = task.Id
	back.Name = task.Name
	back.ListerIp = task.ListerIp
	back.SpeedLimit = task.SpeedLimit
	back.AgentSpeedLimit = task.AgentSpeedLimit
	back.QpsLimit = task.QpsLimit
	back.Priority = task.Priority
	back.FragIdSet = make(map[int64]int)
	back.WorkUrlTaskResultMap = make(map[string]WorkTaskResultBackup)
	back.AgentIpTaskResultMap = make(map[string]AgentTaskResultBackup)
	back.SlowCurMarkers = make(map[string]string)
	back.QuickCurMarkers = make(map[string]string)
	back.JobInfo = *task.JobInfo
	back.ChanLevel = 0
	back.SpeedLimitInfo = task.SpeedLimitInfo
	back.IncrSrcInfo = task.IncrSrcInfo
	back.Retry = task.Retry // NOTE: crucial

	return back
}

// task 的源子计量更新到 backup
func (i *backupStateImpl) setBackupListingCnt(back *TaskBackup, task *models.Task) *TaskBackup {

	back.ExistFileSuccessNum = task.ExistFileSuccessNum.Load()
	back.ExistFileSuccessSize = task.ExistFileSuccessSize.Load()
	back.QuickListFileNum = task.QuickListFileNum.Load()
	back.QuickListSize = task.QuickListSize.Load()
	back.QuickListOver = task.QuickListOver.Load()
	back.SlowListFileNum = task.SlowListFileNum.Load()
	back.SlowListSize = task.SlowListSize.Load()
	back.SlowListFragNum = task.SlowListFragNum.Load()
	back.SlowListOver = task.SlowListOver.Load()
	back.State = task.GetState()
	back.SuccessfulFragNum = task.SuccessfulFragNum.Load()
	back.SuccessfulFileNum = task.SuccessfulFileNum.Load()
	back.FailedFileNum = task.FailedFileNum.Load()
	back.SuccessfulSize = task.SuccessfulSize.Load()
	back.FailedSize = task.FailedSize.Load()
	back.FragId = task.FragId.Load()
	back.QuickListSkipped = task.GetSkipped()
	back.WorkerSpeedInfo = make(map[string]WorkSpeedInfo)
	task.WorkSpeedInfo.Range(func(k, v interface{}) bool {
		workerAddr := k.(string)
		wi := v.(*models.WorkerSpeedInfo)
		back.WorkerSpeedInfo[workerAddr] = WorkSpeedInfo{
			SpeedInUse: wi.SpeedInUse,
			SpeedLimit: wi.SpeedLimit,
		}
		return true
	})
	return back
}

// 从 task backup object 构造/初始化 task object 的基本类型属性 string, int, etc.
func (i *backupStateImpl) taskFromBackup(back TaskBackup) *models.Task {
	task := models.NewTask()

	task.Id = back.Id
	task.Name = back.Name
	task.ListerIp = back.ListerIp
	task.SpeedLimit = back.SpeedLimit
	task.AgentSpeedLimit = back.AgentSpeedLimit
	task.QpsLimit = back.QpsLimit
	task.Priority = back.Priority
	task.ChanLevel = 0
	task.JobInfo = &back.JobInfo

	// 装载原子量
	task.QuickListSize.Store(back.QuickListSize)
	task.QuickListFileNum.Store(back.QuickListFileNum)
	task.SlowListSize.Store(back.SlowListSize)
	task.SlowListFileNum.Store(back.SlowListFileNum)
	task.ExistFileSuccessNum.Store(back.ExistFileSuccessNum)
	task.ExistFileSuccessSize.Store(back.ExistFileSuccessSize)
	task.QuickListOver.Store(back.QuickListOver)
	task.SlowListFragNum.Store(back.SlowListFragNum)
	task.SlowListOver.Store(back.SlowListOver)
	task.SuccessfulFragNum.Store(back.SuccessfulFragNum)
	task.SuccessfulFileNum.Store(back.SuccessfulFileNum)
	task.FailedFileNum.Store(back.FailedFileNum)
	task.SuccessfulSize.Store(back.SuccessfulSize)
	task.FailedSize.Store(back.FailedSize)
	task.FragId.Store(back.FragId)
	task.SpeedLimitInfo = back.SpeedLimitInfo
	task.IncrSrcInfo = back.IncrSrcInfo
	task.Retry = back.Retry // NOTE: crucial
	task.QuickListSkipped.Store(back.QuickListSkipped)
	task.State.Store(int64(back.State))
	task.WorkerMetric = &sync.Map{}
	for k, v := range back.WorkerSpeedInfo {
		task.WorkSpeedInfo.Store(k, &models.WorkerSpeedInfo{
			SpeedInUse: v.SpeedInUse,
			SpeedLimit: v.SpeedLimit,
		})
	}

	return task
}
