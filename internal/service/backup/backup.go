package backup

import (
	"context"
	"sync"
	"time"
	"unsafe"

	"git.code.oa.com/cloud-msp/migration-lister/internal/service/models"
	"git.code.oa.com/cloud-msp/migration-lister/internal/service/tasks"

	"go.uber.org/atomic"
	"go.uber.org/zap"
)

// 任务未完成分片备份
type FragSetBackup map[string]map[int64]models.FragBaseInfo // fragId string: FragBaseInfo

// CreateTaskBackup
func CreateTaskBackup(taskId string, logger *zap.Logger) TaskBackup {
	taskAdmin := tasks.GetTaskAdmin()

	task := taskAdmin.GetTask(taskId, logger)
	backupState := GetBackupStateService()
	// task 转成 taskBackup
	taskBackup := backupState.ConvertTaskToBackup(task)
	logger.Info(
		"persist task",
		zap.Any("binSize", unsafe.Sizeof(task)),
		zap.Bool("task.Retry", task.Retry),
		zap.Bool("backup.Retry", taskBackup.Retry))

	return taskBackup
}

// CreateFragSetBackup
func CreateFragSetBackup(taskId string) FragSetBackup {
	taskAdmin := tasks.GetTaskAdmin()

	// unfinishedFrag 只备份 base info
	baseInfoSet := make(map[int64]models.FragBaseInfo)

	unfinishedFragSet, _ := taskAdmin.GetUnfinishedFragSet(taskId)
	unfinishedFragSet.Range(
		func(key, value interface{}) bool {
			fragP := value.(*models.Frag)
			var fragBaseInfo = *fragP.BaseInfo
			baseInfoSet[fragBaseInfo.FragId] = fragBaseInfo
			return true
		},
	)

	return FragSetBackup{taskId: baseInfoSet}
}

// LoadTaskBackup
func LoadTaskBackup(backup TaskBackup, cancel context.CancelFunc) {
	// 恢复 tasks
	backupSvc := GetBackupStateService()
	task := backupSvc.ConvertBackupToTask(backup)

	taskAdmin := tasks.GetTaskAdmin()
	// 恢复任务加入管理器
	taskAdmin.AddTask(task, cancel)
}

// LoadFragSetBackup
func LoadFragSetBackup(backup FragSetBackup, task *models.Task, logger *zap.Logger) {
	taskAdmin := tasks.GetTaskAdmin()
	if len(backup) == 0 {
		logger.Warn("task has no unfinished frags")
	}

	// 恢复任务未完成的分片
	for taskId, fragSet := range backup {
		fragBaseInfoSet := fragSet

		unfinishedFragSet := &sync.Map{}
		// NOTE: 分片备份只包含静态数据, 运行时信息需要重新构造
		for fragId, fragBaseInfo := range fragBaseInfoSet {
			// 跳过已经处理完的分片
			if _, ok := task.FragIdSet.Load(fragId); ok {
				logger.Info(
					"unfinished frag has been processed", zap.Int64("fragId", fragId))
				continue
			}

			// NOTE: 备份只包含 BaseInfo, 重置 runtime
			var baseInfo = fragBaseInfo
			frag := &models.Frag{
				BaseInfo:    &baseInfo,
				RuntimeInfo: models.NewFragRuntimeInfo(),
				Over:        atomic.NewBool(false),
			}
			frag.RuntimeInfo.FirstHeart.Store(time.Now())
			frag.RuntimeInfo.LastHeart.Store(time.Now())

			unfinishedFragSet.Store(fragId, frag)
			logger.Info("add unfinished frag", zap.Int64("fragId", fragId))
		}

		// 恢复分片加入管理器
		taskAdmin.AddUnfinishedFragSet(taskId, unfinishedFragSet)
	}
}
