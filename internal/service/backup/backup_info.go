package backup

import (
	"encoding/json"

	"git.code.oa.com/cloud-msp/migration-lister/api"
	"git.code.oa.com/cloud-msp/migration-lister/tools"
)

// BaseInfoBackup task 的备份的基本属性
type BaseInfoBackup struct {
	TaskId            string
	FragNum           int64
	SuccessfulFileNum int64
	FailedFileNum     int64
	SuccessfulSize    int64
	FailedSize        int64
}

// TaskBackup task 的备份数据
// NOTE: 成员和 models.Task 一一对应
type TaskBackup struct {
	Id                   string
	Name                 string
	ListerIp             string
	SpeedLimit           int
	AgentSpeedLimit      int
	QpsLimit             int64
	Priority             int   // 优先级
	ExistFileSuccessNum  int64 // 已有的迁移量
	ExistFileSuccessSize int64
	QuickListFileNum     int64
	QuickListSize        int64
	QuickListOver        bool
	SlowListFileNum      int64
	SlowListSize         int64
	SlowListFragNum      int64
	SlowListOver         bool
	State                api.TaskState // migrateproject.TaskState, 对应 tasks.Stat
	SuccessfulFragNum    int64         // 等待任务遍历完成后更新分片数量
	SuccessfulFileNum    int64
	FailedFileNum        int64
	SuccessfulSize       int64
	FailedSize           int64
	FragId               int64 // NOTE: 非常重要, 任务恢复以后分片要从上次位置开始
	FragIdSet            map[int64]int
	WorkUrlTaskResultMap map[string]WorkTaskResultBackup
	AgentIpTaskResultMap map[string]AgentTaskResultBackup
	SlowCurMarkers       map[string]string
	QuickCurMarkers      map[string]string
	JobInfo              tools.JobInfo
	ChanLevel            int
	SpeedLimitInfo       string // 限速配置
	IncrSrcInfo          string // NOTE: 增量源
	Retry                bool   // NOTE: 非常重要, 数据库表里的 102 状态持续时间很短暂
	QuickListSkipped     int64
	WorkerSpeedInfo      map[string]WorkSpeedInfo
}

// 获取 task 当前的所有计数
func (t *TaskBackup) StatSnapshot() map[string]int64 {
	return map[string]int64{
		"ExistFileSuccessNum":  t.ExistFileSuccessNum,
		"ExistFileSuccessSize": t.ExistFileSuccessSize,
		"QuickListFileNum":     t.QuickListFileNum,
		"QuickListSize":        t.QuickListSize,
		"SlowListFileNum":      t.SlowListFileNum,
		"SlowListSize":         t.SlowListSize,
		"SuccessfulFileNum":    t.SuccessfulFileNum,
		"SuccessfulSize":       t.SuccessfulSize,
		"FailedSize":           t.FailedSize,
		"FailedFileNum":        t.FailedFileNum,
	}
}

func (t *TaskBackup) Unmarshal_WorkUrlTaskResultMap(data string) {
	if data == "" {
		return
	}
	if err := json.Unmarshal([]byte(data), &t.WorkUrlTaskResultMap); err != nil {
		panic(err)
	}
}

func (t *TaskBackup) Unmarshal_AgentIpTaskResultMap(data string) {
	if data == "" {
		return
	}
	if err := json.Unmarshal([]byte(data), &t.AgentIpTaskResultMap); err != nil {
		panic(err)
	}
}

func (t *TaskBackup) Unmarshal_SlowCurMarkers(data string) {
	if data == "" {
		return
	}
	if err := json.Unmarshal([]byte(data), &t.SlowCurMarkers); err != nil {
		panic(err)
	}
}

func (t *TaskBackup) Unmarshal_QuickCurMarkers(data string) {
	if data == "" {
		return
	}
	if err := json.Unmarshal([]byte(data), &t.QuickCurMarkers); err != nil {
		panic(err)
	}
}

func (t *TaskBackup) Unmarshal_JobInfo(data string) {
	if data == "" {
		return
	}
	if err := json.Unmarshal([]byte(data), &t.JobInfo); err != nil {
		panic(err)
	}
}

// WorkTaskResultBackup worker task 执行结果备份
type WorkTaskResultBackup struct {
	BaseInfo   BaseInfoBackup
	WorkerIp   string
	WorkerPort int64
}

// AgentTaskResultBackup agent task 执行结果备份
type AgentTaskResultBackup struct {
	AgentIp  string
	BaseInfo BaseInfoBackup
}

type WorkSpeedInfo struct {
	SpeedInUse int64
	SpeedLimit int64
}
