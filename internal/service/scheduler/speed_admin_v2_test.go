package scheduler

import (
	"git.code.oa.com/cloud-msp/migration-lister/internal/service/models"
	"git.code.oa.com/cloud-msp/migration-lister/tools"
	"testing"
)

func TestSpeedAllocatorV2_allocate(t *testing.T) {
	boot()

	initSAV2()
	SpeedLimitInfo := `{"isLimitSpeed":true,"limitSpeedType":"timePeriod","globalSpeedLimit":{"QPSLimit":2000,"MbpsLimit":0},"timePeriodSpeedLimit":{"others":[{"startTime":"08:00:00","endTime":"23:59:00","QPSLimit":200,"MbpsLimit":0}],"default":{"QPSLimit":-1,"MbpsLimit":0}}}`
	tast := &models.Task{
		Id:              "j1",
		Name:            "",
		ListerIp:        "",
		SpeedLimit:      0,
		AgentSpeedLimit: 0,
		QpsLimit:        0,
		SpeedLimitInfo:  SpeedLimitInfo,
		Priority:        0,
		ChanLevel:       0,
		IncrSrcInfo:     "",
		JobInfo: &tools.JobInfo{
			AgentInfo:           nil,
			DirectConnectInfo:   nil,
			DstBucket:           nil,
			DstRegion:           nil,
			DstSecretID:         nil,
			DstSecretKey:        nil,
			FileEndTime:         nil,
			FileOverWrite:       nil,
			FileStartTime:       nil,
			HasFileTimeFilter:   nil,
			JobID:               nil,
			JobProgress:         nil,
			JobStatus:           nil,
			LimitQPS:            nil,
			LimitSpeed:          nil,
			SpeedLimitInfo:      &SpeedLimitInfo,
			MigrationHeader:     nil,
			MigrationHeaderType: nil,
			MigrationRule:       nil,
			MigrationRuleType:   nil,
			PathConf:            nil,
			Priority:            nil,
			ChannelType:         nil,
			SavePath:            nil,
			SrcBucket:           nil,
			SrcFileName:         nil,
			SrcFileURL:          nil,
			SrcRegion:           nil,
			SrcSecretID:         nil,
			SrcSecretKey:        nil,
			SrcService:          nil,
			Status:              nil,
			StorageType:         nil,
			UserAppID:           nil,
			ManifestURL:         nil,
			BucketType:          nil,
			KodoEndpoint:        nil,
			IsIncrease:          nil,
			IncrSrcInfo:         nil,
		},
		ExistFileSuccessNum:  nil,
		ExistFileSuccessSize: nil,
		Retry:                false,
		QuickListFileNum:     nil,
		QuickListSize:        nil,
		QuickListOver:        nil,
		SlowListFileNum:      nil,
		SlowListSize:         nil,
		SlowListFragNum:      nil,
		SlowListOver:         nil,
		QuickCurMarkers:      nil,
		SlowCurMarkers:       nil,
		FragId:               nil,
		FragIdSet:            nil,
		SuccessfulFragNum:    nil,
		SuccessfulFileNum:    nil,
		FailedFileNum:        nil,
		SuccessfulSize:       nil,
		FailedSize:           nil,
		WorkUrlTaskResultMap: nil,
		AgentIpTaskResultMap: nil,
		Mutex:                nil,
		WorkerMetric:         nil,
		WorkSpeedInfo:        nil,
	}

	SpeedAdminV2.AddTask(tast)

	//go func() {
	//	SpeedAdminV2.ExecutedJobInfo()
	//}()

	go func() {

	}()
}
