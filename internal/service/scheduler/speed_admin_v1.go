package scheduler

import (
	"context"
	"git.code.oa.com/cloud-msp/migration-lister/internal/service/deal"
	"git.code.oa.com/cloud-msp/migration-lister/internal/service/models"
	"git.code.oa.com/cloud-msp/migration-lister/pkg/conf"
	workerapi "git.code.oa.com/cloud-msp/migration-worker/api"
	"git.code.oa.com/pulse-line/pl_boot"
	"go.uber.org/zap"
	"math"
	"sync"
	"time"
)

type SpeedAllocator struct {
	mu            sync.Mutex
	jobSpeedAdmin map[string]*SpeedRecordAdmin //第一层 JobId
}

type SpeedState int

const (
	Normal SpeedState = iota
	PreClean
)

type TaskWorkerSpeedRecord struct {
	State          SpeedState // 这里的分配只管SpeedAdmin， 不必管workerAdmin
	WorkerAddr     string
	SpeedAllocated int64
	SpeedInUse     int64
	LastHeart      int64
}

type SpeedRecordAdmin struct {
	ctx    context.Context
	cancel context.CancelFunc
	// TODO stke速度计算叠加
	srs      map[string]*TaskWorkerSpeedRecord // key->workerAddr
	jobSpeed int64
	jobId    string
	logger   *zap.Logger
}

func (sra *SpeedRecordAdmin) exit() {
	sra.logger.Info("job SpeedRecordAdmin exit")
	sra.cancel()
	return
}

// 单线程做速度分配， 这里需要记录workerAddr-SpeedAllocated
func (sra *SpeedRecordAdmin) run() {
	for {
		select {
		case <-sra.ctx.Done():
			sra.logger.Info("job ctx cancel")
			return
		default:
		}
		sra.logger.Debug("SpeedRecordAdmin Start")
		// 计算速度分配的时候要max(worker-speedInUse, worker-SpeedAllocated)
		// TODO 最简单的，直接把表里速度每次都均分，最终他们会按能力取得自己想要的需要的极限速度
		// TODO 以后再按其他算法统计做分配
		// 遍历worker, 区分好available和unavailable
		aWorkers, err := wa.AvailableWorkers()
		sra.logger.Debug("aWorkers", zap.Any("aWorkers", aWorkers))
		if err != nil {
			sra.logger.Error("AvailableWorkers Error", zap.Error(err))
			time.Sleep(time.Second)
			continue
		}

		// 获取aWorkers关于task的速度统计
		for _, w := range aWorkers {
			info, ok := w.GetWorkerTaskInfoByTaskId(sra.jobId)
			sra.logger.Debug("GetWorkerTaskInfoByTaskId",
				zap.Any("sra.jobId", sra.jobId),
				zap.Any("info", info),
				zap.Any("ok", ok),
			)
			if !ok {
				// 没有下发记录，则在这里找找看有没有记录
				record, ook := sra.srs[w.Addr()]
				sra.logger.Debug("GetWorkerTaskInfoByTaskId !ok",
					zap.Any("sra.jobId", sra.jobId),
					zap.Any("info", info),
					zap.Any("ok", ok),
					zap.Any("record", record),
					zap.Any("ook", ook),
				)
				if ook {
					record.State = Normal
					// 可能w挂过然后重启， 这里略过，后面会重新下发通知的
					sra.logger.Debug("GetWorkerTaskInfoByTaskId !ok !ook",
						zap.Any("sra.jobId", sra.jobId),
						zap.Any("info", info),
						zap.Any("ok", ok),
						zap.Any("record", record),
						zap.Any("ook", ook),
					)
					continue
				} else {
					// 这里也没有记录，当成新来的
					record = &TaskWorkerSpeedRecord{
						State:          Normal,
						WorkerAddr:     w.Addr(),
						SpeedAllocated: 0,
						SpeedInUse:     0,
						LastHeart:      time.Now().Unix(),
					}

					sra.srs[w.Addr()] = record
					sra.logger.Debug("GetWorkerTaskInfoByTaskId !ok ook",
						zap.Any("sra.jobId", sra.jobId),
						zap.Any("info", info),
						zap.Any("ok", ok),
						zap.Any("record", record),
						zap.Any("ook", ook),
						zap.Any("sra.srs", sra.srs),
					)
				}
			} else {
				// 更新状态
				record, ook := sra.srs[w.Addr()]
				sra.logger.Debug("GetWorkerTaskInfoByTaskId ok",
					zap.Any("sra.jobId", sra.jobId),
					zap.Any("info", info),
					zap.Any("ok", ok),
					zap.Any("record", record),
					zap.Any("sra.srs", sra.srs),
				)
				if !ook {
					sra.srs[w.Addr()] = &TaskWorkerSpeedRecord{
						State:          Normal,
						WorkerAddr:     w.Addr(),
						SpeedAllocated: info.SpeedAllocated,
						SpeedInUse:     info.SpeedInUse,
						LastHeart:      info.LastHeart,
					}
					sra.logger.Debug("GetWorkerTaskInfoByTaskId ok !ook",
						zap.Any("sra.jobId", sra.jobId),
						zap.Any("info", info),
						zap.Any("ok", ok),
						zap.Any("record", record),
						zap.Any("sra.srs", sra.srs),
					)
				} else {
					if record.State == Normal {
						if record.LastHeart < info.LastHeart {
							// 更新
							record.SpeedInUse = info.SpeedInUse
							record.SpeedAllocated = info.SpeedAllocated
							record.LastHeart = info.LastHeart
						}
					} else {
						err = allocate(sra.jobId, record.WorkerAddr, record.SpeedAllocated)
						if err != nil {
							delete(sra.srs, record.WorkerAddr)
						} else {
							record.State = Normal
						}
					}

					sra.logger.Debug("GetWorkerTaskInfoByTaskId ok ook",
						zap.Any("sra.jobId", sra.jobId),
						zap.Any("info", info),
						zap.Any("ok", ok),
						zap.Any("record", record),
						zap.Any("sra.srs", sra.srs),
					)
				}
			}
		}
		// 以上步骤使得sra更新了最新的可用worker速度统计信息
		sra.logger.Debug("availableInfos", zap.Any("srs", sra.srs))
		// 再统计NotAvailable的信息
		naWorkers, err := wa.NotAvailableWorkers()
		if err != nil {
			sra.logger.Error("NotAvailableWorkers Error", zap.Error(err))
			time.Sleep(time.Second)
			continue
		}
		sra.logger.Debug("naWorkers", zap.Any("naWorkers", naWorkers))
		for _, w := range naWorkers {
			record, ok := sra.srs[w.Addr()]
			if !ok {
				// 在速度记录里不存在，则做一次速度下发测试，如果能通过，
				// 则只能记做可用，生成一个新的record插入
				taskInfo, ook := w.GetWorkerTaskInfoByTaskId(sra.jobId)
				if !ook {
					// 什么都不用做
					continue
				} else {
					err = allocate(sra.jobId, w.Addr(), taskInfo.SpeedAllocated)
					if err != nil {
						record = &TaskWorkerSpeedRecord{
							State:          PreClean,
							WorkerAddr:     w.Addr(),
							SpeedAllocated: taskInfo.SpeedAllocated,
							SpeedInUse:     taskInfo.SpeedInUse,
							LastHeart:      taskInfo.LastHeart,
						}
						sra.srs[w.Addr()] = record
					} else {
						record = &TaskWorkerSpeedRecord{
							State:          Normal,
							WorkerAddr:     w.Addr(),
							SpeedAllocated: taskInfo.SpeedAllocated,
							SpeedInUse:     taskInfo.SpeedInUse,
							LastHeart:      taskInfo.LastHeart,
						}
						sra.srs[w.Addr()] = record
					}
				}
			} else {
				// record记录也存在
				// record.State == PreClean测试一下, 如果能正常下发，则改成Normal, 否则删除
				// record.State == Normal, 如果能正常下发，则跳过，否则改成PreClean
				if record.State == PreClean {
					err = allocate(sra.jobId, w.Addr(), record.SpeedAllocated)
					if err != nil {
						// 直接清除
						delete(sra.srs, w.Addr())
					} else {
						// 下发成功，扭转状态
						record.State = Normal
						// 暂时把这种记做是满速，等待变成Available后再更新速度
						record.SpeedInUse = record.SpeedAllocated
						record.LastHeart = time.Now().Unix()
					}
				} else {
					// 现在的状态还是Normal, 做一个测试，如果能下发，则更新下速度，否则改PreClean
					err = allocate(sra.jobId, w.Addr(), record.SpeedAllocated)
					if err != nil {
						// 直接清除, 相当于回收了速度资源
						record.State = PreClean
						record.SpeedInUse = record.SpeedAllocated
					}
					record.LastHeart = time.Now().Unix()
				}
			}
		}
		sra.logger.Debug("nAvailableInfos", zap.Any("srs", sra.srs))
		// 使用sra.srs做一遍分配，现在sra.srs都是需要分配的，上面的归零后
		// 第一遍回收
		var allocated = int64(0) // 实际上取inUse和allocated的最大值加
		var normal = 0
		for _, record := range sra.srs {
			if record.State == Normal {
				normal += 1
				if float64(record.SpeedInUse)/float64(record.SpeedAllocated) < 0.8 {
					err = allocate(sra.jobId, record.WorkerAddr, record.SpeedInUse)
					if err != nil {
						record.State = PreClean
					} else {
						// 回收
						taskInfo := wa.GetWorkerByAddr(record.WorkerAddr)
						if taskInfo != nil {
							info, ok := taskInfo.GetWorkerTaskInfoByTaskId(sra.jobId)
							if !ok {
								record.SpeedAllocated = record.SpeedInUse
							} else {
								fragNum := len(info.FragSet)
								if fragNum > 0 {
									record.SpeedAllocated = int64(math.Max(float64(record.SpeedInUse),
										float64(conf.ListerConfig.GlobalConf.SpeedPodUnit)))
								} else {
									record.SpeedAllocated = record.SpeedInUse
								}
							}
						} else {
							record.SpeedAllocated = record.SpeedInUse
						}
					}
					allocated += record.SpeedAllocated
				} else {
					allocated += record.SpeedAllocated
				}
			} else {
				allocated += record.SpeedAllocated
			}
		}

		sra.logger.Debug("allocated",
			zap.Any("allocated", allocated),
			zap.Any("normal", normal),
			zap.Any("jobSpeed", sra.jobSpeed),
			zap.Any("srs", sra.srs),
		)

		if normal == 0 {

			time.Sleep(time.Duration(conf.ListerConfig.GlobalConf.SpeedInterval) * time.Second)
			continue
		}
		// 第二遍尝试下发，计算和speed的差值，多退少补
		if allocated > sra.jobSpeed {
			// 均分
			needRecycle := allocated - sra.jobSpeed
			avg := int64(math.Floor(float64(needRecycle) / float64(normal)))
			// 给所有计算Normal的开始回收
			// 可能要多轮回收
			for _, record := range sra.srs {
				if record.State == Normal {
					if record.SpeedAllocated > avg {
						record.SpeedAllocated = record.SpeedAllocated - avg
					} else {
						record.SpeedAllocated = 0
					}
				}
			}

		} else {
			// 均分
			canAllocated := sra.jobSpeed - allocated
			avg := int64(math.Floor(float64(canAllocated) / float64(normal)))
			for _, record := range sra.srs {
				if record.State == Normal {
					record.SpeedAllocated = record.SpeedAllocated + avg
				}
			}
		}
		sra.logger.Debug("beforeAllocated", zap.Any("srs", sra.srs))
		// 定时通知workerAddr admin-SpeedAllocated
		var wg sync.WaitGroup
		for _, record := range sra.srs {
			if record.State == Normal {
				wg.Add(1)
				go func(record *TaskWorkerSpeedRecord) {
					defer wg.Done()
					err := allocate(sra.jobId, record.WorkerAddr, record.SpeedAllocated)
					if err != nil {
						record.State = PreClean
					}
				}(record)
			}
		}
		sra.logger.Debug("afterAllocated", zap.Any("srs", sra.srs))
		wg.Wait()
		time.Sleep(time.Duration(conf.ListerConfig.GlobalConf.SpeedInterval) * time.Second)
	}

}

func allocate(taskId, addr string, speed int64) error {
	client := wa.GetWorkerClientByAddr(addr)
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	req := &workerapi.SpeedAllocateRequest{
		TaskId:        taskId,
		SpeedAllocate: speed,
	}
	if req.SpeedAllocate < 0 {
		req.SpeedAllocate = 0
	}
	_, err := client.SpeedAllocate(ctx, req)
	if err != nil {
		pl_boot.AppCtx.GetAppLogger().Error("SpeedAllocate error", zap.Error(err),
			zap.Any("req", req))
		return err
	}
	return nil
}

func (sa *SpeedAllocator) AddTask(task *models.Task) {
	sa.mu.Lock()
	defer sa.mu.Unlock()

	admin, ok := sa.jobSpeedAdmin[task.Id]
	if !ok {
		ctx, cancel := context.WithCancel(context.Background())
		admin = &SpeedRecordAdmin{
			ctx:      ctx,
			cancel:   cancel,
			jobId:    task.Id,
			srs:      make(map[string]*TaskWorkerSpeedRecord),
			jobSpeed: deal.NEWCroServiceDeal().GetQpsLimit(task),
			logger: pl_boot.AppCtx.GetAppLogger().With(
				zap.String("speedAdmin", "speedAdmin"),
				zap.String("jobId", task.Id),
			),
		}
		sa.jobSpeedAdmin[task.Id] = admin
		admin.logger.Debug("addJobSpeedAdmin", zap.Any("task.Id", task.Id))
		go func() {
			admin.run()
		}()
	} else {
		return
	}
}

func (sa *SpeedAllocator) DelTask(taskId string) {
	sa.mu.Lock()
	defer sa.mu.Unlock()

	admin, ok := sa.jobSpeedAdmin[taskId]
	if !ok {
		pl_boot.AppCtx.GetAppLogger().Debug("DelTask No admin",
			zap.Any("taskId", taskId),
		)
		return
	}
	pl_boot.AppCtx.GetAppLogger().Debug("DelTask admin",
		zap.Any("taskId", taskId),
	)
	admin.exit()
	delete(sa.jobSpeedAdmin, taskId)
	return
}

func (sa *SpeedAllocator) UpdateTask(task *models.Task) {
	sa.mu.Lock()
	defer sa.mu.Unlock()

	admin, ok := sa.jobSpeedAdmin[task.Id]
	if !ok {
		return
	}

	admin.jobSpeed = deal.NEWCroServiceDeal().GetQpsLimit(task)
	return
}
