package scheduler

import (
	"context"
	"fmt"
	"sync"
	"time"

	"git.code.oa.com/cloud-msp/migration-lister/api"
	"git.code.oa.com/cloud-msp/migration-lister/internal/service/models"
	"git.code.oa.com/cloud-msp/migration-lister/pkg/conf"
	workerapi "git.code.oa.com/cloud-msp/migration-worker/api"
	"git.code.oa.com/pulse-line/pl_boot"
	"go.uber.org/zap"
	"google.golang.org/grpc"
)

// 可供决策的监控内容
// WorkerAddr Cpu Memory SpeedInUse Speed FailRate Timeout
// 外部Policy限制条件 Speed Qps

// worker管理器，全局单例使用
type WorkerAdmin interface {
	// update_worker 添加或者更新worker信息, 信息存储到worker信息池（map or sync.Map）
	UpdateWorker(ip string, port int, baseInfo *api.WorkerBaseInfo, taskInfo *api.WorkerTaskInfo,
		isStke bool, region string)
	// get_worker 从worker记录里提供所有可用worker信息
	AvailableWorkers() ([]*models.WorkerInfo, error)
	NotAvailableWorkers() ([]*models.WorkerInfo, error)
	// set_worker_not_available 标记某个worker状态不可用
	SetWorkerNotAvailable(worker *models.WorkerInfo)
	// worker心跳检测
	WorkerHeartCheck() // 只dial conn即可
	GetWorkerClientByAddr(addr string) workerapi.WorkerServiceClient
	// 判断某个worker是否available
	Available(addr string) bool
}

// GetWorkerAdmin 单例模式获取 WorkerAdmin 接口实例
func GetWorkerAdmin() WorkerAdmin {
	return wa
}

// WorkGroup
type WorkGroup struct {
	workerInfo   map[string]*models.WorkerInfo // key->addr
	conns        map[string]*grpc.ClientConn
	workerClient map[string]workerapi.WorkerServiceClient
	mutex        sync.Mutex
	logger       *zap.Logger
}

func (wg *WorkGroup) Available(addr string) (res bool) {
	wg.mutex.Lock()
	defer wg.mutex.Unlock()
	defer func() {
		wg.logger.Debug("AvailableWorkers",
			zap.Any("res", res))
	}()

	if w, ok := wg.workerInfo[addr]; ok {
		if w.Available {
			return true
		} else {
			return false
		}
	} else {
		return false
	}
}

func (wg *WorkGroup) GetWorkerClientByAddr(addr string) workerapi.WorkerServiceClient {
	wg.mutex.Lock()
	defer wg.mutex.Unlock()
	wc, ok := wg.workerClient[addr]
	if ok {
		return wc
	}

	conn := wg.getConnByAddr(addr)
	client := workerapi.NewWorkerServiceClient(conn)
	wg.workerClient[addr] = client
	return client
}

func (wg *WorkGroup) NotAvailableWorkers() (res []*models.WorkerInfo, err error) {
	wg.mutex.Lock()
	defer wg.mutex.Unlock()
	defer func() {
		wg.logger.Debug("NotAvailableWorkers", zap.Any("res", res))
	}()
	res = make([]*models.WorkerInfo, 0)
	for _, w := range wg.workerInfo {
		if !w.Available {
			res = append(res, w)
		}
	}

	return res, nil
}

// WorkerHeartCheck
func (wg *WorkGroup) WorkerHeartCheck() {
	for {
		var wag sync.WaitGroup
		wg.mutex.Lock()
		for _, w := range wg.workerInfo {
			wag.Add(1)
			go func(w *models.WorkerInfo) {
				ctx, cancel := context.WithTimeout(context.Background(), 2*time.Second)
				defer cancel()
				defer wag.Done()
				// if w.IsStke {
				if true {
					// 按周期，超过一定周期数，直接按失效处理。比如三个周期置为失效，6个周期删除记录
					lh := time.Unix(w.BaseInfo.LastHeart, 0)
					if time.Now().Sub(lh) > time.Duration(3*conf.ListerConfig.GlobalConf.SpeedInterval)*time.Second {
						wg.logger.Info("worker heart timeout", zap.Any("addr", w.Addr()),
							zap.Any("lh", lh.String()))
						w.Available = false
						return
					}
					// TODO 6个周期删除
					//if time.Now().Sub(lh) > time.Duration(6*conf.ListerConfig.GlobalConf.SpeedInterval)*time.Second {
					//	wg.logger.Info("worker heart timeout", zap.Any("addr", w.Addr()),
					//		zap.Any("lh", lh.String()))
					//	w.Available = false
					//	return
					//}

					return
				}
				opts := []grpc.DialOption{
					grpc.WithInsecure(),
				}

				// yunis注，这里有坑，杀掉worker进程后，这里Dial并没有返回err
				_, err := grpc.DialContext(ctx, w.Addr(), opts...)
				if err != nil {
					wg.logger.Error("WorkerHeartCheck Error", zap.Error(err), zap.Any("addr", w.Addr()))
					w.Available = false
				}
			}(w)
		}
		wag.Wait()
		wg.mutex.Unlock()
		time.Sleep(time.Minute)
	}
}

func (wg *WorkGroup) GetWorkerByAddr(addr string) (w *models.WorkerInfo) {
	wg.mutex.Lock()
	defer wg.mutex.Unlock()
	defer func() {
		wg.logger.Debug("GetWorkerByAddr",
			zap.Any("addr", addr),
			zap.Any("w", w))
	}()
	if addr == "" {
		return nil
	}

	w, ok := wg.workerInfo[addr]
	if ok {
		return w
	}
	return nil
}

func (wg *WorkGroup) getConnByAddr(addr string) *grpc.ClientConn {
	if addr == "" {
		return nil
	}

	c, ok := wg.conns[addr]
	if ok {
		return c
	}

	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	opts := []grpc.DialOption{
		grpc.WithInsecure(),
	}

	conn, err := grpc.DialContext(ctx, addr, opts...)
	if err != nil {
		pl_boot.AppCtx.GetAppLogger().Error("can't connect worker",
			zap.Any("addr", addr),
			zap.Error(err))
		return nil
	}

	wg.conns[addr] = conn
	return conn
}

// UpdateWorker
func (wg *WorkGroup) UpdateWorker(ip string, port int, baseInfo *api.WorkerBaseInfo, taskInfo *api.WorkerTaskInfo,
	isStke bool, region string,
) {
	wg.mutex.Lock()
	defer wg.mutex.Unlock()

	addr := fmt.Sprintf("%s:%d", ip, port)

	wi, ok := wg.workerInfo[addr]
	if !ok {
		wi = &models.WorkerInfo{
			Ip:   ip,
			Port: port,
			BaseInfo: models.WorkerBaseInfo{
				Cpu:       float64(baseInfo.Cpu),
				Memory:    float64(baseInfo.Memory),
				LastHeart: baseInfo.Timestamp,
			},
			TaskInfo:  &sync.Map{},
			Available: true,
			IsStke:    isStke,
			Region:    region,
		}
		if taskInfo.TaskId != "" {
			wi.TaskInfo.Store(taskInfo.TaskId, models.WorkerTaskInfo{
				SpeedInUse:         taskInfo.SpeedInUse,
				SpeedAllocated:     taskInfo.SpeedAllocated,
				FailRate:           float64(taskInfo.FailRate),
				TimeoutRate:        float64(taskInfo.TimeoutRate),
				ConcurrentFileNum:  taskInfo.ConcurrentFileNum,
				ConcurrentFileSize: taskInfo.ConcurrentFileSize,
				FragSet:            taskInfo.FragIdSet,
				LastHeart:          time.Now().Unix(),
			})
		}
	} else {
		wi.Available = true
		if wi.BaseInfo.LastHeart < baseInfo.Timestamp {
			wi.BaseInfo.LastHeart = baseInfo.Timestamp
			wi.BaseInfo.Cpu = float64(baseInfo.Cpu)
			wi.BaseInfo.Memory = float64(baseInfo.Memory)
		}

		t := models.WorkerTaskInfo{
			SpeedInUse:         taskInfo.SpeedInUse,
			SpeedAllocated:     taskInfo.SpeedAllocated,
			FailRate:           float64(taskInfo.FailRate),
			TimeoutRate:        float64(taskInfo.TimeoutRate),
			ConcurrentFileNum:  taskInfo.ConcurrentFileNum,
			ConcurrentFileSize: taskInfo.ConcurrentFileSize,
			FragSet:            taskInfo.FragIdSet,
			LastHeart:          time.Now().Unix(),
		}
		if taskInfo.TaskId != "" {
			wi.TaskInfo.Store(taskInfo.TaskId, t)
		}
	}

	wg.logger.Debug("UpdateWorker",
		zap.Any("addr", addr),
		zap.Any("w", wi))

	wg.workerInfo[addr] = wi
	return
}

// AvailableWorkers
func (wg *WorkGroup) AvailableWorkers() (res []*models.WorkerInfo, err error) {
	wg.mutex.Lock()
	defer wg.mutex.Unlock()
	defer func() {
		wg.logger.Debug("AvailableWorkers",
			zap.Any("res", res),
			zap.Error(err))
	}()
	res = make([]*models.WorkerInfo, 0)
	for _, w := range wg.workerInfo {
		if w.Available {
			res = append(res, w)
		}
	}

	return res, nil
}

// SetWorkerNotAvailable
func (wg *WorkGroup) SetWorkerNotAvailable(worker *models.WorkerInfo) {
	wg.mutex.Lock()
	defer wg.mutex.Unlock()

	if worker == nil {
		return
	}
	defer func() {
		wg.logger.Debug("SetWorkerNotAvailable",
			zap.Any("worker", worker))
	}()
	w, ok := wg.workerInfo[worker.Addr()]
	if !ok {
		return
	}

	w.Available = false
	return
}
