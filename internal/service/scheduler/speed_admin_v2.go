package scheduler

import (
	"context"
	"fmt"
	"math"
	"strings"
	"sync"
	"time"

	"git.code.oa.com/cloud-msp/migration-lister/api"
	"git.code.oa.com/cloud-msp/migration-lister/internal/service/deal"
	"git.code.oa.com/cloud-msp/migration-lister/internal/service/models"
	"git.code.oa.com/cloud-msp/migration-lister/pkg/conf"
	"git.code.oa.com/pulse-line/pl_boot"
	"github.com/duke-git/lancet/slice"
	"go.uber.org/zap"
)

// speed admin v2, 新版本的限速管理器，非主动分配，worker主动请求，master应答
var (
	SpeedAdminV2 *SpeedAllocatorV2
)

func initSAV2() {
	SpeedAdminV2 = &SpeedAllocatorV2{
		logger:        pl_boot.AppCtx.GetAppLogger().With(zap.String("name", "SpeedAdminV2")),
		jobSpeedAdmin: make(map[string]*SpeedRecordAdminV2),
	}
}

type SpeedAllocatorV2 struct {
	mu            sync.Mutex
	logger        *zap.Logger
	jobSpeedAdmin map[string]*SpeedRecordAdminV2 //第一层 JobId
}

type TaskWorkerSpeedRecordV2 struct {
	WorkerAddr     string
	WorkerRegion   string
	SpeedAllocated int64
	SpeedInUse     int64
	LastHeart      int64
}

type SpeedRecordAdminV2 struct {
	ctx          context.Context
	cancel       context.CancelFunc
	srs          map[string]*TaskWorkerSpeedRecordV2 // key->workerAddr
	jobSpeed     int64
	jobDstRegion string
	jobId        string
	logger       *zap.Logger
}

func (sa2 *SpeedAllocatorV2) AddTask(task *models.Task) {
	sa2.mu.Lock()
	defer sa2.mu.Unlock()

	admin, ok := sa2.jobSpeedAdmin[task.Id]
	if !ok {
		ctx, cancel := context.WithCancel(context.Background())
		admin = &SpeedRecordAdminV2{
			ctx:          ctx,
			cancel:       cancel,
			jobId:        task.Id,
			srs:          make(map[string]*TaskWorkerSpeedRecordV2),
			jobSpeed:     deal.NEWCroServiceDeal().GetSpeedLimit(task),
			jobDstRegion: *task.JobInfo.DstRegion,
			logger: pl_boot.AppCtx.GetAppLogger().With(
				zap.String("speedAdmin", "speedAdmin"),
				zap.String("jobId", task.Id),
			),
		}
		sa2.jobSpeedAdmin[task.Id] = admin
		admin.logger.Debug("addJobSpeedAdmin", zap.Any("task.Id", task.Id))
	} else {
		return
	}
}
func (sa2 *SpeedAllocatorV2) DelTask(taskId string) {
	sa2.mu.Lock()
	defer sa2.mu.Unlock()

	pl_boot.AppCtx.GetAppLogger().Debug("DelTask admin",
		zap.Any("taskId", taskId),
	)
	delete(sa2.jobSpeedAdmin, taskId)
	return
}
func (sa2 *SpeedAllocatorV2) UpdateTask(task *models.Task) {
	sa2.mu.Lock()
	defer sa2.mu.Unlock()

	admin, ok := sa2.jobSpeedAdmin[task.Id]
	if !ok {
		return
	}

	admin.jobSpeed = deal.NEWCroServiceDeal().GetSpeedLimit(task)
	return
}

func (sa2 *SpeedAllocatorV2) NextSpeed(request *api.NextSpeedRequest) (nextSpeed int64, err error) {
	// 查看是否是available worker, 如果不是，则直接回收速度
	if !wa.Available(fmt.Sprintf("%s:%d", request.WorkerIp, request.WorkerPort)) {
		sa2.logger.Warn("worker is not available now")
		return
	}

	sa2.mu.Lock()
	defer sa2.mu.Unlock()

	nextSpeed = sa2.allocate(request.JobId, fmt.Sprintf("%s:%d", request.WorkerIp, request.WorkerPort),
		request.AllocatedSpeed, request.RealSpeed, request.IsStke, request.Region,
	)
	return
}

// TODO 内部速度分配算法
// TODO worker的缩容保护接口
// TODO 标记worker的NotAvailable次数以作清理，可以用isStke来区分
func (sa2 *SpeedAllocatorV2) allocate(jobId string, addr string, allocated int64,
	inUsed int64, isStke bool, region string) int64 {
	// 1、必须有concurrent的历史记录
	// 2、必须有主动回收
	admin, ok := sa2.jobSpeedAdmin[jobId]
	if !ok {
		sa2.logger.Warn("noValidJobRecord", zap.Any("jobId", jobId))
		return 0
	}
	total := admin.jobSpeed
	srs := admin.srs

	// 第一步，回收所有not available的速度
	var allUsed int64 = 0
	var availableWorkerNum = 0
	for k, r := range srs {
		if !wa.Available(r.WorkerAddr) {
			admin.logger.Warn("worker is not available", zap.Any("addr", r.WorkerAddr))
			r.SpeedInUse = 0
			r.SpeedAllocated = 0
		} else {
			availableWorkerNum++
			if k != addr { // 排除自己
				allUsed = allUsed + r.SpeedAllocated
			}
		}
	}

	canBeUsed := total - allUsed
	if canBeUsed < 0 {
		canBeUsed = 0
	}
	sa2.logger.Info("allocateDetail",
		zap.Any("total", total),
		zap.Any("inUsed", inUsed),
		zap.Any("allUsed", allUsed),
		zap.Any("canBeUsed", canBeUsed),
		zap.Any("jobId", jobId),
	)

	// 第二步，判断本次请求的worker速度使用情况，<0.8, 回收；0.8<=x<0.9 不变；>=0.9, 扩大20%
	wr, ok := srs[addr]
	if !ok {
		wr = &TaskWorkerSpeedRecordV2{
			WorkerAddr:     addr,
			WorkerRegion:   region,
			SpeedAllocated: 0,
			SpeedInUse:     0,
			LastHeart:      0,
		}
		srs[addr] = wr
	}

	if wr.SpeedAllocated == 0 {
		// 初次分配，直接分配剩余全部，但不能超过平均值或10%
		var allocatedWorkerNum int64 = int64(availableWorkerNum)
		if allocatedWorkerNum < 10 {
			allocatedWorkerNum = 10
		}
		averageSpeed := total / allocatedWorkerNum
		wr.SpeedAllocated = canBeUsed
		if wr.SpeedAllocated > averageSpeed {
			wr.SpeedAllocated = averageSpeed
		}
		wr.LastHeart = time.Now().Unix()

		sa2.logger.Info("firstAllocateSpeed", zap.Any("jobId", jobId),
			zap.Any("availableWorkerNum", availableWorkerNum), zap.Any("allocatedWorkerNum", allocatedWorkerNum),
			zap.Any("total", total), zap.Any("averageSpeed", averageSpeed),
			zap.Any("canBeUsed", canBeUsed), zap.Any("realSpeed", wr.SpeedAllocated))
	} else {
		rate := float64(inUsed) / float64(wr.SpeedAllocated)
		if rate >= 0.9 { // 提升速度
			improvedSpeed := int64(math.Ceil(float64(wr.SpeedAllocated) * 0.1))
			wr.SpeedAllocated += improvedSpeed

			// 超过限速值，且低于平均值，则从其它人那里偷一点
			if wr.SpeedAllocated > canBeUsed {
				var stealSuccess bool = false
				if availableWorkerNum >= 2 {
					averageSpeed := total / int64(availableWorkerNum)
					if wr.SpeedAllocated < averageSpeed {
						for k, r := range srs {
							if k != addr && r.SpeedAllocated > averageSpeed+improvedSpeed {
								r.SpeedAllocated -= improvedSpeed
								stealSuccess = true
								break
							}
						}
					}
				}

				// 不能超过阈值
				if stealSuccess {
					wr.SpeedAllocated = canBeUsed + improvedSpeed
				} else {
					wr.SpeedAllocated = canBeUsed
				}
			}
		} else { // 保持或降低速度
			if rate < 0.8 {
				// 降低速度
				wr.SpeedAllocated = int64(math.Ceil(float64(wr.SpeedAllocated) * 0.9))
			} else {
				// 保持速度
			}

			// 不能超过阈值
			if wr.SpeedAllocated > canBeUsed {
				wr.SpeedAllocated = canBeUsed
			}
		}

		wr.SpeedInUse = inUsed
		wr.LastHeart = time.Now().Unix()
	}

	// 最小分配10Mbps
	if wr.SpeedAllocated < conf.ListerConfig.GlobalConf.SpeedPodUnit {
		wr.SpeedAllocated = conf.ListerConfig.GlobalConf.SpeedPodUnit
	}

	// Stke最大分配500Mbps
	if isStke {
		if wr.SpeedAllocated > conf.ListerConfig.GlobalConf.DefaultMaxStkeSpeed {
			wr.SpeedAllocated = conf.ListerConfig.GlobalConf.DefaultMaxStkeSpeed
		}
	}
	return wr.SpeedAllocated
}

func (sa2 *SpeedAllocatorV2) ExecutedJobInfo(request *api.ReceiveExecutedJobInfoRequest) (jobInfo map[string]int64,
	err error) {
	// 查看是否是available worker, 如果不是，则直接回收速度
	if !wa.Available(fmt.Sprintf("%s:%d", request.WorkerIp, request.WorkerPort)) {
		sa2.logger.Warn("worker is not available now")
		return
	}

	sa2.mu.Lock()
	defer sa2.mu.Unlock()

	allJobs := sa2.getAllJob()
	availableJobs := make([]string, 0)
	// 做地域匹配，worker的地域需要与目标桶匹配。如果worker没有地域，则意味着可以接受所有任务。
	if request.Region != "" {
		// 支持worker配置多个region, 例如 ap-beiing|ap-shanghai|ap-chengdu
		regions := strings.Split(request.Region, "|")
		sa2.logger.Info("worker regions info",
			zap.String("worker", fmt.Sprintf("%s:%d", request.WorkerIp, request.WorkerPort)),
			zap.String("raw region", request.Region),
			zap.Any("regions", regions))

		for _, region := range regions {
			jobs, ok := allJobs[region]
			if !ok {
				// 不存在对应地域的job，不处理
				sa2.logger.Info("no corresponding region job",
					zap.Any("region", region))
			} else {
				availableJobs = append(availableJobs, jobs...)
			}
		}
	} else {
		// 没有地域，可以接受所有地方的任务
		for _, jobs := range allJobs {
			availableJobs = append(availableJobs, jobs...)
		}
	}

	sa2.logger.Info("availableJobs",
		zap.Any("availableJobs", availableJobs), zap.Any("request", request))

	// 与现有的job做个diff，差额即是可以分配速度的job. 可以预分配一些速度
	curJobs := request.CurrentJobIds
	diffInterface := slice.Difference(availableJobs, curJobs)
	diff := diffInterface.([]string)
	// 分配速度
	jobInfo = make(map[string]int64)
	for _, jobId := range diff {
		speed := sa2.allocate(jobId, fmt.Sprintf("%s:%d", request.WorkerIp, request.WorkerPort),
			0, 0, request.IsStke, request.Region,
		)
		jobInfo[jobId] = speed
	}
	return jobInfo, nil
}

func (sa2 *SpeedAllocatorV2) getAllJob() map[string][]string { //map[region][]{jobIds}
	result := make(map[string][]string)
	for jobId, jobAdmin := range sa2.jobSpeedAdmin {
		region := jobAdmin.jobDstRegion
		if sl, ok := result[region]; ok {
			sl = append(sl, jobId)
			result[region] = sl
		} else {
			sl = make([]string, 0)
			sl = append(sl, jobId)
			result[region] = sl
		}
	}
	sa2.logger.Info("getAllJobInfo", zap.Any("result", result))
	return result
}
