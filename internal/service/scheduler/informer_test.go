package scheduler

import (
	"fmt"
	"git.code.oa.com/cloud-msp/migration-lister/api"
	"git.code.oa.com/cloud-msp/migration-lister/internal/service/models"
	"git.code.oa.com/pulse-line/pl_boot"
	"google.golang.org/grpc"
	"testing"
)

func boot() {
	boot := pl_boot.NewBootstrapper()
	boot.RegisterAppContextWithConfigPath("../../configs", "test")
}

func TestWorkerGroup(t *testing.T) {
	boot()
	wa = &WorkGroup{
		workerInfo: make(map[string]*models.WorkerInfo),
		conns:      make(map[string]*grpc.ClientConn),
		logger:     pl_boot.AppCtx.GetAppLogger(),
	}

	wa.UpdateWorker("1", 0, &api.WorkerBaseInfo{}, &api.WorkerTaskInfo{}, true, "")
	wa.UpdateWorker("2", 1, &api.WorkerBaseInfo{}, &api.WorkerTaskInfo{}, false, "")
	fmt.Println(wa.AvailableWorkers())
	as, err := wa.AvailableWorkers()
	if err != nil {
		t.Fatal(err)
	}

	if len(as) != 2 {
		t.Fatal("as", as)
	}

	wa.SetWorkerNotAvailable(as[1])
	res, _ := wa.NotAvailableWorkers()
	fmt.Println("not ", res[0])
	res2, _ := wa.AvailableWorkers()
	fmt.Println("yes", res2[0])
}
