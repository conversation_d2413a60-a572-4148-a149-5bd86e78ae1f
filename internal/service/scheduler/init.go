package scheduler

import (
	"git.code.oa.com/cloud-msp/migration-lister/internal/service/models"
	workerapi "git.code.oa.com/cloud-msp/migration-worker/api"
	"git.code.oa.com/pulse-line/pl_boot"
	"google.golang.org/grpc"
	"sync"
)

var (
	// 全局使用
	wa   *WorkGroup
	once sync.Once
	Sch  Scheduler // 导出
)

func Init() {
	once.Do(func() {
		wa = &WorkGroup{
			workerInfo:   make(map[string]*models.WorkerInfo),
			conns:        make(map[string]*grpc.ClientConn),
			workerClient: make(map[string]workerapi.WorkerServiceClient),
			logger:       pl_boot.AppCtx.GetAppLogger(),
		}
		initSAV2()
		Sch = newSch()
		go wa.WorkerHeartCheck()
	})
}
