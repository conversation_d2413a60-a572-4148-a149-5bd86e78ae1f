package scheduler

import (
	"git.code.oa.com/cloud-msp/migration-lister/internal/service/models"
	"git.code.oa.com/pulse-line/pl_boot"
	"go.uber.org/zap"
	"math/rand"
	"time"
)

// 实际的调度算法
// frag分配算法 PID 多目标
func fragPickWorker(frag *models.Frag) (w *models.WorkerInfo) {
	var logger = pl_boot.AppCtx.GetAppLogger().With(
		zap.String("jobId", frag.BaseInfo.TaskId),
		zap.Int64("fragId", frag.BaseInfo.FragId),
	)

	defer func() {
		if w != nil {
			logger.Debug("fragPickWorker", zap.Any("w", w.Addr()))
		}
	}()
	//files := frag.BaseInfo.Files
	//fileNum := len(files)
	//var fileSize = int64(0)
	//for _, file := range files {
	//	fileSize += file.Size
	//}

	workers, _ := wa.AvailableWorkers()
	if len(workers) == 0 {
		logger.Warn("AvailableWorkers is Zero")
		return nil
	}

	// 筛选出speed 不为0，并且负载最低的w
	var round1 = make([]*models.WorkerInfo, 0)
	for _, ww := range workers {
		if ww.IsStke {
			// stke 主动消费，不用被动派发；非stke的既可以被主动派发，也可以主动请求
			continue
		}
		info, ok := ww.GetWorkerTaskInfoByTaskId(frag.BaseInfo.TaskId)
		if !ok {
			continue
		} else {
			if info.SpeedAllocated > 0 {
				round1 = append(round1, ww)
			}

		}
	}

	if len(round1) == 0 {
		logger.Warn("AvailableWorkers is Zero")
		return nil
	}

	// TODO 计算frag和worker的资源匹配度
	// 暂时先从round1里继续筛选, 随机
	rand.Seed(time.Now().Unix())
	n := rand.Int() % len(round1)
	return round1[n]
}
