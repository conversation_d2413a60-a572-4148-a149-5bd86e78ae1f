package scheduler

import (
	"context"
	"fmt"
	"runtime/debug"
	"sync"
	"time"

	"git.code.oa.com/cloud-msp/migration-lister/api"
	"git.code.oa.com/cloud-msp/migration-lister/internal/service/models"
	"git.code.oa.com/cloud-msp/migration-lister/pkg/conf"
	"git.code.oa.com/cloud-msp/migration-lister/pkg/metrics"
	"git.code.oa.com/cloud-msp/migration-lister/tools"
	workerapi "git.code.oa.com/cloud-msp/migration-worker/api"
	"git.code.oa.com/pulse-line/pl_boot"
	"git.code.oa.com/pulse-line/pl_prom"
	"go.uber.org/atomic"
	"go.uber.org/zap"
)

type Scheduler interface {
	commitFrag(ctx context.Context, frag *models.Frag) error
	addTask(ctx context.Context, task *models.Task) error
	delTask(ctx context.Context, taskId string) error
	updateTask(ctx context.Context, task *models.Task) error
	// consumerFrag(ctx context.Context, taskId string)
	ConsumerFragByApi(ctx context.Context, taskId string) (*api.Frag, error)
	DumpFrags(taskId string) []*models.Frag
}

// 调度流水线
// frag生成后是进入本地待调度队列
// 调度器单线程调度消费队列
// 从informer打分，选取一个合适的派发过去
// 另一个线程计算informer, 用来做speed分配算法，从而解耦frag和speed分配
// 实际上执行两个调度的控制，一个是frag, 一个是speed
type FragAndSpeedScheduler struct {
	tasks         map[string]*models.Task
	pQueue        map[string]*queueAdmin // 优先级队列, 用于调度frag
	fragInterval  int                    // frag调度时间间隔 接近实时
	speedInterval int                    // speed调度时间间隔
	logger        *zap.Logger
	sa            SpeedAdmin // 用于speed的控制和计算
	mu            sync.Mutex
}

type queueAdmin struct {
	q      *tools.PriorityQueue
	cancel context.CancelFunc
}

// TODO
func (f *FragAndSpeedScheduler) ConsumerFragByApi(ctx context.Context, taskId string) (*api.Frag, error) {
	f.mu.Lock()
	pq, ok := f.pQueue[taskId]
	f.mu.Unlock()
	if !ok || pq == nil {
		return nil, fmt.Errorf("scheduler queue is nil")
	}
	rawFrag, err := pq.q.Pop()
	if err != nil {
		if err.Error() == "empty queue" {
			f.logger.Info("scheduler queue is empty, no frags to consume", zap.String("taskId", taskId))
			return nil, nil
		} else {
			f.logger.Error("consume frag err", zap.Error(err), zap.String("taskId", taskId))
		}

		return nil, err
	}

	frag := rawFrag.(*models.Frag)
	// 转换一次成api.Frag
	res := &api.Frag{
		TaskId:   frag.BaseInfo.TaskId,
		FragId:   frag.BaseInfo.FragId,
		Files:    nil,
		RetryNum: frag.RuntimeInfo.Retry.Load(),
	}

	for _, file := range frag.BaseInfo.Files {
		sf := &api.SendFile{
			Path:     file.FullPath,
			Size:     file.Size,
			UploadId: file.UploadId,
		}
		res.Files = append(res.Files, sf)
		res.ScanSize = res.ScanSize + file.Size
	}
	return res, nil
}

func (f *FragAndSpeedScheduler) commitFrag(ctx context.Context, frag *models.Frag) error {
	// 目前都按1算
	realFrag := &models.Frag{
		BaseInfo: &models.FragBaseInfo{
			TaskId: frag.BaseInfo.TaskId,
			FragId: frag.BaseInfo.FragId,
			Files:  make([]*api.File, 0),
		},
		RuntimeInfo: &models.FragRuntimeInfo{
			FirstHeart: &atomic.Value{},
			LastHeart:  &atomic.Value{},
			Retry:      atomic.NewInt64(frag.RuntimeInfo.Retry.Load()),
		},
		Over: atomic.NewBool(frag.Over.Load()),
	}
	realFrag.RuntimeInfo.FirstHeart.Store(frag.RuntimeInfo.FirstHeart.Load())
	realFrag.RuntimeInfo.LastHeart.Store(frag.RuntimeInfo.LastHeart.Load())
	for _, f := range frag.BaseInfo.Files {
		file := &api.File{
			FullPath:     f.FullPath,
			Size:         f.Size,
			CurTime:      f.CurTime,
			FailedReason: f.FailedReason,
			UploadId:     f.UploadId,
		}
		realFrag.BaseInfo.Files = append(realFrag.BaseInfo.Files, file)
	}

	for {
		f.mu.Lock()
		pq, ok := f.pQueue[frag.BaseInfo.TaskId]
		f.mu.Unlock()
		if !ok || pq == nil {
			return fmt.Errorf("scheduler queue is nil")
		}
		if pq.q.Len() >= conf.ListerConfig.GlobalConf.MaxFragNumInQueue {
			time.Sleep(10 * time.Millisecond)
			continue
		}
		pq.q.Insert(realFrag, 1)
		break
	}
	return nil
}

func (f *FragAndSpeedScheduler) DumpFrags(taskId string) (frags []*models.Frag) {
	f.mu.Lock()
	pq, ok := f.pQueue[taskId]
	f.mu.Unlock()

	if ok {
		rawFrags := pq.q.DumpItems()
		for _, frag := range rawFrags {
			realFrag := frag.(*models.Frag)
			frags = append(frags, realFrag)
		}
	}
	return
}

func (f *FragAndSpeedScheduler) addTask(ctx context.Context, task *models.Task) error {
	f.mu.Lock()
	_, ok := f.tasks[task.Id]
	if !ok {
		_, cancel := context.WithCancel(ctx)
		f.pQueue[task.Id] = &queueAdmin{
			q:      tools.New(),
			cancel: cancel,
		}
		// ctx, cancel := context.WithCancel(ctx)
		// go f.consumerFrag(ctx, task.Id)
	}
	f.tasks[task.Id] = task
	f.mu.Unlock()

	f.sa.AddTask(task)
	return nil
}

func (f *FragAndSpeedScheduler) delTask(ctx context.Context, taskId string) error {
	f.mu.Lock()
	delete(f.tasks, taskId)
	pq, ok := f.pQueue[taskId]
	if ok && pq != nil {
		pq.cancel()
	}
	delete(f.pQueue, taskId)
	f.mu.Unlock()
	f.sa.DelTask(taskId)
	f.logger.Debug("delTask", zap.Any("taskId", taskId))

	return nil
}

func (f *FragAndSpeedScheduler) updateTask(ctx context.Context, task *models.Task) error {
	f.mu.Lock()
	f.tasks[task.Id] = task
	f.mu.Unlock()

	f.sa.UpdateTask(task)
	return nil
}

// warning: 不需要主动发送frag到worker，如果后续这里要打开则commitFrag放回队列时，
// 要执行frag.InitLastHeart()，且TasksAdmin和FragAndSpeedScheduler的队列都执行
func (f *FragAndSpeedScheduler) consumerFrag(ctx context.Context, taskId string) {
	var wg sync.WaitGroup
	wg.Add(1)
	for i := 0; i < 1; i++ {
		go func(ctx context.Context) {
			defer func() {
				if err := recover(); err != nil {
					f.logger.Error("RunWithRecover Panic Trigger",
						zap.Any("err", err), zap.Any("stack", string(debug.Stack()[:])))
				}
			}()
			defer wg.Done()
			f.mu.Lock()
			pq, ok := f.pQueue[taskId]
			f.mu.Unlock()
			if !ok || pq == nil {
				f.logger.Warn("scheduler queue is nil", zap.Any("taskId", taskId))
				return
			}
			for {
				select {
				case <-ctx.Done():
					f.logger.Warn("ctx canceled", zap.Any("taskId", taskId))
					return
				default:
				}
				rawFrag, err := pq.q.Pop()
				if err != nil {
					if err.Error() == "empty queue" {
						f.logger.Debug("scheduler queue is empty, no frags to consume", zap.Any("taskId", taskId))
					} else {
						f.logger.Error("consume frag error", zap.Error(err), zap.Any("taskId", taskId))
					}

					time.Sleep(time.Millisecond * time.Duration(f.fragInterval))
					continue
				}

				frag := rawFrag.(*models.Frag)
				var errCnt = 0
				for {
					w := fragPickWorker(frag)
					if w != nil {
						f.logger.Debug("fragPickWorkerResult",
							zap.Any("fragId", frag.BaseInfo.FragId),
							zap.Any("w", w.Addr()),
							zap.Any("taskId", taskId),
						)
						err = f.sendFrag(frag, w)
						if err != nil {
							// 大于三次直接扔回队列重试
							if errCnt++; errCnt > 2 {
								_ = f.commitFrag(context.Background(), frag)
								time.Sleep(time.Millisecond * time.Duration(f.fragInterval))
							} else {
								time.Sleep(time.Millisecond * time.Duration(f.fragInterval))
								continue
							}
						}
					} else {
						f.logger.Debug("fragPickWorkerResult is nil",
							zap.Any("fragId", frag.BaseInfo.FragId),
							zap.Any("taskId", taskId),
						)
						// 没有合适的worker, 直接扔回队列
						_ = f.commitFrag(context.Background(), frag)
						time.Sleep(time.Millisecond * time.Duration(f.fragInterval))
					}
					break
				}
			}
		}(ctx)
	}
	wg.Wait()
}

// 对上层开放3个接口
func CommitFrag(ctx context.Context, frag *models.Frag) error {
	return Sch.commitFrag(ctx, frag)
}

// AddTask
func AddTask(ctx context.Context, task *models.Task) error {
	return Sch.addTask(ctx, task)
}

// DelTask
func DelTask(ctx context.Context, taskId string) error {
	return Sch.delTask(ctx, taskId)
}

// UpdateTask
func UpdateTask(ctx context.Context, task *models.Task) error {
	return Sch.updateTask(ctx, task)
}

func DumpFrags(taskId string) []*models.Frag {
	return Sch.DumpFrags(taskId)
}

// New
func newSch() Scheduler {
	s := &FragAndSpeedScheduler{}
	s.logger = pl_boot.AppCtx.GetAppLogger()
	s.pQueue = make(map[string]*queueAdmin)
	s.fragInterval = conf.ListerConfig.GlobalConf.FragInterval
	s.speedInterval = conf.ListerConfig.GlobalConf.SpeedInterval
	s.tasks = make(map[string]*models.Task)
	if conf.ListerConfig.GlobalConf.SpeedVersion == 0 {
		s.sa = &SpeedAllocator{
			jobSpeedAdmin: make(map[string]*SpeedRecordAdmin),
		}
	} else {
		s.sa = SpeedAdminV2
	}

	return s
}

func (f *FragAndSpeedScheduler) sendFrag(frag *models.Frag, w *models.WorkerInfo) (err error) {
	logger := pl_boot.AppCtx.GetAppLogger().With(zap.String("jobId", frag.BaseInfo.TaskId),
		zap.Int64("fragId", frag.BaseInfo.FragId))
	logger.Debug("sendFrag", zap.Any("w", w.Addr()))

	req := &workerapi.FragRequest{
		TaskId:   frag.BaseInfo.TaskId,
		FragId:   frag.BaseInfo.FragId,
		RetryNum: frag.RuntimeInfo.Retry.Load(),
	}

	files := make([]*workerapi.File, 0, len(frag.BaseInfo.Files))
	for _, v := range frag.BaseInfo.Files {
		f := &workerapi.File{
			Path: v.FullPath,
			Size: v.Size,
		}
		req.ScanSize += v.Size
		files = append(files, f)
	}
	req.Files = files

	start := time.Now() // 记录总耗时

	// 判断一次task状态，如果task已结束，则此处退出即可 // 验证frag所属task是否还存在调度，不存在的话就退出
	f.mu.Lock()
	if _, ok := f.tasks[frag.BaseInfo.TaskId]; !ok {
		logger.Info("no valid match task")
		f.mu.Unlock()
		return
	}
	f.mu.Unlock()

	client := wa.GetWorkerClientByAddr(w.Addr())
	cLogger := logger.With(
		zap.String("worker.ip", w.Ip), zap.Int("worker.port", w.Port))
	var flag = false
	var resp *workerapi.FragResponse
	for i := 0; i < 2; i++ {
		ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
		rpcStart := time.Now() // rpc 开始时间
		resp, err = client.SendFrag(ctx, req)
		rpcCost := time.Now().Sub(rpcStart).Seconds()

		cLogger.Info("SedFrag rpc request", zap.Float64("cost", rpcCost))
		pl_prom.GetHistogramWithValues(
			metrics.RpcHistogram, metrics.Lister, "SendFrag", frag.BaseInfo.TaskId, "").Observe(rpcCost)

		if err != nil {
			cLogger.Warn("SendFrag failed", zap.Error(err), zap.Any("w", w.Addr()))
			pl_prom.GetCounterWithValues(metrics.ErrorCount, metrics.Lister, "sendFrag", frag.BaseInfo.TaskId, "")
			time.Sleep(5 * time.Second)
			cancel()
			continue
		}

		if resp == nil {
			cLogger.Warn("SendFrag failed, nil resp", zap.Any("w", w.Addr()))
			cancel()
			continue
		}

		if resp.IsReceived { // 成功收到，则跳出
			timeElapsed := time.Now().Sub(start).Seconds()
			cLogger.Info(
				"SendFrag succeeded", zap.Float64("cost", timeElapsed))
			cancel()
			err = nil
			return
		}

		cancel()
		cLogger.Warn(
			"SendFrag is rejected", zap.String("reason", resp.RejectReason))
		err = fmt.Errorf(resp.RejectReason)
		flag = true
		time.Sleep(5 * time.Second)
		continue
	}
	if !flag {
		wa.SetWorkerNotAvailable(w)
	}
	return
}
