package tasks

import (
	"testing"
	"time"

	"git.code.oa.com/cloud-msp/migration-lister/internal/service/models"
	"git.code.oa.com/cloud-msp/migration-lister/pkg/lister"
	"git.code.oa.com/cloud-msp/migration-lister/tools"
)

func Test_GetTimeSpanFilter(t *testing.T) {
	var hasTimeFilter int32 = 1
	var fileEndTime int32 = 1616342400
	var fileStartTime int32 = 1616169600
	jobInfo := &tools.JobInfo{HasFileTimeFilter: &hasTimeFilter, FileEndTime: &fileEndTime,
		FileStartTime: &fileStartTime}

	task := models.NewTask()
	task.JobInfo = jobInfo

	if !task.HasTimeSpanFilter() {
		t.Error("should have time span filter")
	}
	t.Log("task have time span filter")

	filter := GetTimeSpanFilter(task)
	file := lister.Content{LastModified: time.Now()}
	if pass := filter(&file); pass {
		t.Error("shall not pass")
	}
}
