package tasks

import (
	jsoniter "github.com/json-iterator/go"
	"sync"
)

// 全局单例task管理器
var (
	taskLister *TasksAdmin
	json       = jsoniter.ConfigCompatibleWithStandardLibrary
	once       sync.Once
)

func init() {
	once.Do(
		func() {
			taskLister = &TasksAdmin{
				tasks: &sync.Map{}, // make(map[string]*tasks.Task),
				// TODO: 未完成分片是否应该移动到 task 对象上 隔离 task 之间的数据
				unfinishedFrags: &sync.Map{},       // make(map[string]map[int64]*Frag),
				cancelFs:        newStrCancelMap(), // jobId string: cancel context.CancelFunc
			}
		},
	)
}
