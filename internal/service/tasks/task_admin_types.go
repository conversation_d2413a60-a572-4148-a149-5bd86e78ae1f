package tasks

import (
	"context"
	"sync"

	"git.code.oa.com/cloud-msp/migration-lister/api"
	"git.code.oa.com/cloud-msp/migration-lister/internal/service/models"
	"go.uber.org/zap"
)

// 任务管理器，全局单例使用
type TaskAdminInterface interface {
	// 添加 task, cancelF, 和空的 unfinishedFrag
	AddTask(task *models.Task, cancel context.CancelFunc)
	// 用于jobCron定时获取Job状态后更新这个管理器，参考py update_task
	// 没更新 task 携带的计数
	UpdateTask(task *models.Task, logger *zap.Logger)
	// judge_frag_heart_timing 定时遍历判断管理任务池中的分片状态，以作错误记录还是超时重新下发等操作
	// 更新分片心跳信息，参考update_frag_heart
	UpdateFragHeart(taskId string, fragId int64, logger *zap.Logger)
	// add_frag_result, 记录分片结果，处理结果回调, 包括job的状态，进度，错误文件上传等
	AddFragResult(result *models.FragResult, logger *zap.Logger) bool
	// 获取一个任务的未完成分片集合
	GetUnfinishedFragSet(taskId string) (*sync.Map, error)
	// 为一个任务加入未完成分片集合
	// NOTE: 只有在任务加入 taskAdmin 的时候调用一次
	AddUnfinishedFragSet(taskId string, unfinishedFragSet *sync.Map)
	// getTask 内存中的 task
	GetTask(taskId string, logger *zap.Logger) (task *models.Task)
	// 获取 task 对应的 cancel function
	GetCancelFunc(taskId string) (context.CancelFunc, bool)
	// 删除task
	RemoveTask(taskId string)
	// 获取所有本地task
	GetAllTasks() map[string]*models.Task
	// 快速遍历
	QuickList(ctx context.Context, taskId string)
	// 慢速遍历
	SlowList(ctx context.Context, taskId string)
	// FillJobInfo
	FillJobInfo(info *api.JobInfo, task *models.Task)
	// 发送任务finalize请求
	NotifyTaskFinalize(task *models.Task, state api.TaskState, stopReason string, logger *zap.Logger)
	// 从内存中排空 task
	// NOTE: 需要满足幂等
	//  删除task, 取消 ctx, 删除未完成分片
	//  删除令牌桶
	PruneTask(taskId string)
	// 单个任务分片心跳
	FragHBCron(ctx context.Context, taskId string) error
	// 重发超时的分片
	RedistributeTimeoutFrags(ctx context.Context, taskId string) error
}

// jobId 和 context.Cancel 对应关系
type strCancelMap struct {
	sm sync.Map
}

func newStrCancelMap() *strCancelMap {
	return &strCancelMap{}
}

// 删除一对 jobId: context.CancelFunc
func (m *strCancelMap) Delete(jobId string) {
	m.sm.Delete(jobId)
}

// 查找 jobId 对应的 context.CancelFunc
func (m *strCancelMap) Load(jobId string) (cancel context.CancelFunc, ok bool) {
	v, ok := m.sm.Load(jobId)
	if v != nil {
		cancel = v.(context.CancelFunc)
	}
	return
}

// 添加一对 jobId: context.CancelFunc, 如果 jobId 已存在, 返回已有值
func (m *strCancelMap) LoadOrStore(jobId string, cancel context.CancelFunc) (actual context.CancelFunc, loaded bool) {
	a, loaded := m.sm.LoadOrStore(jobId, cancel)
	actual = a.(context.CancelFunc)
	return
}

// 遍历 map, 每一对 jobId: context.CancelFunc 作为参数调用 f
func (m *strCancelMap) Range(f func(jobId string, cancel context.CancelFunc) bool) {
	wrap := func(jobId, cancel interface{}) bool {
		return f(jobId.(string), cancel.(context.CancelFunc))
	}
	m.sm.Range(wrap)
}

// 添加一对 jobId: context.CancelFunc
func (m *strCancelMap) Store(jobId string, cancel context.CancelFunc) {
	m.sm.Store(jobId, cancel)
}
