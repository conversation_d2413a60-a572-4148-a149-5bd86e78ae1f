package tasks

import (
	"context"
	"git.code.oa.com/cloud-msp/migration-lister/internal/service/models"
	"testing"

	"git.code.oa.com/cloud-msp/migration-lister/api"
	"git.code.oa.com/cloud-msp/migration-lister/internal/service/apicall"
	"git.code.oa.com/cloud-msp/migration-lister/internal/service/coscall"
	"git.code.oa.com/cloud-msp/migration-lister/internal/service/mock"
	"git.code.oa.com/pulse-line/pl_boot"
	"github.com/golang/mock/gomock"
	"github.com/prashantv/gostub"
	"go.hlw.tencent.com/alego/components/log"
)

func boot() {
	boot := pl_boot.NewBootstrapper()
	boot.RegisterAppContextWithConfigPath("../../configs", "test")
}

// 删除内存中的 task
func Test_taskAdmin_RemoveTask(t *testing.T) {
	boot()

	jobId := "to-be-closed"

	taskAdmin := GetTaskAdmin()
	taskCtx, cancel := context.WithCancel(context.Background())
	task := models.NewTask()
	task.Id = jobId
	// 添加新任务
	taskAdmin.AddTask(task, cancel)
	taskAdmin.RemoveTask(jobId)

	// 任务被结束并清理以后, ctx 应该被取消
	err := taskCtx.Err()
	if err != context.Canceled {
		t.Errorf("taskCtx should be canceled, got∞%v", err)
	}

	_, ok := taskAdmin.GetCancelFunc(jobId)
	if ok {
		t.Errorf("taskCtx cancel func should have been removed")
	}
}

// 多次调用 RemoveTask 清理相同 task 不应该引发问题
// NOTE: 直接运行因为 pl 无法获取 appCtx.logger 会导致失败, 需要先删除 RemoveTask 方法里的 log
func Test_taskAdmin_RemoveTask_multi_times(t *testing.T) {
	boot()

	jobId := "to-be-closed"
	taskAdmin := GetTaskAdmin()
	taskCtx, cancel := context.WithCancel(context.Background())
	task := models.NewTask()
	task.Id = jobId
	// 添加新任务
	taskAdmin.AddTask(task, cancel)
	taskAdmin.RemoveTask(jobId)
	taskAdmin.RemoveTask(jobId)

	// 任务被结束并清理以后, ctx 应该被取消
	err := taskCtx.Err()
	if err != context.Canceled {
		t.Errorf("taskCtx should be canceled, got∞%v", err)
	}

	_, ok := taskAdmin.GetCancelFunc(jobId)
	if ok {
		t.Errorf("taskCtx cancel func should have been removed")
	}
}

func equals(fragA, fragB *models.Frag) bool {
	a := fragA.BaseInfo
	b := fragB.BaseInfo
	if a == nil || b == nil {
		return false
	}

	if a.TaskId != b.TaskId {
		return false
	}
	if a.FragId != b.FragId {
		return false
	}
	if len(a.Files) != len(b.Files) {
		return false
	}
	for i := 0; i < len(a.Files); i++ {
		if a.Files[i].FullPath != b.Files[i].FullPath {
			return false
		}
	}
	return true
}

// 删除未完成分片
func Test_taskAdmin_unfinishedFrag_delete(t *testing.T) {
	logger := log.GetLogger()

	GetTaskAdmin()
	taskId := "task1"
	var fragId int64 = 1
	// 删除不存在的分片不应该 panic
	taskLister.deleteUnfinishedFrag(taskId, fragId, logger)

	task2frag2 := &models.Frag{
		BaseInfo: &models.FragBaseInfo{
			TaskId: taskId,
			FragId: fragId,
			Files:  []*api.File{},
		},
	}
	taskLister.addUnfinishedFrag(task2frag2, logger)
	frag := taskLister.getUnfinishedFrag(taskId, fragId, logger)
	if frag == nil {
		t.Errorf("should get non-nil frag")
	}

	taskLister.deleteUnfinishedFrag(taskId, fragId, logger)
	frag = taskLister.getUnfinishedFrag(taskId, fragId, logger)
	if frag != nil {
		t.Errorf("should get nil frag")
	}
}

// 失败文件上报不成功不会清理 unfinished frag
// 先 mock 再 stub 很难搞
func Test_taskAdmin_AddFragResult_failed(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	// setup mock
	var getMspCosCallStub = func() coscall.MspCosCallIF {
		m := mock.NewMockMspCosCallIF(ctrl)
		// 总是返回失败
		m.EXPECT().ReportFailedFiles(gomock.Any()).Return(false)
		return m
	}

	stubs := gostub.Stub(&coscall.GetMspCosCall, getMspCosCallStub)
	defer stubs.Reset()

	boot()
	logger := log.GetLogger()
	taskAdmin := GetTaskAdmin()

	// 加一个任务到 taskAdmin
	task1 := models.NewTask()
	task1.Id = "task1"
	_, cancel1 := context.WithCancel(context.Background())
	defer cancel1()

	taskAdmin.AddTask(task1, cancel1)

	// 加一个 unfinished frag
	task1frag1 := &models.Frag{
		RuntimeInfo: models.NewFragRuntimeInfo(),
		BaseInfo: &models.FragBaseInfo{
			TaskId: "task1",
			FragId: 1,
		},
	}
	taskLister.addUnfinishedFrag(task1frag1, logger)

	// 添加 frag result
	fragResult := models.ConstructFragResult(task1frag1)
	fragResult.BaseInfo.SuccessfulSize = 1

	taskAdmin.AddFragResult(fragResult, logger)
	// 应该因为上报失败文件调用失败没加上
	_, ok := task1.FragIdSet.Load(1)
	if ok {
		t.Fatal("frag result should have been dropped due to failFail report failure")
	}

	// unfinished 仍然保留
	unfinishedFrag := taskLister.getUnfinishedFrag("task1", 1, logger)
	if unfinishedFrag == nil {
		t.Fatal("unfinished frag should remain")
	}

}

// 上报成功累加计数清理 unfinished frag
func Test_taskAdmin_AddFragResult(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	// setup mock
	// NOTE: test use only
	var getMspCosCallStub = func() coscall.MspCosCallIF {
		m := mock.NewMockMspCosCallIF(ctrl)
		m.EXPECT().ReportFailedFiles(gomock.Any()).Return(true)
		return m
	}

	var getApiCallStub = func() apicall.ApiCallIf {
		m := mock.NewMockApiCallIf(ctrl)
		m.EXPECT().ReportTaskProgress(gomock.Any(), gomock.Any())
		return m
	}

	stubs := gostub.Stub(&coscall.GetMspCosCall, getMspCosCallStub)
	stubs.Stub(&apicall.GetApiCall, getApiCallStub)
	defer stubs.Reset()

	boot()
	logger := log.GetLogger()
	taskAdmin := GetTaskAdmin()

	// 加一个任务到 taskAdmin
	taskId := "task1"
	fragId := int64(1)
	task1 := models.NewTask()
	task1.Id = taskId
	_, cancel1 := context.WithCancel(context.Background())
	defer cancel1()
	taskAdmin.AddTask(task1, cancel1)

	// 加一个 unfinished frag
	task1frag1 := &models.Frag{
		RuntimeInfo: models.NewFragRuntimeInfo(),
		BaseInfo: &models.FragBaseInfo{
			TaskId: taskId,
			FragId: fragId,
		},
	}
	taskLister.addUnfinishedFrag(task1frag1, logger)

	// 添加 frag result
	fragResult := models.ConstructFragResult(task1frag1)
	fragResult.BaseInfo.SuccessfulSize = 1
	taskAdmin.AddFragResult(fragResult, logger)

	sucSize := task1.SuccessfulSize.Load()
	sucFragUnm := task1.SuccessfulFragNum.Load()
	if sucSize != 1 || sucFragUnm != 1 {
		t.Fatal("suc size and fragNum mismatch")
	}
	_, ok := task1.FragIdSet.Load(fragId)
	if !ok {
		t.Fatal("frag result should have been added")
	}

	// unfinished 仍然保留
	unfinishedFrag := taskLister.getUnfinishedFrag("task1", 1, logger)
	if unfinishedFrag != nil {
		t.Fatal("unfinished frag should have been removed")
	}
}
