package tasks

import (
	"context"
	"sync"
	"testing"

	"git.code.oa.com/cloud-msp/migration-lister/api"
	"git.code.oa.com/cloud-msp/migration-lister/internal/service/apicall"
	"git.code.oa.com/cloud-msp/migration-lister/internal/service/coscall"
	"git.code.oa.com/cloud-msp/migration-lister/internal/service/mock"
	"git.code.oa.com/cloud-msp/migration-lister/internal/service/models"
	"github.com/golang/mock/gomock"
	"go.hlw.tencent.com/alego/components/log"

	"github.com/prashantv/gostub"
	"go.uber.org/zap"
)

// 队列正确初始化
func Test_InitFinalizer(t *testing.T) {
	InitTaskFinalizer(zap.NewNop())
	if needFinalizeCh == nil {
		t.Fatal("finalize que nil")
	}
}

// 处理 nil task
func Test_finalizeTask_nil_task(t *testing.T) {
	boot()

	logger := zap.NewNop()
	req := &TaskConclusion{
		JobId: "not-exist",
		State: api.TaskState_SUCCESS,
	}
	err := finalizeTask(req, logger)
	if err != nil {
		t.Fatal("nil task should be no-op")
	}
}

// 处理 nil task
func Test_finalizeTask(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	jobId := "whatever"
	finalState := api.TaskState_SUCCESS
	// setup mock
	var getMspCosCallStub = func() coscall.MspCosCallIF {
		m := mock.NewMockMspCosCallIF(ctrl)
		m.EXPECT().EndReportFailedFiles(gomock.Eq(jobId))
		return m
	}
	var getApiCallStub = func() apicall.ApiCallIf {
		m := mock.NewMockApiCallIf(ctrl)
		m.EXPECT().GetStatus(jobId).Return(finalState, nil) // 间接调用
		return m
	}
	// setup stub
	stubs := gostub.Stub(&coscall.GetMspCosCall, getMspCosCallStub)
	stubs.Stub(&apicall.GetApiCall, getApiCallStub)
	defer stubs.Reset()

	boot()

	// 添加任务到管理器
	taskAdmin := GetTaskAdmin()
	_, cancel := context.WithCancel(context.Background())
	task := models.NewTask()
	task.Id = jobId
	taskAdmin.AddTask(task, cancel)

	logger := log.GetLogger()
	req := &TaskConclusion{JobId: jobId, State: finalState}
	err := finalizeTask(req, logger)
	if err != nil {
		t.Fatalf("got err=%v", err)
	}

	// assertions
	// 到这里 task 已经从内存里被清理了
	if taskAdmin.GetTask(jobId, logger) != nil {
		t.Fatalf("task should be removed from taskAdmin")
	}
}

func Test_receive_finalize_request(t *testing.T) {
	InitTaskFinalizer(zap.NewNop())

	tcs := []*TaskConclusion{
		{JobId: "task1"},
		{JobId: "task2"},
		{JobId: "task3"},
	}
	for _, c := range tcs {
		SendFinalizeRequest(c)
	}
	// 实际使用不会 close, 这里要解除阻塞
	close(needFinalizeCh)

	var mu sync.Mutex
	var finalized []string
	logger := zap.NewNop()
	var wg sync.WaitGroup
	wg.Add(len(tcs))
	handler := func(tc *TaskConclusion, logger *zap.Logger) error {
		mu.Lock()
		defer mu.Unlock()
		finalized = append(finalized, tc.JobId)
		wg.Done()
		return nil
	}
	serve(logger, handler)
	wg.Wait()

	if len(finalized) != len(tcs) {
		t.Fatalf("missing task, got=%v", finalized)
	}
	t.Log(finalized)
}
