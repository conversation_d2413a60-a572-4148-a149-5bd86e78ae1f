package tasks

import (
	"testing"

	"git.code.oa.com/cloud-msp/migration-lister/tools"
)

//func boot() {
//	boot := pl_boot.NewBootstrapper()
//	boot.RegisterAppContextWithConfigPath("../../configs", "test")
//}

// TestTask_QuickListMarker
func TestTask_QuickListMarker(t *testing.T) {
	task := NewTask()

	var prefix = "dummy-prefix"
	if marker, ok := task.LoadQuickListMarker(prefix); ok {
		t.Errorf("should not find a marker, got marker=%s, ok=%t", marker, ok)
	}

	var m = "dummy-marker"
	task.StoreQuickListMarker(prefix, m)
	marker, ok := task.LoadQuickListMarker(prefix)
	if !ok {
		t.Errorf("should find a marker, got marker=%s, ok=%t", marker, ok)
	}
	if marker != m {
		t.<PERSON>rrorf("marker value missmatehed, expect %s, got %s", m, marker)
	}
}

// TestTask_SlowListMarker
func TestTask_SlowListMarker(t *testing.T) {
	task := NewTask()

	var prefix = "dummy-prefix"
	if marker, ok := task.LoadSlowListMarker(prefix); ok {
		t.Errorf("should not find a marker, got marker=%s, ok=%t", marker, ok)
	}

	var m = "dummy-marker"
	task.StoreSlowListMarker(prefix, m)
	marker, ok := task.LoadSlowListMarker(prefix)
	if !ok {
		t.Errorf("should find a marker, got marker=%s, ok=%t", marker, ok)
	}
	if marker != m {
		t.Errorf("marker value missmatehed, expect %s, got %s", m, marker)
	}
}

// TestTask_GetPrefixes 测试获取 task prefix 列表
func TestTask_GetPrefixes(t *testing.T) {
	task := NewTask()

	// get prefixes on newly created task
	if prefixes, err := task.GetPrefixes(); err != nil || len(prefixes) != 0 {
		t.Errorf("should return nil prefix list and nil err, got prefixes=%v, err=%v", prefixes, err)
	}

	task.JobInfo = &tools.JobInfo{}
	if _, err := task.GetPrefixes(); err != nil {
		t.Errorf("should return nil err, got err=%v", err)
	}

	rule, _ := json.Marshal(make([]string, 0))
	ruleStr := string(rule)
	task.JobInfo.MigrationRule = &ruleStr
	if prefixes, err := task.GetPrefixes(); err != nil && len(prefixes) != 0 {
		t.Errorf("should return empty prefix list and nil err, got prefixes=%v, err=%v", prefixes, err)
	}

	rule, _ = json.Marshal([]string{
		"dummy-prefix-0",
		"dummy-prefix-1",
		"dummy-prefix-2",
	})
	ruleStr = string(rule)
	task.JobInfo.MigrationRule = &ruleStr
	if prefixes, err := task.GetPrefixes(); err != nil && len(prefixes) != 3 {
		t.Errorf("should return non-empty prefix list and nil err, got prefixes=%v, err=%v", prefixes, err)
	}
}

// 累加 fragId 相同 fragResult 会去重
func Test_AddFragResult(t *testing.T) {
	boot()

	task := NewTask()
	task.Id = "task1"

	frag := &Frag{
		BaseInfo: &FragBaseInfo{
			FragId: 1,
			TaskId: "task1",
		},
		RuntimeInfo: NewFragRuntimeInfo(),
	}
	fragResult := ConstructFragResult(frag)
	fragResult.BaseInfo.SuccessfulSize = 1

	// 第一次添加 fragResult
	changed := task.AddFragResult(fragResult)
	if !changed {
		t.Fatal("frag result should have been added")
	}

	sucSize := task.SuccessfulSize.Load()
	sucFragUnm := task.SuccessfulFragNum.Load()
	if sucSize != 1 || sucFragUnm != 1 {
		t.Fatal("suc size and fragNum mismatch")
	}

	// 相同 ID 重复添加 fragResult 应该忽略
	fragResult.BaseInfo.SuccessfulSize = 2
	changed = task.AddFragResult(fragResult)
	if changed {
		t.Fatal("frag result should be ignored")
	}

	sucSize = task.SuccessfulSize.Load()
	sucFragUnm = task.SuccessfulFragNum.Load()
	if sucSize != 1 || sucFragUnm != 1 {
		t.Fatal("suc size and fragNum changed after add duplicate frag result")
	}

}
