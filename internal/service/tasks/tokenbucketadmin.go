package tasks

import (
	"math"
	"sync"

	"git.code.oa.com/cloud-msp/migration-lister/pkg/tokenbucket"
	"git.code.oa.com/pulse-line/pl_boot"
	"go.uber.org/zap"
)

// 管理所有任务的token bucket
type TokenBucketAdmin interface {
	ConsumeToken(jobId string, n int) int
	UpdateRate(jobId string, newRate int)
	CreateBucket(jobId string, rate int)
	CloseTokenBucket(jobId string)
}

var (
	tokenBucketAdmin TokenBucketAdmin
	once2            sync.Once
)

type tokenBucketMap struct {
	buckets map[string]tokenbucket.TokenBucket
	mu      sync.Mutex
}

func init() {
	once2.Do(
		func() {
			tokenBucketAdmin = &tokenBucketMap{
				buckets: make(map[string]tokenbucket.TokenBucket),
			}
		},
	)
}

// ConsumeToken
func (t *tokenBucketMap) ConsumeToken(jobId string, n int) int {
	t.mu.Lock()
	defer t.mu.Unlock()
	logger := pl_boot.AppCtx.GetAppLogger().With(zap.String("jobId", jobId))

	tb, ok := t.buckets[jobId]

	if !ok {
		logger.Info("NoMatchBucket")
		return 0
	}
	ret := tb.AsyncTakeN(int64(n))

	logger.Info("ConsumeToken", zap.Any("req", n), zap.Any("resp", ret))
	return int(ret)
}

// UpdateRate
func (t *tokenBucketMap) UpdateRate(jobId string, newRate int) {
	t.mu.Lock()
	defer t.mu.Unlock()
	logger := pl_boot.AppCtx.GetAppLogger().With(zap.String("jobId", jobId))

	tb, ok := t.buckets[jobId]

	if !ok {
		logger.Info("NoMatchBucket")
		return
	}
	// 这里要维持一个最小速度，以便worker能清空内存
	// if newRate < 100 {
	// 	newRate = 100
	// }
	// TODO 这里是否有必要? 如果配置QPSLimit=10, 部署了5个worker, 每个worker都会来申请10个token,
	// 每个worker的请求都会被满足, 这个任务的实际qps就是50. 即实际qps=min(n_worker*QPSLimit, 100)
	tb.UpdateRate(int64(newRate))
}

// CloseTokenBucket
func (t *tokenBucketMap) CloseTokenBucket(jobId string) {
	t.mu.Lock()
	defer t.mu.Unlock()
	logger := pl_boot.AppCtx.GetAppLogger().With(zap.String("jobId", jobId))
	delete(t.buckets, jobId)
	logger.Info("CloseTokenBucket Success")
}

// CreateBucket
func (t *tokenBucketMap) CreateBucket(jobId string, rate int) {
	tb := tokenbucket.New(&tokenbucket.Config{
		MaxTokens:       int64(math.Ceil(float64(rate) * 1.02)), // 根据实测损耗
		InitialTokens:   int64(rate),
		TokensPerSecond: int64(rate),
	})

	t.mu.Lock()
	defer t.mu.Unlock()
	logger := pl_boot.AppCtx.GetAppLogger().With(zap.String("jobId", jobId))
	t.buckets[jobId] = tb

	logger.Info("CreateBucket Success")
}

// ConsumeToken
func ConsumeToken(jobId string, n int) int {
	return tokenBucketAdmin.ConsumeToken(jobId, n)
}

// UpdateRate
func UpdateRate(jobId string, newRate int) {
	pl_boot.AppCtx.GetAppLogger().Info("UpdateRate",
		zap.Any("jobId", jobId),
		zap.Any("newRate", newRate),
	)
	tokenBucketAdmin.UpdateRate(jobId, newRate)
}

// CreateBucket
func CreateBucket(jobId string, rate int) {
	pl_boot.AppCtx.GetAppLogger().Info("CreateBucket",
		zap.Any("jobId", jobId),
		zap.Any("rate", rate),
	)
	tokenBucketAdmin.CreateBucket(jobId, rate)
}

// CloseTokenBucket
func CloseTokenBucket(jobId string) {
	tokenBucketAdmin.CloseTokenBucket(jobId)
}
