package tasks

import (
	"context"
	"errors"
	"fmt"
	"runtime/debug"
	"strings"
	"sync"
	"time"

	"git.code.oa.com/cloud-msp/migration-lister/internal/service/deal"
	"git.code.oa.com/cloud-msp/migration-lister/internal/service/models"
	"git.code.oa.com/cloud-msp/migration-lister/internal/service/scheduler"

	"git.code.oa.com/cloud-msp/migration-lister/api"
	"git.code.oa.com/cloud-msp/migration-lister/internal/service/apicall"
	"git.code.oa.com/cloud-msp/migration-lister/internal/service/consts"
	"git.code.oa.com/cloud-msp/migration-lister/internal/service/coscall"
	"git.code.oa.com/cloud-msp/migration-lister/pkg/conf"
	"git.code.oa.com/cloud-msp/migration-lister/pkg/lister"
	"git.code.oa.com/cloud-msp/migration-lister/pkg/metrics"
	failfile "git.code.oa.com/cloud-msp/migration-lister/pkg/mspcos"
	"git.code.oa.com/cloud-msp/migration-lister/pkg/utils"
	"git.code.oa.com/cloud-msp/migration-lister/tools"
	worker_api "git.code.oa.com/cloud-msp/migration-worker/api"
	"go.uber.org/atomic"

	"git.code.oa.com/pulse-line/pl_boot"
	"git.code.oa.com/pulse-line/pl_prom"
	"go.uber.org/zap"
	"google.golang.org/grpc"
)

// GetTaskAdmin 单例模式获取 TaskAdminInterface 任务管理器
func GetTaskAdmin() TaskAdminInterface {
	return taskLister
}

// 单例模式获取 BackupService 备份服务
// func GetTaskAdminBackup() backup.BackupService {
// 	return taskLister
// }

// TasksAdmin implements TaskAdminInterface
type TasksAdmin struct {
	tasks           *sync.Map     // map[string]*Task
	unfinishedFrags *sync.Map     // map[string taskId]map[int64 fragId]*Frag
	cancelFs        *strCancelMap // jobId string: cancelF context.CancelFunc
	forceBackupChan chan struct{}
}

// FillJobInfo 将 domain object (*models.Task) 转成 controller VO (*api.JobInfo)
func (t *TasksAdmin) FillJobInfo(info *api.JobInfo, task *models.Task) {
	jobInfo := task.JobInfo
	info.SrcSecretKey = *jobInfo.SrcSecretKey
	info.SrcSecretId = *jobInfo.SrcSecretID
	info.SrcBucket = *jobInfo.SrcBucket
	info.SrcFileName = *jobInfo.SrcFileName
	info.SrcFileUrl = *jobInfo.SrcFileURL
	info.SrcRegion = *jobInfo.SrcRegion
	info.SrcService = *jobInfo.SrcService
	info.DstSecretKey = *jobInfo.DstSecretKey
	info.DstSecretId = *jobInfo.DstSecretID
	info.DstRegion = *jobInfo.DstRegion
	info.DstBucket = *jobInfo.DstBucket
	info.FileOverWrite = int32(*jobInfo.FileOverWrite)
	info.LimitFlow = int64(*jobInfo.LimitSpeed)
	// info.LimitQps = int64(*jobInfo.LimitQPS)
	info.LimitQps = int64(deal.NEWCroServiceDeal().GetQpsLimit(task))
	info.PathConf = int32(*jobInfo.PathConf)
	info.UserAppId = tools.StrconInt64(*jobInfo.UserAppID)
	info.StorageType = int32(*jobInfo.StorageType)
	info.MigrationHeaderType = int32(*jobInfo.MigrationHeaderType)
	info.MigrationHeader = *jobInfo.MigrationHeader
	info.JobId = *jobInfo.JobID
	info.SavePath = *jobInfo.SavePath
	info.BucketType = *jobInfo.BucketType
	info.KodoEndpoint = *jobInfo.KodoEndpoint
	info.NewHeaders = *jobInfo.NewHeaders
}

// WorkerAgentClientStruct worker agent gRPC 客户端
type WorkerAgentClientStruct struct {
	Conn   *grpc.ClientConn
	Client worker_api.WorkerServiceClient
}

// 添加任务到 TaskAdmin
// NOTE: 添加 task, cancelF, 和空的 unfinishedFrag
func (t *TasksAdmin) AddTask(task *models.Task, cancel context.CancelFunc) {
	t.tasks.LoadOrStore(task.Id, task)
	t.unfinishedFrags.LoadOrStore(task.Id, &sync.Map{})
	t.cancelFs.LoadOrStore(task.Id, cancel)
	// 创建token bucket admin
	CreateBucket(task.Id, int(deal.NEWCroServiceDeal().GetQpsLimit(task)))
	_ = scheduler.AddTask(context.Background(), task)
}

// GetTask 从 TaskAdmin 获取指定任务
func (t *TasksAdmin) GetTask(taskId string, logger *zap.Logger) (task *models.Task) {
	// t.mutex.Lock()
	// defer t.mutex.Unlock()
	taskInterface, ok := t.tasks.Load(taskId) // [taskId]
	//logger.Info(
	//	"get task by taskId", zap.String("jobId", taskId), zap.Bool("ok", ok),
	//)
	if ok {
		task = taskInterface.(*models.Task)
		return task
	}
	return nil
}

// 获取一个任务的未完成分片集合
func (t *TasksAdmin) GetUnfinishedFragSet(taskId string) (*sync.Map, error) {
	rawFrags, ok := t.unfinishedFrags.Load(taskId)
	if !ok {
		return nil, fmt.Errorf("%s has no unfinished frag set", taskId)
	}
	unfinishedFragSet, ok := rawFrags.(*sync.Map)
	if !ok {
		return nil, fmt.Errorf("%s expect unfinished frag set, got %T", taskId, unfinishedFragSet)
	}
	return unfinishedFragSet, nil
}

// 为一个任务加入未完成分片集合
// NOTE: 只有在任务加入 taskAdmin 的时候调用一次
func (t *TasksAdmin) AddUnfinishedFragSet(taskId string, unfinishedFragSet *sync.Map) {
	t.unfinishedFrags.Store(taskId, unfinishedFragSet)
}

// HandleFrag 处理分片的回调函数
// 在文件遍历调用
func (t *TasksAdmin) HandleFrag(
	frag *models.Frag, logger *zap.Logger,
) (bool, error) {
	// 处理分片，1、添加到全局的任务池中(sync.Map, 或者普通map加锁) 2、调用distribute
	// t.mutex.Lock()
	// defer t.mutex.Unlock()
	// 通过distribute分发
	appLogger := pl_boot.AppCtx.GetAppLogger().With(
		zap.Int64("fragId", frag.BaseInfo.FragId),
		zap.Int("fileNum", len(frag.BaseInfo.Files)),
		zap.String("jobId", frag.BaseInfo.TaskId),
	)
	appLogger.Info("HandleFrag params")

	var task *models.Task
	// task, ok := t.tasks[frag.TaskId]
	task = t.GetTask(frag.BaseInfo.TaskId, logger)
	if task == nil {
		appLogger.Info("get task fail, empty task")
		return false, errors.New("task has not init")
	}

	res, err := t.DistributeFrag(frag, logger)
	if err != nil {
		return false, err
	}

	// 维护state数据
	task.SetState(api.TaskState_RUNNING)
	realFrag := &models.Frag{
		BaseInfo: &models.FragBaseInfo{
			TaskId: frag.BaseInfo.TaskId,
			FragId: frag.BaseInfo.FragId,
			Files:  make([]*api.File, 0),
		},
		RuntimeInfo: &models.FragRuntimeInfo{
			FirstHeart: &atomic.Value{},
			LastHeart:  &atomic.Value{},
			Retry:      atomic.NewInt64(frag.RuntimeInfo.Retry.Load()),
		},
		Over: atomic.NewBool(frag.Over.Load()),
	}
	realFrag.RuntimeInfo.FirstHeart.Store(frag.RuntimeInfo.FirstHeart.Load())
	realFrag.RuntimeInfo.LastHeart.Store(frag.RuntimeInfo.LastHeart.Load())
	for _, f := range frag.BaseInfo.Files {
		file := &api.File{
			FullPath:     f.FullPath,
			Size:         f.Size,
			CurTime:      f.CurTime,
			FailedReason: f.FailedReason,
			UploadId:     f.UploadId,
		}
		realFrag.BaseInfo.Files = append(realFrag.BaseInfo.Files, file)
	}

	// NOTE: 分片先标记为未完成, worker 上报 fragResult 后删除
	t.addUnfinishedFrag(realFrag, appLogger)

	return res, err
}

// DistributeFrag 上报分片
func (t *TasksAdmin) DistributeFrag(
	frag *models.Frag, logger *zap.Logger,
) (bool, error) {
	logger = logger.With(
		zap.Any("taskId", frag.BaseInfo.TaskId),
		zap.Any("fragId", frag.BaseInfo.FragId),
	)
	logger.Info("DistributeFrag start")

	start := time.Now()
	defer func() {
		logger.Info(
			"DistributeFrag over",
			zap.Float64("cost", time.Now().Sub(start).Seconds()),
		)
	}()

	var retry = frag.RuntimeInfo.Retry

	if retry.Load() == 0 { // 更新frag信息 // 如果重试则不更新
		frag.UpdateFirstHeart()
	}
	frag.UpdateLastHeart()
	err := t.sendFrag(frag, logger)

	if err != nil {
		logger.Error("DistributeFrag err", zap.Error(err))
		// 失败上报统计
		pl_prom.GetCounterWithValues(
			metrics.ErrorCount, metrics.Lister, "DistributeFrag", frag.BaseInfo.TaskId, "",
		)
		return false, err
	}

	return true, nil
}

// 提交到scheduler统一队列
func (t *TasksAdmin) sendFrag(frag *models.Frag, logger *zap.Logger) error {
	for {
		// 如果任务已经终止, 则停止提交frag
		task := t.GetTask(frag.BaseInfo.TaskId, logger)
		if task == nil {
			logger.Info("sendFrag stop, get task fail, empty task")
			return errors.New("empty task")
		}

		err := scheduler.CommitFrag(context.Background(), frag)
		if err != nil {
			logger.Warn("sendFrag err", zap.Error(err))
			time.Sleep(time.Millisecond * time.Duration(conf.ListerConfig.GlobalConf.FragInterval))
			continue
		}
		break
	}
	return nil
}

func (t *TasksAdmin) lenSyncMap(s *sync.Map) int {
	var c int
	s.Range(
		func(k, v interface{}) bool {
			c++
			return true
		},
	)

	return c
}

// 通知任务进入清理阶段
// NOTE: 结束失败文件列表上传, 清理上传缓存, 更新任务状态
func (t *TasksAdmin) NotifyTaskFinalize(task *models.Task, state api.TaskState, stopReason string, logger *zap.Logger) {
	_, quickCnt := task.GetQuickListingStat()
	_, existingCnt := task.GetExistingStat()
	fitNum := quickCnt + existingCnt

	// NOTE: 结束前刷新一波 detail.FitCondFileNum 和 detail.ScanFileNum
	skippedCnt := task.GetSkipped()
	totalNum := fitNum + skippedCnt
	_ = apicall.GetApiCall().ReportJobScanInfo(task.Id, fitNum, totalNum)

	_ = apicall.GetApiCall().ReportTaskProgress(task, logger)
	logger.Info("ReportTaskProgress Final")
	c := &TaskConclusion{JobId: task.Id, State: state, StopReason: stopReason}
	SendFinalizeRequest(c)
}

// 从内存中排空 task
// NOTE: 需要满足幂等
//  删除task, 取消 ctx, 删除未完成分片
//  删除令牌桶
func (t *TasksAdmin) PruneTask(taskId string) {
	t.RemoveTask(taskId)
	// TODO worker clean task
	// 排空token bucket
	CloseTokenBucket(taskId)
}

// 更新分片心跳
func (t *TasksAdmin) UpdateFragHeart(taskId string, fragId int64, logger *zap.Logger) {
	frag := t.getUnfinishedFrag(taskId, fragId, logger)
	if frag != nil {
		frag.UpdateLastHeart()
	}
}

// 查询内存中的未完成分片
func (t *TasksAdmin) getUnfinishedFrag(taskId string, fragId int64, logger *zap.Logger) *models.Frag {
	logger = logger.With(zap.String("jobId", taskId), zap.Any("fragId", fragId))

	rawFrags, ok := t.unfinishedFrags.Load(taskId)
	if !ok {
		logger.Info("Update Frag Heart no match task")
		return nil
	}

	frags := rawFrags.(*sync.Map)
	rawFrag, ok := frags.Load(fragId)
	if !ok {
		logger.Info("Update Frag Heart no match frag")
		return nil
	}
	frag := rawFrag.(*models.Frag)
	return frag
}

// worker 上报分片结果后, 把这个分片从 taskAdmin.unfinishedFrags 删除
func (t *TasksAdmin) deleteUnfinishedFrag(taskId string, fragId int64, logger *zap.Logger) {
	logger = logger.With(zap.String("jobId", taskId), zap.Any("fragId", fragId))

	fragSet, ok := t.unfinishedFrags.Load(taskId)
	if !ok {
		logger.Warn("task has no corresponding unfinished frags")
		return
	}

	unfinishedFrags := fragSet.(*sync.Map)
	unfinishedFrags.Delete(fragId)
	syncUnfinishedFrag(taskId, fragId, nil)
	logger.Info("deleted from unfinishedFrags")
}

// 新生成的分片加入 unfinishedFrag
// NOTE: 非线程安全
func (t *TasksAdmin) addUnfinishedFrag(frag *models.Frag, logger *zap.Logger) {
	taskId := frag.BaseInfo.TaskId
	fragId := frag.BaseInfo.FragId

	if fragSet, ok := t.unfinishedFrags.Load(taskId); ok {
		frags := fragSet.(*sync.Map)
		frags.Store(fragId, frag)
		syncUnfinishedFrag(taskId, fragId, frag)
		logger.Debug("addUnfinishedFrag", zap.Any("fragId", frag.BaseInfo.FragId))
		return
	}
	logger.Warn("task has no corresponding unfinished frag set")
}

func syncUnfinishedFrag(taskId string, fragId int64, frag *models.Frag) error {
	db, err := utils.DBOpen(models.BackupDir + taskId)
	if err != nil {
		return err
	}

	key := "FragSet.BaseInfo." + fmt.Sprint(fragId)
	if frag != nil { // Put
		value, err := json.Marshal(frag.BaseInfo)
		if err != nil {
			return err
		}
		utils.DBPut(db, key, string(value))
	} else { // Delete
		utils.DBDelete(db, key)
	}
	return nil
}

// 添加分片迁移结果
// 核查重试分片匹配
// 更新维护的已下发分片
// 更新维护的任务迁移结果
// 调用回调函数上报任务进度
// 调用回调函数上报失败文件
// NOTE: 这个地方不能阻塞太久，可能有好几个 worker 在调这个接口
func (t *TasksAdmin) AddFragResult(
	result *models.FragResult, logger *zap.Logger,
) bool {
	appLogger := pl_boot.AppCtx.GetAppLogger().With(
		zap.String("jobId", result.BaseInfo.TaskId),
		zap.Int64("fragId", result.BaseInfo.FragId))
	appLogger.Info("AddFragResult")

	frag := t.getUnfinishedFrag(result.BaseInfo.TaskId, result.BaseInfo.FragId, logger)
	// 如果未完成分片不存在返回成功
	if frag == nil {
		appLogger.Warn("received frag result, but no matching unfinished frag")
		return true
	}
	// 如果未完成的分片与请求的分片重试次数不一致返回成功
	if frag.RuntimeInfo.Retry.Load() != result.BaseInfo.RetryNum {
		appLogger.Warn(
			"received frag and unfinished frag retry num mismatch",
			zap.Int64("unfinishedFrag retryNum", frag.RuntimeInfo.Retry.Load()),
			zap.Int64("fragResult retryNum", result.BaseInfo.RetryNum),
		)
		return true
	}

	// 先上报进度
	rawTask, ok := t.tasks.Load(result.BaseInfo.TaskId)
	if !ok {
		appLogger.Warn("no corresponding task")
		return false
	}
	task := rawTask.(*models.Task)

	// 获取未完成的分片
	task.Mutex.Lock()
	defer task.Mutex.Unlock()

	// double check
	frag = t.getUnfinishedFrag(result.BaseInfo.TaskId, result.BaseInfo.FragId, logger)
	// 如果未完成分片不存在返回成功
	if frag == nil {
		appLogger.Warn("received frag result, but no matching unfinished frag")
		return true
	}

	// 失败文列表写入cos
	ret := coscall.GetMspCosCall().ReportFailedFiles(result.BaseInfo)
	if !ret {
		appLogger.Error("failed to report failed files, frag remains unfinished")
		return false
	}

	// 累加内存中的计数, fragId 加入已完成 set
	changed := task.AddFragResult(result)
	if changed {
		appLogger.Info("AddFragResult changed")
	}
	// 从未完成分片删除
	t.deleteUnfinishedFrag(result.BaseInfo.TaskId, result.BaseInfo.FragId, logger)
	return true
}

// UpdateTask 更新当前进程中已有的 task, 没有则 insert
func (t *TasksAdmin) UpdateTask(task *models.Task, logger *zap.Logger) {
	loadedTask, ok := t.tasks.Load(task.Id)
	if !ok {
		t.tasks.Store(task.Id, task)
		logger.Info(
			"Insert task into taskAdmin",
			zap.String("jobId", task.Id),
			zap.Int32("state", int32(task.GetState())),
		)
		return
	}

	ot := loadedTask.(*models.Task)
	logger.Info(
		"Update existing task in taskAdmin",
		zap.String("jobId", ot.Id),
		zap.Int32("state", int32(task.GetState())),
	)
	// TODO: 这里是否应该更新 loadedTask 携带的计数？
	ot.SetQpsLimit(task.QpsLimit)
	ot.SetSpeedLimit(task.SpeedLimit)
	ot.SetSpeedLimitInfo(task.SpeedLimitInfo)
	ot.SetPriority(task.Priority)
	// 更新token bucket
	UpdateRate(task.Id, int(deal.NEWCroServiceDeal().GetQpsLimit(task)))
	_ = scheduler.UpdateTask(context.Background(), ot)
}

// 删除内存中的 task, 取消 task ctx, 删除未完成分片
func (t *TasksAdmin) RemoveTask(taskId string) {
	// t.mutex.Lock()
	// defer t.mutex.Unlock()
	logger := pl_boot.AppCtx.GetAppLogger().With(zap.Any("jobId", taskId))

	cancel, ok := t.GetCancelFunc(taskId)
	if ok {
		t.cancelFs.Delete(taskId)
		cancel() // 最终一定要要释放 ctx
		logger.Info("task ctx canceled")
	}

	t.tasks.Delete(taskId)
	t.unfinishedFrags.Delete(taskId)
	scheduler.DelTask(context.Background(), taskId)

	// TODO: 是否应该覆盖当前备份文件
	logger.Info("remove task all the task admin")
}

// GetAllTasks 从 taskAdmin 获取全部任务
func (t *TasksAdmin) GetAllTasks() map[string]*models.Task {
	// t.mutex.Lock()
	// defer t.mutex.Unlock()
	res := make(map[string]*models.Task)
	t.tasks.Range(
		func(k, v interface{}) bool {
			taskId := k.(string)
			task := v.(*models.Task)
			res[taskId] = task
			return true
		},
	)

	return res
}

// 任务的最终状态
var finalStateSet = map[api.TaskState]struct{}{
	api.TaskState_SUCCESS:  {},
	api.TaskState_FAILURE:  {},
	api.TaskState_STOP:     {},
	api.TaskState_DELETION: {},
}

// 状态值是否已经是最终状态
func IsFinalState(state api.TaskState) bool {
	_, ok := finalStateSet[state]
	return ok
}

// 上报任务状态
// changed = false 状态没有实际变更
// NOTE：只有数据库中的 state 不在(9, 10, 101, 13)中的任意个一时上报, 防止覆盖最终状态
var UpdateTaskState = func(taskId string, newState api.TaskState) (changed bool) {
	logger := pl_boot.AppCtx.GetAppLogger().With(zap.String("jobId", taskId))

	// 重试到成功获取任务状态为止
	var currState api.TaskState
	var err error
	for {
		currState, err = apicall.GetApiCall().GetStatus(taskId)
		if err != nil {
			logger.Warn("UpdateTaskState GetStatus failed", zap.Error(err))
			time.Sleep(lister.RetryInterval)
			continue
		}
		break
	}

	if currState == newState {
		logger.Info("newState == currState no need to update", zap.Int("state", int(currState)))
		return false
	}

	// 重试到成功更新任务状态为止
	// NOTE: 最终状态不更新防止意外覆盖
	if IsFinalState(currState) {
		logger.Warn(
			"UpdateTaskState task is already final state",
			zap.Int("currState", int(currState)),
			zap.Int("newState", int(newState)))
		return false
	}

	for {
		reportRes, err := apicall.GetApiCall().ReportTaskState(taskId, tools.IntStrcon(int(newState)))
		if err != nil {
			logger.Warn(
				"UpdateTaskState ReportTaskState failed", zap.Any("resp", reportRes), zap.Error(err))
			time.Sleep(lister.RetryInterval)
			continue
		}
		break
	}

	return true
}

// 更新任务状态
// NOTE: 先查数据库再更新, 只有数据库中的 state 不在(9, 10, 101, 13)中的任意个一时上报, 防止覆盖最终状态
func SafeUpdateTaskState(t *models.Task, newState api.TaskState) bool {
	if t == nil {
		return false
	}
	// 锁单个任务
	t.Mutex.Lock()
	defer t.Mutex.Unlock()
	return UpdateTaskState(t.Id, newState)
}

// 固定时间间隔上报 quick list 进度
// NOTE: 请求 MAReportJobScanInfo 更新
//  detail.FitCondFileNum
//  detail.ScanFileNum
func reportQuickListProgress(task *models.Task, logger *zap.Logger) {
	quickSize, quickCnt := task.GetQuickListingStat()
	existingSize, existingCnt := task.GetExistingStat()
	skippedCnt := task.GetSkipped()
	fileNum := quickCnt + existingCnt
	totalNum := fileNum + skippedCnt

	_ = apicall.GetApiCall().ReportJobScanInfo(task.Id, fileNum, totalNum)
	logger.Info(
		"task_admin.QuickList progress",
		zap.Int64("quickSize", quickSize),
		zap.Int64("existingSize", existingSize),
		zap.Int64("fileNum", fileNum),
		zap.Int64("totalNum", totalNum),
	)
}

// quick 过程中定时调用 ReportScanInfo, 结束调用 ReportSliceInfo
func periodReportQuickStat(ctx context.Context, task *models.Task, logger *zap.Logger) {
	defer func() {
		if e := recover(); e != nil {
			logger.Error(
				"recovered from QuickList ReportJobSliceInfo panic",
				zap.Any("error", e),
				zap.Any("stack", string(debug.Stack()[:])),
			)
		}
	}()

	ticker := time.NewTicker(time.Minute)
	for {
		select {
		case <-ctx.Done():
			quickSize, quickCnt := task.GetQuickListingStat()
			existingSize, existingCnt := task.GetExistingStat()
			fileNum := quickCnt + existingCnt
			size := quickSize + existingSize
			// NOTE: quick 不上报分片数
			_ = apicall.GetApiCall().ReportJobSliceInfo(task.Id, task.Name, 0, fileNum, size)

			// NOTE: 结束前刷新一波 detail.FitCondFileNum 和 detail.ScanFileNum
			skippedCnt := task.GetSkipped()
			totalNum := fileNum + skippedCnt
			_ = apicall.GetApiCall().ReportJobScanInfo(task.Id, fileNum, totalNum)
			logger.Info(
				"task_admin.QuickList about to finish",
				zap.Int64("fileNum", fileNum),
				zap.Int64("size", size))
			return
		case <-ticker.C:
			reportQuickListProgress(task, logger)
		}
	}
}

// 取出 task 上记录的 prefix: marker 键值对
func populatePrefixMarkerMap(task *models.Task, isSlowListing bool) (map[string]string, error) {
	prefixMap := make(map[string]string)
	loader := task.LoadQuickListMarker
	if isSlowListing {
		loader = task.LoadSlowListMarker
	}

	var filterType = *task.JobInfo.MigrationRuleType
	switch filterType {
	case consts.MigrationRuleTypeNone:
		prefix := ""
		marker, _ := loader(prefix)
		prefixMap[prefix] = marker

	case consts.MigrationRuleTypePrefix:
		prefixes, err := task.GetPrefixes()
		if err != nil {
			return nil, err
		}

		for _, prefix := range prefixes {
			marker, _ := loader(prefix)
			prefixMap[prefix] = marker
		}

	case consts.MigrationRuleTypeRegex:
		prefix := ""
		marker, _ := loader(prefix)
		prefixMap[prefix] = marker

	case 0:
		// NOTE: URL 任务传的0, 兼容性考虑这里兜底
		prefix := ""
		marker, _ := loader(prefix)
		prefixMap[prefix] = marker

	default:
		return prefixMap, fmt.Errorf("unknown MigrationRuleType: %v", filterType)
	}

	return prefixMap, nil
}

// QuickList 快速遍历源存储(通常是 bucket)的文件, 累加 file cnt 和 size
//nolint
func (t *TasksAdmin) QuickList(ctx context.Context, taskId string) {
	logger := pl_boot.AppCtx.GetAppLogger().With(zap.String("jobId", taskId))
	task := t.GetTask(taskId, logger)
	if nil == task {
		logger.Error("nil task")
		return
	}
	if task.IsEnd() {
		logger.Info("task ended, no need to continue")
		return
	}
	if task.QuickListOver.Load() {
		logger.Info("quickList already over")
		return
	}
	logger.Info("task_admin.QuickList running")

	// 及时上报快速遍历进度更新
	reporterCtx, cancel := context.WithCancel(ctx)
	// 快速遍历结束, report 协程也结束
	defer cancel()
	go periodReportQuickStat(reporterCtx, task, logger)

	if task.Retry {
		logger.Info("retry task quick list (from failFile)")
		failFileLister, err := failfile.NewFailFilLister()
		if err != nil {
			logger.Error("failed to init failFile lister", zap.Error(err))
			t.NotifyTaskFinalize(task, api.TaskState_FAILURE, err.Error(), logger)
			return
		}

		var prefix = fmt.Sprintf("%s-failFile-quick", taskId)
		marker, _ := task.LoadQuickListMarker(prefix)
		var srcService string
		if task.JobInfo != nil && task.JobInfo.SrcService != nil {
			srcService = *task.JobInfo.SrcService
		}
		err = failFileLister.ListFromCosFile(ctx, taskId, prefix, marker, t.handleQuickListResult, srcService, logger)
		if err != nil {
			taskStat := api.TaskState_FAILURE
			stopReason := err.Error()
			if err == context.Canceled { // 任务手动取消
				logger.Warn("QuickList canceled (from failFile)", zap.Error(err))
				taskStat = api.TaskState_STOP
				stopReason = "手动终止"
			} else {
				logger.Error("QuickList failed (from failFile)", zap.Error(err))
			}
			t.NotifyTaskFinalize(task, taskStat, "(retry)"+stopReason, logger)
			return
		}
		logger.Info("QuickList finished (from failFile)")
	} else {
		prefixMap, err := populatePrefixMarkerMap(task, false)
		if err != nil {
			logger.Error("failed to populate prefix-marker map", zap.Error(err))
		}

		var srcSvcType = *task.JobInfo.SrcService
		srcService, err := newObjectLister(ctx, task.JobInfo, task.IncrMsgDB, true)
		if err != nil {
			logger.Error("failed to init storage client", zap.Error(err), zap.String("platform", srcSvcType))
			return
		}

		var filters []lister.ObjectFilter
		if task.HasTimeSpanFilter() {
			logger.Info("task has time span filter")
			filters = append(filters, GetTimeSpanFilter(task))
		}
		if task.HasRegexFilter() {
			logger.Info("task has regex filter")
			regexFilter, err := GetRegexFilter(task)
			if err != nil {
				logger.Error("failed to get regex filter from task", zap.Error(err))
				return
			}
			filters = append(filters, regexFilter)
		}

		var listService = &lister.ConcurrentLister{}
		err = listService.ConcurList(ctx, taskId, prefixMap, t.handleQuickListResult, srcService, logger, filters...)
		if err != nil {
			taskStat := api.TaskState_FAILURE
			stopReason := fmt.Sprintf("list objects failed: %v", err)
			if err == context.Canceled { // 任务手动取消
				logger.Warn("QuickList canceled", zap.Error(err), zap.String("platform", srcSvcType))
				taskStat = api.TaskState_STOP
				stopReason = "手动终止"
			} else {
				logger.Error("QuickList failed", zap.Error(err), zap.String("platform", srcSvcType))
			}
			t.NotifyTaskFinalize(task, taskStat, stopReason, logger)
			return
		}
	}

	task.SetQuickListOver(true)
	if task.QuickListOver.Load() && task.QuickListFileNum.Load() == 0 && task.QuickListSize.Load() == 0 {
		logger.Info("task_admin.QuickList no matching file")
		t.NotifyTaskFinalize(task, api.TaskState_SUCCESS, "", logger)
	}
}

func doSlowListing(
	ctx context.Context,
	task *models.Task,
	listService *lister.ConcurrentLister,
	prefixMap map[string]string,
	handler lister.ResultHandler,
	logger *zap.Logger,
) error {
	// 对象存储 bucket 访问相关参数
	var taskId = task.Id

	var srcSvcType = *task.JobInfo.SrcService
	srcService, err := newObjectLister(ctx, task.JobInfo, task.IncrMsgDB, false)
	if err != nil {
		logger.Error("failed to init storage client", zap.Error(err), zap.String("platform", srcSvcType))

		if strings.HasPrefix(err.Error(), "ssrf warning") {
			errStr := strings.SplitN(err.Error(), ">", 3)[1]
			return errors.New(errStr)
		}

		return err
	}

	var filters []lister.ObjectFilter
	if task.HasTimeSpanFilter() {
		logger.Info("task has time span filter")
		filters = append(filters, GetTimeSpanFilter(task))
	}
	if task.HasRegexFilter() {
		logger.Info("task has regex filter")
		regexFilter, err := GetRegexFilter(task)
		if err != nil {
			logger.Error("failed to get regex filter from task", zap.Error(err))
			return err
		}
		filters = append(filters, regexFilter)
	}

	err = listService.ConcurList(ctx, taskId, prefixMap, handler, srcService, logger, filters...)
	return err
}

// SlowList 慢速遍历源存储(通常是 bucket)的文件, 1000 个文件作为分片上报
//nolint
func (t *TasksAdmin) SlowList(ctx context.Context, taskId string) {
	logger := pl_boot.AppCtx.GetAppLogger().With(zap.String("jobId", taskId))
	task := t.GetTask(taskId, logger)
	time.Sleep(time.Second) // why?
	if nil == task {
		logger.Error("nil task")
		return
	}
	if task.IsEnd() {
		logger.Info("task ended, no need to continue")
		return
	}
	if task.SlowListOver.Load() {
		logger.Info("slowList already over")
		return
	}
	logger.Info("task_admin.SlowList running")

	if task.Retry {
		logger.Info("retry task slow list (from failFile)")
		failFileLister, err := failfile.NewFailFilLister()
		if err != nil {
			logger.Error("failed to init failFile lister (for slow list)", zap.Error(err))
			t.NotifyTaskFinalize(task, api.TaskState_FAILURE, err.Error(), logger)
			return
		}

		var prefix = fmt.Sprintf("%s-failFile-slow", taskId)
		marker, _ := task.LoadSlowListMarker(prefix)
		var srcService string
		if task.JobInfo != nil && task.JobInfo.SrcService != nil {
			srcService = *task.JobInfo.SrcService
		}
		err = failFileLister.ListFromCosFile(ctx, taskId, prefix, marker, t.handleSlowListResult, srcService, logger)
		if err != nil {
			logger.Error("QuickList failed (from failFile)", zap.Error(err))
			t.NotifyTaskFinalize(task, api.TaskState_FAILURE, err.Error(), logger)
			return
		}
	} else {
		prefixMap, err := populatePrefixMarkerMap(task, true)
		if err != nil {
			logger.Error("failed to populate prefix-marker map", zap.Error(err))
		}

		var listService = &lister.ConcurrentLister{}
		err = doSlowListing(
			ctx, task, listService, prefixMap, t.handleSlowListResult, logger.With(zap.Bool("slow", true)))
		if err != nil {
			taskStat := api.TaskState_FAILURE
			stopReason := err.Error()
			if err == context.Canceled { // 任务手动取消
				logger.Warn("SlowList canceled", zap.Error(err))
				taskStat = api.TaskState_STOP
				stopReason = "Manually Canceled"
			} else {
				logger.Error("SlowList task err", zap.Error(err))
			}

			t.NotifyTaskFinalize(task, taskStat, stopReason, logger)
			return
		}
	}

	task.SetSlowListOver(true)
	pidNum := task.FragId.Load()
	slowSize, slowCnt := task.GetSlowListingStat()
	existingSize, existingCnt := task.GetExistingStat()
	fileNum := slowCnt + existingCnt
	size := slowSize + existingSize

	_ = apicall.GetApiCall().ReportJobSliceInfo(task.Id, task.Name, pidNum, fileNum, size)

	logger.Info(
		"task_admin.SlowList finish",
		zap.Int64("pidNum", pidNum),
		zap.Int64("fileNum", fileNum),
		zap.Int64("size", size))

	if task.SlowListOver.Load() && task.SlowListFileNum.Load() == 0 && task.SlowListSize.Load() == 0 {
		logger.Info("task_admin.SlowList No match file", zap.Int64("pidNum", pidNum))
		t.NotifyTaskFinalize(task, api.TaskState_SUCCESS, "", logger)
	}
}

// 快速遍历 bucket 的 callback
// 遍历处理每获取一次分页(page), 累加分页 size 和 cnt, 更新当前 prefix 和 marker
func (t *TasksAdmin) handleQuickListResult(taskId string, result *lister.Result) (err error) {
	logger := pl_boot.AppCtx.GetAppLogger()
	if result == nil {
		logger.Error("quickListHandler result is nil", zap.String("jobId", taskId))
		return errors.New("result is nil")
	}
	task := t.GetTask(taskId, logger)
	if task == nil {
		logger.Error("quickListHandler task is nil", zap.String("jobId", taskId))
		return errors.New("result is nil")
	}
	if task.QuickListFileNum == nil {
		logger.Error("quickListHandler QuickListFileNum is nil", zap.String("jobId", taskId))
	}
	// 更新内存中的计数
	task.AddQuickListingStat(int64(result.Size), int64(result.Num))
	task.AddSkipped(int64(result.Skip))
	task.StoreQuickListMarker(result.Prefix, result.Marker)
	if result.IsTruncated {
		logger.Info(
			"quickListHandler list finish", zap.String("jobId", task.Id),
			zap.String("Prefix", result.Prefix),
		)
	}
	logger.Info(
		"quickListHandler finish", zap.String("jobId", task.Id),
		zap.Int64("QuickListFileNum", task.QuickListFileNum.Load()),
		zap.Int64("QuickListSize", task.QuickListSize.Load()),
		zap.Int64("QuickListSkipped", task.GetSkipped()),
		zap.String("Prefix", result.Prefix),
		zap.String("Marker", result.Marker),
	)
	return err
}

// commit 提交分片会进行 5 次重试, 间隔 3s
func (t *TasksAdmin) commit(frag *models.Frag, task *models.Task, logger *zap.Logger) (err error) {
	var retry = 5
	for {
		_, err = t.HandleFrag(frag, logger)
		if err == nil {
			// 下发成功, 增加成功分片计数
			task.IncrFragmentCnt()
			return
		}

		logger.Error("failed to commit frag", zap.Error(err))
		retry -= 1
		if retry <= 0 {
			logger.Error("failed to commit frag, max retry limit reached")
			return
		}

		time.Sleep(3 * time.Second)
		continue
	}
}

// slowlist 过程的 callback
// 遍历处理每获取一次分页(page), 累加分页 size 和 cnt, 更新当前 prefix 和 marker, 将分页内容组装成分片(frag)上报
//nolint
func (t *TasksAdmin) handleSlowListResult(taskId string, result *lister.Result) (err error) {
	logger := pl_boot.AppCtx.GetAppLogger().With(zap.String("jobId", taskId))

	task := t.GetTask(taskId, logger)
	if task == nil {
		logger.Warn("SlowListHandler got nil task")
		return
	}

	logger.Info(
		"slowList handler accumulates result",
		zap.Int("result.Num", result.Num), zap.Int("result.Size", result.Size),
		zap.String("result.Marker", result.Marker), zap.String("result.Prefix", result.Prefix))

	var (
		fragBaseInfo = &models.FragBaseInfo{TaskId: taskId, FragId: 0, Files: nil}
		frag         = &models.Frag{
			BaseInfo:    fragBaseInfo,
			RuntimeInfo: models.NewFragRuntimeInfo(),
			Over:        atomic.NewBool(false),
		}
		files = make([]*api.File, 0)
	)

	// 下发分片, 会阻塞
	for _, content := range result.Contents {
		if content.Size > conf.ListerConfig.GlobalConf.FragMaxSize { // 大文件单独一个分片提交
			file := &api.File{
				FullPath: content.Key,
				Size:     content.Size,
				CurTime:  time.Now().String(),
				UploadId: content.UploadId,
			}
			fragBaseInfo.Files = append(fragBaseInfo.Files, file)
			fragBaseInfo.FragId = task.NextFragmentId() // fragId +1
			frag.BaseInfo = fragBaseInfo
			if err := t.commit(frag, task, logger); err != nil {
				logger.Error("failed to distribute frag", zap.Error(err))
				return err
			}

			fragBaseInfo.Files = fragBaseInfo.Files[:0]
			continue

		} else if len(files) >= conf.ListerConfig.GlobalConf.FragMaxNum {
			fragBaseInfo.Files = files
			fragBaseInfo.FragId = task.NextFragmentId() // fragId +1
			frag.BaseInfo = fragBaseInfo
			if err := t.commit(frag, task, logger); err != nil {
				logger.Error("failed to distribute frag", zap.Error(err))
				return err
			}
			files = files[:0]
			fragBaseInfo.Files = files
		}
		// 加入分片
		file := &api.File{
			FullPath: content.Key,
			Size:     content.Size,
			CurTime:  time.Now().String(),
			UploadId: content.UploadId,
		}
		files = append(files, file)
	}

	if len(files) > 0 {
		fragBaseInfo.Files = files
		fragBaseInfo.FragId = task.NextFragmentId() // fragId +1
		frag.BaseInfo = fragBaseInfo
		if err := t.commit(frag, task, logger); err != nil {
			logger.Error("failed to distribute frag", zap.Error(err))
			return err
		}
		// frag 置空
		files = files[:0]
		fragBaseInfo.Files = files
	}

	// NOTE: 分片下发完后更新marker，防止中途重启丢文件
	task.AddSlowListingStat(int64(result.Size), int64(result.Num))
	task.StoreSlowListMarker(result.Prefix, result.Marker)
	logger.Info(
		"slowListHandle params",
		zap.Int64("task.SlowListFileNum", task.SlowListFileNum.Load()),
		zap.Int64("task.SlowListSize", task.SlowListSize.Load()))

	return err
}

// 获取 task 对应的 cancel func
func (t *TasksAdmin) GetCancelFunc(taskId string) (context.CancelFunc, bool) {
	return t.cancelFs.Load(taskId)
}

// 单个任务分片心跳
func (t *TasksAdmin) FragHBCron(ctx context.Context, taskId string) error {
	logger := pl_boot.AppCtx.GetAppLogger().With(zap.String("jobId", taskId))

	// 内存中是否还有这个任务
	task := t.GetTask(taskId, logger)
	if task == nil {
		logger.Warn("got nil task, maybe already pruned")
		return nil
	}

	// TODO: 5 分钟1次是否太长了?
	interval := time.Duration(conf.ListerConfig.GlobalConf.JudgeFragTimeoutInterval) * time.Second
	logger.Info("start frag heart beat cron", zap.Any("interval", interval.String()))
	timer := time.NewTimer(interval)
	for {
		select {
		case <-ctx.Done():
			logger.Info("frag heart beat canceled", zap.Error(ctx.Err()))
			if !timer.Stop() {
				<-timer.C
			}
			return ctx.Err()
		case <-timer.C:
			break
		}

		// 报告一次实时进度
		apicall.GetApiCall().ReportTaskProgress(task, logger)
		logger.Info("ReportTaskProgress Once Finish")

		// 内存中的任务是否到达最终状态
		state := task.GetState()
		if IsFinalState(state) {
			logger.Info(
				"task reached final state, send finalize request",
				zap.String("jobId", taskId), zap.Int("state", int(state)))
			t.NotifyTaskFinalize(task, state, "", logger)
			return nil
		}

		rawFrags, ok := t.unfinishedFrags.Load(taskId)
		if !ok {
			logger.Error("task has no unfinished frag set")
			return fmt.Errorf("%s has no unfinished frag set", taskId)
		}
		unfinishedFragSet, ok := rawFrags.(*sync.Map)
		if !ok {
			logger.Error(
				fmt.Sprintf("got unknown unfinished frag set type, expect *sync.Map, got %T", rawFrags))
			return fmt.Errorf("%s got unknown unfinished frag type %T", taskId, rawFrags)
		}

		// 处理未完成的分片
		// NOTE: 分片下发完了, 而且没有未完成分片, 标记任务成功
		// 	添加 unfinishedFrags 肯定在 slowListOver=true 之前, 不会出现遍历完了 unfinishedFrags 还没加上的情况
		if task.QuickListOver.Load() && task.SlowListOver.Load() && t.lenSyncMap(unfinishedFragSet) == 0 {
			logger.Info("task quick & slow list over, 0 unfinished frags, will be marked success")
			t.NotifyTaskFinalize(task, api.TaskState_SUCCESS, "", logger)
			return nil
		}

		logger.Info("frag heart beat cron put to sleep",
			zap.Any("quickListOver", task.QuickListOver.Load()),
			zap.Any("slowListOver", task.SlowListOver.Load()),
			zap.Any("unfinished frags", t.lenSyncMap(unfinishedFragSet)))
		timer.Reset(interval)
	}
}

// 重试未完成分片, 增加重试次数或者标记为失败
// NOTE: 上传 fragResult 耗时 70ms, 重新下发分片可能阻塞
func (t *TasksAdmin) reDistributeFrag(frag *models.Frag, fragsInQueue []*models.Frag, logger *zap.Logger) {
	logger = logger.With(zap.Int64("fragId", frag.BaseInfo.FragId))
	if frag.RuntimeInfo.Retry.Load() > conf.ListerConfig.GlobalConf.FragRetryMaxTimes {
		logger.Info(
			"exceed frag max retry", zap.Int64("frag retry", frag.RuntimeInfo.Retry.Load()))
		fragResult := models.ConstructFragResult(frag)

		// NOTE: 所有文件标记成失败
		for fileId, file := range frag.BaseInfo.Files {
			file.FailedReason = "Network congestion: Frag retry too many times timeout. Please retry failed files."
			fragResult.AddFile(int64(fileId), file, false)
		}

		t.AddFragResult(fragResult, logger)
	} else {
		// frag还在queue里面，说明没有人执行它，此时不重发，避免多次重发判失败
		for _, oneFrag := range fragsInQueue {
			if frag.BaseInfo.FragId == oneFrag.BaseInfo.FragId {
				logger.Info("frag timeout, but no one execute it, not re-distribute and re-calc timeout",
					zap.Time("runtimeInfo-LastHeart", frag.RuntimeInfo.LastHeart.Load().(time.Time)))
				frag.UpdateLastHeart()
				frag.UpdateFirstHeart()
				return
			}
		}

		frag.IncrRetry()

		logger.Info(
			"frag re-distribute start",
			zap.Time("runtimeInfo-LastHeart", frag.RuntimeInfo.LastHeart.Load().(time.Time)),
		)

		_, _ = t.DistributeFrag(frag, logger)
		logger.Info(
			"frag re-distribute over",
			zap.Time("runtimeInfo-LastHeart", frag.RuntimeInfo.LastHeart.Load().(time.Time)),
		)
	}
}

// 重发心跳超时的分片
func (t *TasksAdmin) RedistributeTimeoutFrags(ctx context.Context, taskId string) error {
	logger := pl_boot.AppCtx.GetAppLogger().With(zap.String("jobId", taskId))

	// 内存中是否还有这个任务
	task := t.GetTask(taskId, logger)
	if task == nil {
		logger.Warn("got nil task, maybe already pruned")
		return nil
	}

	interval := time.Duration(30) * time.Second
	logger.Info("start frag redistribute timeout frags cron", zap.Any("interval", interval.String()))
	timer := time.NewTimer(interval)
	for {
		select {
		case <-ctx.Done():
			logger.Info("redistribute timeout frags canceled", zap.Error(ctx.Err()))
			if !timer.Stop() {
				<-timer.C
			}
			return ctx.Err()
		case <-timer.C:
			break
		}

		// 内存中的任务是否到达最终状态
		state := task.GetState()
		if IsFinalState(state) {
			logger.Info(
				"task reached final state, exit redistribute frags",
				zap.String("jobId", taskId), zap.Int("state", int(state)))
			return nil
		}

		rawFrags, ok := t.unfinishedFrags.Load(taskId)
		if !ok {
			logger.Error("task has no unfinished frag set")
			return fmt.Errorf("%s has no unfinished frag set", taskId)
		}
		unfinishedFragSet, ok := rawFrags.(*sync.Map)
		if !ok {
			logger.Error(
				fmt.Sprintf("got unknown unfinished frag set type, expect *sync.Map, got %T", rawFrags))
			return fmt.Errorf("%s got unknown unfinished frag type %T", taskId, rawFrags)
		}

		needRetrySet := extractNeedRetrySet(unfinishedFragSet, logger)
		if len(needRetrySet) > 0 {
			logger.Info("frags re-distribute start", zap.Int("cnt", len(needRetrySet)))
			fragsInQueue := scheduler.DumpFrags(taskId)
			for _, frag := range needRetrySet {
				t.reDistributeFrag(frag, fragsInQueue, logger)
			}
			logger.Info("frags re-distribute done", zap.Int("cnt", len(needRetrySet)))
		}

		logger.Info("redistribute timeout frags cron put to sleep",
			zap.Any("unfinished frags", t.lenSyncMap(unfinishedFragSet)))
		timer.Reset(interval)
	}
}

// 找出未完成的分片中心跳超时的
func extractNeedRetrySet(
	unfinishedFragSet *sync.Map, logger *zap.Logger,
) (needRetry []*models.Frag) {
	// 找到未完成分片中需要重试的
	unfinishedFragSet.Range(
		func(k, v interface{}) bool {
			frag := v.(*models.Frag)
			logger := logger.With(zap.Int64("fragId", frag.BaseInfo.FragId))

			if time.Now().Sub(frag.RuntimeInfo.LastHeart.Load().(time.Time)).Seconds() >
				float64(conf.ListerConfig.GlobalConf.FragTimeout) {
				// 需要超时分片重发
				logger.Info("frag last heart beat timeout, add to retry set")
				needRetry = append(needRetry, frag)
				return true
			}

			if time.Now().Sub(frag.RuntimeInfo.FirstHeart.Load().(time.Time)).Seconds() >
				float64(conf.ListerConfig.GlobalConf.FragDeadTimeout) {
				logger.Info("frag first heart beat timeout, add to retry set")
				needRetry = append(needRetry, frag)
			}
			return true
		})

	return
}
