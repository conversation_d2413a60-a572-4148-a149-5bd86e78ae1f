package tasks

import (
	"time"

	"git.code.oa.com/cloud-msp/migration-lister/api"
	"git.code.oa.com/cloud-msp/migration-lister/internal/service/apicall"
	"git.code.oa.com/cloud-msp/migration-lister/internal/service/coscall"
	"go.uber.org/zap"
)

// 任务应当被如何结束
type TaskConclusion struct {
	State      api.TaskState // 任务的最终状态
	StopReason string        // 任务中止原因, required if State == TaskState_FAILURE
	JobId      string        // 任务唯一标志
}

// String
func (c *TaskConclusion) String() string {
	content, _ := json.Marshal(c)
	return string(content)
}

// 通知队列的容量
const queCap = 1000

// 接收通知给 task 做清理工作
// 用缓冲 chan 防止协程泄漏
var needFinalizeCh chan *TaskConclusion

// 清理 task 的 handler
type FinalizeHandler func(*TaskConclusion, *zap.Logger) error

// 准备结束的任务发送 finalize 请求
func SendFinalizeRequest(c *TaskConclusion) {
	needFinalizeCh <- c
}

// monitor 模式后台 goroutine
func serve(logger *zap.Logger, handler FinalizeHandler) {
	for c := range needFinalizeCh {
		if c == nil {
			logger.Warn("got nil *TaskConclusion")
			continue
		}
		logger.Info(
			"ready to finalize task", zap.String("conclusion", c.String()))

		go func(tc *TaskConclusion) {
			err := handler(tc, logger)
			if err != nil {
				logger.Error(
					"failed to finalize task",
					zap.Error(err), zap.String("conclusion", c.String()))
			}
		}(c)
	}
}

// 终结一个任务
// 结束失败文件分块上传, 更新数据库, 从内存中删除 task
// 错误重试可能阻塞, 协程执行
func finalizeTask(c *TaskConclusion, logger *zap.Logger) error {
	taskId := c.JobId
	taskAdmin := GetTaskAdmin()

	logger = logger.With(zap.String("jobId", taskId))

	// 内存中是否还有这个任务
	task := taskAdmin.GetTask(taskId, logger)
	if task == nil {
		logger.Warn("got nil task, maybe already pruned")
		return nil
	}

	// 记录总耗时
	start := time.Now()
	defer func() {
		timeElapsed := time.Now().Sub(start).Seconds()
		logger.Info("task finalized", zap.Float64("cost", timeElapsed))
	}()

	task.Mutex.Lock()
	defer task.Mutex.Unlock()

	// 关闭失败文件上传
	// TODO: 错误处理，否则可能出现失败列表丢失前功尽弃
	coscall.GetMspCosCall().EndReportFailedFiles(taskId)

	// 更新数据库状态
	// NOTE：必须确保任务状态更新完成再删除内存中的任务, 否则会导致任务重复拉取启动
	UpdateTaskState(task.Id, c.State)
	// optional 上报失败原因
	if c.State == api.TaskState_FAILURE || len(c.StopReason) != 0 {
		err := apicall.GetApiCall().ReportJobStopReason(taskId, c.StopReason)
		if err != nil {
			logger.Warn("failed to report task stop reason", zap.Error(err))
		}
	}

	// 内存中改成最终状态
	state := task.GetState()
	if !IsFinalState(state) {
		task.SetState(c.State)
	}

	// 最后清理内存
	// 如果清理不了没关系, 重启以后分片心跳协程会再次发起
	taskAdmin.PruneTask(taskId)

	// 关闭存储任务备份的数据库文件
	if err := task.CloseBackupDB(); err != nil {
		logger.Info("close backup db error", zap.String("taskId", task.Id), zap.Error(err))
	} else {
		logger.Info("close backup db success", zap.String("taskId", task.Id))
	}

	// 关闭增量任务的增量消息文件
	if *task.JobInfo.IncrSrcInfo != "" {
		if err := task.CloseIncrDB(); err != nil {
			logger.Info("close incr db error", zap.String("taskId", task.Id), zap.Error(err))
		} else {
			logger.Info("close incr db success", zap.String("taskId", task.Id))
		}
	}

	return nil
}

// 模块初始化, 启动 monitor
func InitTaskFinalizer(logger *zap.Logger) {
	needFinalizeCh = make(chan *TaskConclusion, queCap)
	go serve(logger, finalizeTask)
}
