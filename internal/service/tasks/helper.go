package tasks

import (
	"context"
	"fmt"
	"net/url"
	"strings"

	"git.code.oa.com/cloud-msp/migration-lister/internal/service/models"
	"git.code.oa.com/cloud-msp/migration-lister/pkg/conf"
	"git.code.oa.com/cloud-msp/migration-lister/pkg/lister/azure"
	"git.code.oa.com/cloud-msp/migration-lister/pkg/lister/innercos"
	"git.code.oa.com/cloud-msp/migration-lister/pkg/lister/tdmq"
	"github.com/syndtr/goleveldb/leveldb"

	"git.code.oa.com/cloud-msp/migration-lister/pkg/lister/s3protocol"
	"git.code.oa.com/pulse-line/pl_boot"
	"go.uber.org/zap"

	"git.code.oa.com/cloud-msp/migration-lister/internal/service/consts"
	"git.code.oa.com/cloud-msp/migration-lister/pkg/lister"
	"git.code.oa.com/cloud-msp/migration-lister/pkg/lister/aws"
	"git.code.oa.com/cloud-msp/migration-lister/pkg/lister/bos"
	"git.code.oa.com/cloud-msp/migration-lister/pkg/lister/cos"
	"git.code.oa.com/cloud-msp/migration-lister/pkg/lister/kodo"
	"git.code.oa.com/cloud-msp/migration-lister/pkg/lister/ks3"
	"git.code.oa.com/cloud-msp/migration-lister/pkg/lister/obs"
	"git.code.oa.com/cloud-msp/migration-lister/pkg/lister/oss"
	"git.code.oa.com/cloud-msp/migration-lister/pkg/lister/ucloud"
	"git.code.oa.com/cloud-msp/migration-lister/pkg/lister/urls"
	"git.code.oa.com/cloud-msp/migration-lister/pkg/lister/uss"
	"git.code.oa.com/cloud-msp/migration-lister/tools"
)

func newObjectLister(ctx context.Context, jobInfo *tools.JobInfo, db *leveldb.DB, isQuick bool) (lister.FileLister, error) {
	svcType := *jobInfo.SrcService
	accessKey := *jobInfo.SrcSecretID
	secretKey := *jobInfo.SrcSecretKey
	bucket := *jobInfo.SrcBucket
	region := *jobInfo.SrcRegion
	endpoint := ""
	manifestURL := *jobInfo.ManifestURL
	if len(manifestURL) != 0 {
		pl_boot.AppCtx.GetAppLogger().Info("newObjectLister enter manifest",
			zap.Any("jobId", jobInfo.JobID))
		return lister.NewManifestLister(manifestURL), nil
	}

	incr := *jobInfo.IncrSrcInfo
	if incr != "" {

		return tdmq.NewTdmqLister(ctx, *jobInfo.JobID,
			conf.ListerConfig.IncreaseConfig.SecretId,
			conf.ListerConfig.IncreaseConfig.SecretKey,
			incr, db, isQuick,
		)
	}
	switch svcType {
	case consts.OBS:
		return obs.NewOBSObjectLister(accessKey, secretKey, bucket, region, endpoint)
	case consts.OSS:
		// NOTE: console 传进来的 region 是 endpoint
		endpoint := region
		return oss.NewOSSLister(accessKey, secretKey, endpoint, bucket)
	case consts.S3, consts.S3Inter:
		return aws.NewS3ObjectLister(accessKey, secretKey, bucket, region)
	case consts.BOS:
		return bos.NewBOSObjectLister(accessKey, secretKey, bucket, region, endpoint)
	case consts.KODO:
		endpoint = *jobInfo.KodoEndpoint
		endpoint = strings.Trim(endpoint, " ")
		return kodo.NewKODOS3Lister(accessKey, secretKey, bucket, region, endpoint)
	case consts.KODOCDN:
		// NOTE: console 传进来的 region 是 DomainURL 用于加速下载, 遍历只需要知道 bucket
		return kodo.NewKODOCDNLister(accessKey, secretKey, bucket, "")
	case consts.S3Protocol:
		endpoint = *jobInfo.KodoEndpoint
		endpoint = strings.Trim(endpoint, " ")
		return s3protocol.NewS3Lister(accessKey, secretKey, bucket, endpoint, region)
	case consts.KS3:
		// NOTE: console 传进来的 region 是 endpoint
		endpoint := region
		return ks3.NewKS3ObjectLister(accessKey, secretKey, bucket, "", endpoint)
	case consts.COS:
		return cos.NewCOSLister(accessKey, secretKey, bucket, region)
	case consts.InnerCOS:
		// NOTE: console 传进来的 region 是 endpoint
		endpoint := region
		return innercos.NewCOSLister(accessKey, secretKey, bucket, endpoint)
	case consts.UFile:
		// NOTE: console 传进来的 region 是 Filehost
		fileHost := region
		return ucloud.NewUFileLister(accessKey, secretKey, bucket, fileHost), nil
	case consts.USS:
		return uss.NewUSSObjectLister(accessKey, secretKey, bucket, region)
	case consts.URLs:
		// 从 cos 桶中获取 msp_ 开头的文件
		manifestURL := *jobInfo.SrcFileName
		if len(manifestURL) == 0 {
			// 通过 http 获取
			manifestURL = *jobInfo.SrcFileURL
		}
		return urls.NewURLsLister(manifestURL), nil
	case consts.AZURE:
		return azure.NewAzureBlobLister(accessKey, secretKey, bucket, region)
	default:
		return nil, fmt.Errorf("invalid argument: unknown service %s", svcType)
	}
}

// GetRegexFilter 将 task 携带的 regex 过滤列表打包成一个 filter func, 按照 file.Key 过滤遍历结果
func GetRegexFilter(task *models.Task) (func(*lister.Content) bool, error) {
	exps, err := task.GetRegexps()
	if err != nil {
		return nil, err
	}

	return func(object *lister.Content) bool {
		decodeKey, err := url.QueryUnescape(object.Key)
		if err != nil {
			pl_boot.AppCtx.GetAppLogger().Error("GetRegexFilter Decode Key Error",
				zap.Any("jobId", task.JobInfo.JobID),
				zap.Error(err),
				zap.Any("key", object.Key),
			)
			return false
		}
		pl_boot.AppCtx.GetAppLogger().Info("regex key",
			zap.Any("jobId", task.JobInfo.JobID),
			zap.Any("key", decodeKey),
		)
		for _, matcher := range exps {
			ok, err := matcher.MatchString(decodeKey)

			if err != nil {
				pl_boot.AppCtx.GetAppLogger().Error("regex key match error",
					zap.Any("key", decodeKey),
					zap.Error(err),
				)
				continue
			}

			if ok {
				return true
			}
		}
		return false
	}, nil
}

// GetTimeSpanFilter i将 task 携带的 regex 过滤列表打包成一个 filter func, 用于过滤遍历结果
func GetTimeSpanFilter(task *models.Task) func(*lister.Content) bool {
	startT, endT := task.GetTimeSpan()
	return func(object *lister.Content) bool {
		ts := object.LastModified
		if ts.Before(endT) && ts.After(startT) {
			return true
		}
		return false
	}
}
