package innercall

import (
	"encoding/json"
	"sync"
	"time"

	"git.code.oa.com/cloud-msp/migration-lister/api"
	"git.code.oa.com/cloud-msp/migration-lister/internal/service/models"
	"git.code.oa.com/cloud-msp/migration-lister/pkg/conf"
	"git.code.oa.com/cloud-msp/migration-lister/pkg/metrics"
	"git.code.oa.com/cloud-msp/migration-lister/tools"
	"git.code.oa.com/pulse-line/pl_boot"
	"git.code.oa.com/pulse-line/pl_prom"
	"github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common"
	"github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common/profile"
	"go.uber.org/zap"
)

var (
	innerApi *InnerApiCall
	once     sync.Once
)

// 对应cos_migrate_tool中的 InnerApiCall
type InnerApiCallInterface interface {
	//  获取该账号下全部未结束任务
	GetTasks(diversionId string) ([]*models.Task, error)
	GetWorkerIpChannel() ([]*tools.ChannelIp, error)
}

// InnerApiCall 调用内部 API (migration-project) 获取迁移任务
type InnerApiCall struct {
	Client *tools.Client
}

// JobProgress 迁移任务进度统计
type JobProgress struct {
	FileFailNumTotal     int64
	FileFailSizeTotal    int64
	FileSuccessNumTotal  int64
	FileSuccessSizeTotal int64
	FileTotal            string
	FileSizeTotal        string
}

/**画
 *由于go的初始化机制，需要手动调用初始化
 */
func GetInnerApiCall() *InnerApiCall {
	once.Do(func() {
		credential := common.NewCredential(
			conf.ListerConfig.TencentCloud.SecretId,
			conf.ListerConfig.TencentCloud.SecretKey,
		)
		cpf := profile.NewClientProfile()
		cpf.HttpProfile.Endpoint = conf.ListerConfig.TencentCloud.Host
		client, _ := tools.NewClient(credential, "", cpf)
		innerApi = &InnerApiCall{
			Client: client,
		}
		pl_boot.AppCtx.GetAppLogger().Info(
			"init inner api success", zap.Any("conf", conf.ListerConfig.TencentCloud))
	})
	return innerApi
}

// GetTasks 拉取迁移任务 (请求 MAListMigrationJobsV2 接口)
// 如果是已经存在的任务, task.ExistFileSuccessSize 会被设置
// NOTE: 拉到的任务状态
//   CREATED,
//   CHECKING,
//   CHECKED_SUCCESSFUL
//   READY,
//   RUNNING,
//   RETRY,
func (a *InnerApiCall) GetTasks(diversionId string, logger *zap.Logger) ([]*models.Task, error) {
	res := make([]*models.Task, 0)

	request := tools.NewMAListMigrationJobsV2Request()
	request.JobId = &diversionId

	// 统计TP性能
	startTime := time.Now()
	logger.Info("MAListMigrationJobsV2 start", zap.String("diversionId", diversionId))
	response, err := a.Client.MAListMigrationJobsV2(request)
	pl_prom.GetHistogramWithValues(metrics.RpcHistogram, metrics.Lister, "GetTask", "all", "").
		Observe(float64(time.Now().Sub(startTime).Microseconds()))
	if nil != err {
		return nil, err
	}

	infos := response.Response.JobsInfo
	logger.Info("MAListMigrationJobsV2 result",
		zap.Any("reqId", response.Response.RequestId), zap.String("diversionId", diversionId))

	if infos == nil || len(infos) == 0 {
		logger.Info(
			"describeJobList result ", zap.Int("The length of JobInfos", len(infos)))
		return res, nil
	}

	for _, job := range infos {
		// 如果是增量任务, 需要等待增量源信息创建完毕
		if *job.IsIncrease == 1 && len(*job.IncrSrcInfo) == 0 {
			logger.Warn("increase job is not ready", zap.Any("job", job))
			continue
		}
		task := models.NewTask()
		err = a.ConstructAllTaskInfo(task, job)
		if err != nil {
			logger.Error("ConstructAllTaskInfo error", zap.Error(err), zap.Any("job", job))
			pl_prom.GetCounterWithValues(metrics.ErrorCount, metrics.Lister, "GetTasks", "all", "")
			continue
		}
		if task.JobInfo == nil {
			logger.Warn("MAListMigrationJobsV2 task constructed with nil JobInfo")
			continue
		}
		if task.JobInfo.BucketType == nil {
			logger.Warn(
				"MAListMigrationJobsV2 task has nil JobInfo.BucketType", zap.Any("task.JobInfo", task.JobInfo))
			continue
		}

		res = append(res, task)
	}
	return res, nil
}

// ConstructAllTaskInfo
func (a *InnerApiCall) ConstructAllTaskInfo(task *models.Task, job *tools.JobInfo) (err error) {
	logger := pl_boot.AppCtx.GetAppLogger().With(zap.Stringp("jobId", job.JobID))

	logger.Info("InnerApiCall.Getmodels.response", zap.Any("job", job))
	speedLimit := *job.LimitSpeed * 1024 * 1024 / 8

	// 这里的 task 实例是新构建出来的, 不是当前运行的任务实例, 所以这里不能将 task 各个字段值落盘
	task.Id = *job.JobID
	task.SpeedLimit = speedLimit
	task.QpsLimit = int64(*job.LimitQPS)
	task.Priority = *job.Priority
	task.SpeedLimitInfo = *job.SpeedLimitInfo
	agentInfo := make(map[string]interface{})
	if *job.AgentInfo != "" {
		err = json.Unmarshal([]byte(*job.AgentInfo), &agentInfo)
		if err != nil {
			logger.Error(
				"json decode agent info error",
				zap.Any("info", job.AgentInfo),
				zap.Any("job", job),
				zap.Error(err))
			// 失败统计
			pl_prom.GetCounterWithValues(metrics.ErrorCount, metrics.Lister, "ConstructAllTaskInfo", task.Id, "")
		}
	}
	if value, ok := agentInfo["LimitAgentSpeed"]; ok {
		if speed, ok := value.(string); ok {
			task.AgentSpeedLimit = tools.StrConInt(speed)
		} else {
			logger.Warn("failed to parse AgentInfo.LimitAgentSpeed", zap.String("LimitAgentSpeed", speed))
		}
	}

	state := api.TaskState(tools.StrConInt32(*job.JobStatus))
	task.State.Store(int64(state))
	logger.Info("InnerApiCall.Getmodels.Job.Status", zap.Any("Status", state))
	if job.JobProgress != nil && *job.JobProgress != "" {
		jobProgress := &JobProgress{}
		s := []byte(*job.JobProgress)
		err := json.Unmarshal(s, jobProgress)
		if nil != err {
			logger.Error("job progress convert object fail", zap.Error(err))
			// 失败统计
			pl_prom.GetCounterWithValues(metrics.ErrorCount, metrics.Lister, "ConstructAllTaskInfo", task.Id, "")
		} else {
			task.ExistFileSuccessNum.Store(jobProgress.FileSuccessNumTotal)
			task.ExistFileSuccessSize.Store(jobProgress.FileSuccessSizeTotal)
			logger.Info(
				"InnerApiCall.Getmodels.Job.Status",
				zap.Int64("FileSuccessNumTotal", jobProgress.FileSuccessNumTotal),
				zap.Int64("FileSuccessSizeTotal", jobProgress.FileSuccessSizeTotal))
		}
	}
	// 用户的Access Key ID和Secret Access Key
	srcSecretId, err := tools.DecryptSecret(*job.SrcSecretID)
	if err != nil {
		logger.Error(
			"Decrypt srcSecretID err", zap.Any("srcSecretID", *job.SrcSecretID), zap.Error(err))
		return
	}
	srcSecretKey, err := tools.DecryptSecret(*job.SrcSecretKey)
	if err != nil {
		logger.Error("Decrypt srcSecretKey err", zap.Any("SrcSecretKey", *job.SrcSecretKey), zap.Error(err))
		return
	}
	dstSecretId, err := tools.DecryptSecret(*job.DstSecretID)
	if err != nil {
		logger.Error("Decrypt dstSecretId err", zap.Any("dstSecretId", *job.DstSecretID), zap.Error(err))
		return
	}
	dstSecretKey, err := tools.DecryptSecret(*job.DstSecretKey)
	if err != nil {
		logger.Error("Decrypt dstSecretKey err", zap.Any("dstSecretKey", *job.DstSecretKey), zap.Error(err))
		return
	}
	job.SrcSecretID = &srcSecretId
	job.SrcSecretKey = &srcSecretKey
	job.DstSecretID = &dstSecretId
	job.DstSecretKey = &dstSecretKey
	task.JobInfo = job

	return nil
}

// GetWorkerIpChannel 获取 channel 中的主机
func (a *InnerApiCall) GetWorkerIpChannel() ([]*tools.ChannelIp, error) {
	res := make([]*tools.ChannelIp, 0)

	logger := pl_boot.AppCtx.GetAppLogger()

	request := tools.NewMADescribeChannelIpRequest()
	params := "{}"
	err := request.FromJsonString(params)
	if err != nil {
		logger.Info("describeChannelIp FromJsonString err", zap.Any("err", err))
		// 失败统计
		pl_prom.GetCounterWithValues(metrics.ErrorCount, metrics.Lister, "GetWorkerIpChannel", "all", "")
		return nil, err
	}
	// 统计TP性能
	startTime := time.Now()
	response, err := a.Client.MADescribeChannelIp(request)
	pl_prom.GetHistogramWithValues(metrics.RpcHistogram, metrics.Lister, "GetWorkerIpChannel", "all", "").
		Observe(float64(time.Now().Sub(startTime).Microseconds()))
	if nil != err {
		// 失败统计
		pl_prom.GetCounterWithValues(metrics.ErrorCount, metrics.Lister, "GetWorkerIpChannel", "all", "")
		return nil, err
	}

	res = response.Response.Ips
	if res == nil || len(res) == 0 {
		logger.Error("DescribeChannelIp result ", zap.Int("The length of Ips", len(res)))
		return res, nil
	}

	return res, nil
}
