package completement

import (
	"git.code.oa.com/cloud-msp/migration-lister/internal/service/models"
	"git.code.oa.com/pulse-line/pl_boot"
	"go.uber.org/zap"
	"sync"
)

var (
	completeService *CompleteServiceImpl
	once            sync.Once
)

func init() {
	once.Do(func() {
		completeService = &CompleteServiceImpl{}
	})
}

// CompleteService 补全 task 运行时信息
type CompleteService interface {
	// 构建补全完整的task信息
	ConstructTask(t *models.Task)
}

// CompleteServiceImpl 实现 CompleteService 接口
type CompleteServiceImpl struct {
}

// GetCompleteService 单例模式获取 CompleteService 接口实例
func GetCompleteService() CompleteService {
	return completeService
}

// ConstructTask 补全 task 信息
func (c *CompleteServiceImpl) ConstructTask(t *models.Task) {
	logger := pl_boot.AppCtx.GetAppLogger().With(zap.String("jobId", t.Id))
	logger.Info("CompleteService.ConstructTask")
	// init base info
	logger.Info(
		"ConstructTask init ...",
		zap.Int64("SuccessfulSize", t.SuccessfulSize.Load()),
		zap.Int64("SuccessfulFileNum", t.SuccessfulFileNum.Load()),
		zap.Int64("FailedSize", t.FailedSize.Load()),
		zap.Int64("FailedFileNum", t.FailedFileNum.Load()))
}
