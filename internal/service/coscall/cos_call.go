package coscall

import (
	"context"
	"fmt"
	"math/rand"
	"path/filepath"
	"strconv"
	"strings"
	"time"

	"git.code.oa.com/cloud-msp/migration-lister/pkg/conf"
	"git.code.oa.com/cloud-msp/migration-lister/pkg/defaultCosClient"

	"git.code.oa.com/cloud-msp/migration-lister/api"
	"git.code.oa.com/cloud-msp/migration-lister/internal/service/apicall"
	"git.code.oa.com/cloud-msp/migration-lister/internal/service/taskupload"
	"git.code.oa.com/cloud-msp/migration-lister/pkg/metrics"
	"git.code.oa.com/cloud-msp/migration-lister/tools"
	"git.code.oa.com/pulse-line/pl_boot"
	"git.code.oa.com/pulse-line/pl_prom"
	"github.com/tencentyun/cos-go-sdk-v5"
	"go.uber.org/atomic"
	"go.uber.org/zap"
)

var (
	cosApi *MspCosCall
)

const (
	ResultPath = "failFiles"
)

// MspCosCall 上传失败文件列表到 COS
type MspCosCall struct {
}

/**
 *由于go的初始化机制，需要手动调用初始化
 */
var GetMspCosCall = func() MspCosCallIF {
	return cosApi
}

// GetLatestFailedFile 获取最近的失败文件
func (m *MspCosCall) GetLatestFailedFile(taskId string) *string {
	opt := &cos.BucketGetOptions{
		Prefix: fmt.Sprintf("%s/%s.csv", ResultPath, taskId),
	}
	result, _, err := defaultCosClient.GetClient().Bucket.Get(context.Background(), opt)
	if nil != err {
		pl_boot.AppCtx.GetAppLogger().Error("get latest failed file ", zap.String("result_path", ""), zap.Error(err))
		return nil
	}

	if result.Contents == nil {
		return nil
	}
	var lastFilePath string
	var lastVersion int
	var version int
	for _, v := range result.Contents {
		_, fileName := filepath.Split(v.Key)
		fileNameSplits := strings.Split(fileName, ".")
		pl_boot.AppCtx.GetAppLogger().Info("GetLatestFailedFile fileNameSplits", zap.Any("detail", fileNameSplits))
		if len(fileNameSplits) > 2 {
			version = tools.StrConInt(fileNameSplits[2])
		} else {
			version = 0
		}

		if lastVersion == 0 || version > lastVersion {
			lastVersion = version
			lastFilePath = v.Key
		}
	}

	return &lastFilePath
}

// 获取包含版本号的失败文件列表
func (m *MspCosCall) getFailFilePathWithVersion(service taskupload.TaskUploadService, taskId string) *string {
	failFilePath := service.GetTaskToFailedPathById(taskId)
	if failFilePath == nil {
		failFilePath = m.GetLatestFailedFile(taskId)
		if failFilePath == nil {
			filePath := fmt.Sprintf("%s/%s.csv", ResultPath, taskId)
			failFilePath = &filePath
		} else {
			_, file := filepath.Split(*failFilePath)
			fileName := strings.Split(file, ".")
			var version int32
			if len(fileName) > 2 {
				version = tools.StrConInt32(fileName[2])
			} else {
				version = 0
			}
			version++
			filePath := fmt.Sprintf("%s/%s.csv.%d", ResultPath, taskId, version)
			failFilePath = &filePath
		}
		service.PutTaskIdFailedMap(taskId, failFilePath)
	}
	return failFilePath
}

// ReportFailedFiles 上报失败文件列表
func (m *MspCosCall) ReportFailedFiles(fragResult *api.FragResult) bool {
	logger := pl_boot.AppCtx.GetAppLogger().With(zap.String("jobId", fragResult.TaskId))
	if fragResult.FailedNum == 0 {
		logger.Info("no failed files, skip ReportFailedFiles")
		return true
	}

	service := taskupload.GetUploadService()
	taskUploadInfo := service.GetTaskUploadInfoById(fragResult.TaskId)
	if taskUploadInfo == nil {
		// 初始化 taskUploadInfo
		taskUploadInfo = service.PutTaskUploadAndGet(fragResult.TaskId, service.InitTaskUploadInfo())
	} else if taskUploadInfo.Over.Load() { // 如果存在且已经完成的话，直接返回
		logger.Info("multipart upload already completed, skip ReportFailedFiles")
		return true
	}

	// 获取文件路径 并构建文件路径
	failedFilePath := m.getFailFilePathWithVersion(service, fragResult.TaskId)

	// 已经在外层加锁了
	// task总多frag情况下cos会存在毫秒级别的并发，需要加锁
	// lock := service.GetTaskLockByTaskId(fragResult.TaskId)
	// defer func() {
	// 	// taskUploadInfo.ChildUploadOver.Swap(true)
	// 	lock.Unlock()
	// }()
	// lock.Lock()
	// taskUploadInfo.ChildUploadOver.Swap(false)

	apiService := apicall.GetApiCall()
	if taskUploadInfo.UploadId == "" {
		logger.Info("ReportFailedFiles taskUploadInfo.UploadId is empty")
		// ReportTaskResultUploadState 调用：MAReportJobResultStoreStatus，上报任务失败文件上传到cos的状态
		if !apiService.ReportTaskResultUploadState(fragResult.TaskId, apicall.UploadBeginning) {
			return false
		}
		// 分块上传初始化
		uploadId := m.StartMultiUpload(*failedFilePath, fragResult.TaskId)
		if uploadId == "" {
			return false
		}

		duplicate := m.DeepCopy(taskUploadInfo)
		duplicate.UploadId = uploadId
		if !m.UploadPart(
			duplicate, *failedFilePath, "FileName,FileSize(B),FailureTime,FailureReason,UploadId\n", false, fragResult.TaskId) {
			// 改为英文
			// duplicate, *failedFilePath, "文件名称,文件大小(B),失败时间,失败原因,UploadId\n", false, fragResult.TaskId) {
			return false
		}
		// 各调用均无问题才能更新task_upload_info
		taskUploadInfo.Update(duplicate)
	}

	if !apiService.ReportTaskResultUploadState(fragResult.TaskId, apicall.UPLOADING) {
		logger.Info(
			"ReportFailedFiles apiService.ReportTaskResultUploadState res", zap.Bool("res", false))
		return false
	}

	duplicate := m.DeepCopy(taskUploadInfo)

	if !m.UploadPart(duplicate, *failedFilePath, tools.DumpFailedFile(fragResult), false, fragResult.TaskId) {
		return false
	} else {
		taskUploadInfo.Update(duplicate)
	}

	logger.Info("ReportFailedFiles sync taskUploadInfo")
	service.PutTaskUploadInfo(fragResult.TaskId, taskUploadInfo)

	logger.Info("ReportFailedFiles Success", zap.String("filePath", *failedFilePath))
	return true
}

/**
初始化分块上传
*/
func (m *MspCosCall) StartMultiUpload(filePath string, taskId string) string {
	// 统计TP性能
	logger := pl_boot.AppCtx.GetAppLogger().With(zap.String("jobId", taskId))

	startTime := time.Now()
	result, _, err := defaultCosClient.GetClient().Object.InitiateMultipartUpload(context.Background(), filePath, nil)

	pl_prom.GetHistogramWithValues(metrics.RpcHistogram, metrics.Lister, "StartMultiUpload", taskId, "").
		Observe(float64(time.Now().Sub(startTime).Microseconds()))
	if nil != err {
		logger.Error("StartMultiUpload fail ", zap.String("filePath", filePath), zap.Error(err))
		pl_prom.GetCounterWithValues(metrics.ErrorCount, metrics.Lister, "UploadPart", taskId, "")
		return ""
	}
	if result.UploadID == "" {
		logger.Error("StartMultiUpload UploadID is nil ", zap.String("filePath", filePath), zap.Error(err))
		return ""
	}
	logger.Info(
		"StartMultiUpload start upload",
		zap.String("filePath", filePath),
		zap.String("uploadID", result.UploadID))
	return result.UploadID
}

// 调用 cos 客户端向指定 path 上传一段 []byte
func doUpload(
	cosClient *cos.Client,
	taskUploadInfo *taskupload.TaskUploadInfo,
	resultPath string,
	taskId string,
	logger *zap.Logger,
) (*cos.Response, error) {

	body := []byte(taskUploadInfo.AccumulatePart.Load())
	opt := &cos.ObjectUploadPartOptions{
		ContentLength: int64(len(body)),
	}

	var response *cos.Response
	var err error
	retry := 10
	for count := 0; count < retry; count++ {
		// 统计TP性能
		startTime := time.Now()
		response, err = cosClient.Object.UploadPart(
			context.Background(),
			resultPath,
			taskUploadInfo.UploadId,
			int(taskUploadInfo.PartNum.Load()),
			strings.NewReader(taskUploadInfo.AccumulatePart.Load()),
			opt)
		pl_prom.GetHistogramWithValues(metrics.RpcHistogram, metrics.Lister, "UploadPart", taskId, "").
			Observe(float64(time.Now().Sub(startTime).Microseconds()))
		if err != nil { // 上传失败后记录日志并重试
			logger.Error(
				"UploadPart cos err",
				zap.Int("body len", len(body)),
				zap.Int("count", count),
				zap.Error(err))
			random := rand.Intn(30)
			// 重试等待 0～10 毫秒 避免并发
			time.Sleep(time.Duration(random) * time.Millisecond)
			if count == retry-1 {
				// 失败上报
				pl_prom.GetCounterWithValues(metrics.ErrorCount, metrics.Lister, "UploadPart", taskId, strconv.Itoa(count))
			}
		} else {
			// 成功直接返回
			logger.Info(
				"UploadPart cos suc break retry",
				zap.Int("count", count))
			break
		}
	}

	// 计算内容行数
	rows := len(strings.Split(taskUploadInfo.AccumulatePart.Load(), "\n"))
	logger.Info(
		"UploadPart cos finished",
		zap.Int32("PartNum", taskUploadInfo.PartNum.Load()),
		zap.Int("AccumulatePart.Rows", rows),
	)

	return response, err
}

// 上传失败文件内容
func (m *MspCosCall) UploadPart(
	taskUploadInfo *taskupload.TaskUploadInfo, resultPath string, part string, force bool, taskId string,
) bool {
	logger := pl_boot.AppCtx.GetAppLogger().With(
		zap.String("jobId", taskId),
		zap.Bool("force", force),
		zap.String("resultPath", resultPath))
	logger.Info("UploadPart parts params", zap.Int("part len", len(part)))

	var parts strings.Builder
	parts.WriteString(taskUploadInfo.AccumulatePart.Load())
	parts.WriteString(part)
	taskUploadInfo.AccumulatePart.Store(parts.String())
	var MinFileLen = conf.ListerConfig.GlobalConf.FailFilePartSize
	if !force {
		if len(taskUploadInfo.AccumulatePart.Load()) <= MinFileLen {
			logger.Info(
				"UploadPart AccumulatePart < MinFileLen",
				zap.Int("AccumulatePart", len(taskUploadInfo.AccumulatePart.Load())))
			return true
		}
		if part == "" {
			logger.Info("UploadPart part empty")
			return true
		}
	}

	if len(taskUploadInfo.AccumulatePart.Load()) == 0 {
		logger.Info("UploadPart AccumulatePart len is 0")
		return true
	}

	// // 本次上传之前，要检查是否需要补加，避免因为服务宕机引起丢失块，
	// m.ComplementPart(taskUploadInfo, resultPath, taskUploadInfo.UploadId, taskId)
	response, err := doUpload(defaultCosClient.GetClient(), taskUploadInfo, resultPath, taskId, logger)
	if err != nil {
		logger.Error("UploadPart failed", zap.Error(err))
		return false
	}

	if m.VerifyUpload(response, taskUploadInfo.AccumulatePart.Load()) {
		m := make(map[string]string)
		m["PartNumber"] = tools.Int32Strcon(taskUploadInfo.PartNum.Load())
		m["ETag"] = tools.Md5ToHex([]byte(taskUploadInfo.AccumulatePart.Load()))
		taskUploadInfo.PartsUpload["Part"] = append(taskUploadInfo.PartsUpload["Part"], m)
		taskUploadInfo.PartNum.Add(1)
		// 上传成功清空当前累积的分片
		taskUploadInfo.AccumulatePart.Store("")
		logger.Info(
			"UploadPart verify part successful and set upload value",
			zap.Int("AccumulatePart len", len(taskUploadInfo.AccumulatePart.Load())),
			zap.Any("PartNum", taskUploadInfo.PartNum.Load()),
			zap.Any("ETag", m["ETag"]))
		return true
	}
	logger.Error(
		"UploadPart verify part failed",
		zap.Int("AccumulatePart", len(taskUploadInfo.AccumulatePart.Load())))
	return false
}

// verify md5
func (m *MspCosCall) VerifyUpload(resp *cos.Response, part string) bool {
	logger := pl_boot.AppCtx.GetAppLogger()
	etag := resp.Header.Get("ETag")
	if etag == "" {
		logger.Warn("VerifyUpload skip part without ETag", zap.Any("resp", resp))
		return true
	}
	serverMd5 := strings.ToUpper(resp.Header.Get("ETag"))
	serverMd5 = strings.Trim(serverMd5, `"`)
	partMd5 := tools.Md5ToHexUpper([]byte(part))
	logger.Info("VerifyUpload",
		zap.String("serverMd5", serverMd5),
		zap.String("partMd5", partMd5),
	)
	return serverMd5 == partMd5
}

//  完成分块上传
func (m *MspCosCall) EndMultiUpload(taskUploadInfo *taskupload.TaskUploadInfo, resultPath string, taskId string) bool {
	logger := pl_boot.AppCtx.GetAppLogger().With(zap.String("jobId", taskId))

	if taskUploadInfo.Over.Load() {
		logger.Info("multipart upload already completed", zap.String("resultPath", resultPath))
		return true
	}

	duplicate := m.DeepCopy(taskUploadInfo)
	if !m.UploadPart(duplicate, resultPath, "", true, taskId) {
		return false
	}

	taskUploadInfo.Update(duplicate)
	time.Sleep(time.Second * 2) // 等待2s所有分片上传完毕
	opt := &cos.CompleteMultipartUploadOptions{}
	opt.Parts = make([]cos.Object, 0)
	logger.Info(
		"EndMultiUpload parts upload",
		zap.Any("part", taskUploadInfo.PartsUpload["Part"]),
		zap.String("resultPath", resultPath))
	for _, v := range taskUploadInfo.PartsUpload["Part"] {
		opt.Parts = append(opt.Parts, cos.Object{
			ETag:       v["ETag"],
			PartNumber: tools.StrConInt(v["PartNumber"]),
		})
	}
	// 统计TP性能
	startTime := time.Now()
	_, _, err := defaultCosClient.GetClient().Object.CompleteMultipartUpload(
		context.Background(), resultPath, taskUploadInfo.UploadId, opt)
	pl_prom.GetHistogramWithValues(metrics.RpcHistogram, metrics.Lister, "EndMultiUpload", taskId, "").
		Observe(float64(time.Now().Sub(startTime).Microseconds()))
	if nil != err {
		logger.Error(
			"CompleteMultipartUpload err",
			zap.String("resultPath", resultPath),
			zap.Any("UploadId", taskUploadInfo.UploadId),
			zap.Any("parts", opt.Parts),
			zap.Error(err))
		// 失败上报
		pl_prom.GetCounterWithValues(metrics.ErrorCount, metrics.Lister, "CompleteMultipartUpload", taskId, "")
		return false
	}
	taskUploadInfo.SetOver(true)
	logger.Info(
		"EndMultiUpload Success",
		zap.Any("partsUpload.Part", taskUploadInfo.PartsUpload["Part"]),
		zap.String("resultPath", resultPath))
	return true
}

// DeepCopy 深拷贝 task upload 详情
func (m *MspCosCall) DeepCopy(raw *taskupload.TaskUploadInfo) *taskupload.TaskUploadInfo {
	dup := &taskupload.TaskUploadInfo{
		UploadId:        raw.UploadId,
		PartNum:         atomic.NewInt32(1),
		PartsUpload:     make(map[string][]map[string]string),
		AccumulatePart:  &atomic.String{},
		Over:            atomic.NewBool(false),
		ChildUploadOver: atomic.NewBool(false),
	}
	dup.PartNum = atomic.NewInt32(raw.PartNum.Load())
	dup.AccumulatePart = atomic.NewString(raw.AccumulatePart.Load())
	dup.Over = atomic.NewBool(raw.Over.Load())
	dup.ChildUploadOver = atomic.NewBool(raw.ChildUploadOver.Load())
	l := make([]map[string]string, 0)

	// logger := pl_boot.AppCtx.GetAppLogger()

	// logger.Info("TaskUploadInfo.DeepCopy.raw", zap.Any("taskupload", raw.PartsUpload["Part"]))
	for _, e := range raw.PartsUpload["Part"] {
		// list element : map[string]string
		m := make(map[string]string)
		// logger.Info("TaskUploadInfo.DeepCopy.foreach", zap.Any("map", e))
		for k, v := range e { // copy element to m
			m[k] = v
			// logger.Info(
			// 	"TaskUploadInfo.DeepCopy.foreach.set ",
			// 	zap.String("k", k),
			// 	zap.String("v", v),
			// 	zap.String("m", m[k]))
		}
		l = append(l, m)
	}
	dup.PartsUpload["Part"] = l
	// logger.Info(
	// 	"TaskUploadInfo.DeepCopy",
	// 	zap.Any("dup.PartsUpload", dup.PartsUpload["Part"]),
	// 	zap.Int32("dup.PartNum", dup.PartNum.Load()),
	// 	zap.Bool("ChildUploadOver", dup.ChildUploadOver.Load()),
	// 	zap.Bool("Over", dup.Over.Load()))
	return dup
}

// 定时任务调度
func (m *MspCosCall) EndReportFailedFiles(taskId string) {
	logger := pl_boot.AppCtx.GetAppLogger().With(zap.String("jobId", taskId))

	service := taskupload.GetUploadService()
	taskUploadInfo := service.GetTaskUploadInfoById(taskId)
	failedFilePath := service.GetTaskToFailedPathById(taskId)
	if taskUploadInfo == nil || failedFilePath == nil { // 判断状态
		logger.Info(
			"EndReportFailedFiles skip",
			zap.Bool("taskUploadInfo is nil", taskUploadInfo == nil),
			zap.Bool("failedFilePath is nil", failedFilePath == nil))
		return
	}
	status := taskUploadInfo.Over.Load()
	if status {
		logger.Info("EndReportFailedFiles skip", zap.Bool("status is over", status))
		return
	}
	logger.Info("EndReportFailedFiles.params", zap.String("failedFilePath", *failedFilePath))
	// 尝试完成上传
	res := m.EndMultiUpload(taskUploadInfo, *failedFilePath, taskId)
	apiService := apicall.GetApiCall()
	// 上报状态
	apiService.ReportTaskResultUploadState(taskId, apicall.UploadSuccess)
	if res {
		// 删除内存 taskUpload ，filepath，lock信息
		service.RemoveUploadCache(taskId)
	}
	logger.Info("EndReportFailedFiles suc", zap.Bool("res", res))
	return

}
