package service

import (
	"bytes"
	"encoding/json"
	"fmt"
	"git.code.oa.com/pulse-line/pl_boot"
	"go.uber.org/zap"
	"io/ioutil"
	"net/http"
)

// TaskDiversionRepo 任务分流配置 repo 层接口
type TaskDiversionRepo interface {
	Get(diversionId string) (*TaskDiversion, error)
	GetTaskDiversionsNoIndependent() ([]*TaskDiversion, error)
}

// 从 mspadmin 拉取分流账号ID (对应 mspServer 配置的 "except" 代码块里的 key)
type MSPAdminTaskDiversionRepo struct {
	URL string
}

// Request message of TaskDiversionAPI.GetTaskDiversionRequest.
type GetTaskDiversionRequest struct {
	Action      string `json:"Action,omitempty"`
	DiversionId string `json:"DiversionId,omitempty"`
}

// Request message of TaskDiversionAPI.GetTaskDiversionRequest.
type GetTaskDiversionResponse struct {
	Response TaskDiversion `json:"Response,omitempty"`
}

// TaskDiversion 任务分流记录
type TaskDiversion struct {
	Id          uint32 `json:"Id,omitempty"`
	AppId       uint64 `json:"AppId,string,omitempty"`
	DiversionId string `json:"DiversionId,omitempty"`
}

//获取分流记录请求
type GetTaskDiversionsReq struct {
	Action string `json:"Action,omitempty"`
}

//返回所有分流记录
type GetTaskDiversionaResp struct {
	Response struct {
		TaskDiversions []*TaskDiversion `json:"TaskDiversions,omitempty"`
	} `json:"Response"`
}

// 使用 diversionId 查询任务分流配置
// curl -XPOST -H "Content-Type: application/json" -d '{"Action": "GetTaskDiversion", "DiversionId": "id"}'
// http://admin.msp.cloud.tencentyun.com/msp_admin_api
func (repo *MSPAdminTaskDiversionRepo) Get(diversionId string) (*TaskDiversion, error) {
	url := repo.URL
	request := &GetTaskDiversionRequest{
		Action:      "GetTaskDiversion",
		DiversionId: diversionId,
	}
	payload := new(bytes.Buffer)
	if err := json.NewEncoder(payload).Encode(request); err != nil {
		return nil, err
	}

	resp, err := http.Post(url, "application/json", payload)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	respContent := &GetTaskDiversionResponse{}
	err = json.NewDecoder(resp.Body).Decode(&respContent)
	if err != nil {
		return nil, err
	}

	d := &respContent.Response
	if d.DiversionId != diversionId {
		return nil, fmt.Errorf("diversionId mismtach, want=%s, got=%s", diversionId, d.DiversionId)
	}

	return d, nil
}

//curl -XPOST -H "Content-Type: application/json" -d '{"Action": "GetTaskDiversions"}'
//http://9.71.226.106:19100/msp_admin_api
func (repo *MSPAdminTaskDiversionRepo) GetTaskDiversionsNoIndependent() ([]*TaskDiversion, error) {
	logger := pl_boot.AppCtx.GetAppLogger()
	urlStr := repo.URL

	request := &GetTaskDiversionsReq{
		Action: "GetTaskDiversionsNoIndependent",
	}
	logger.Info("request GetTaskDiversions")
	payload := new(bytes.Buffer)
	if err := json.NewEncoder(payload).Encode(request); err != nil {
		return nil, err
	}

	resp, err := http.Post(urlStr, "application/json", payload)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}
	logger.Info("GetTaskDiversions body ", zap.String("bodyStr", string(body)))

	respContent := &GetTaskDiversionaResp{}
	err = json.Unmarshal(body, respContent)
	if err != nil {
		return nil, err
	}

	return respContent.Response.TaskDiversions, nil
}
