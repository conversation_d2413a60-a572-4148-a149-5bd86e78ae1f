package models

import (
	"encoding/json"
	"fmt"
	"os"
	"sync"
	"time"

	"github.com/dlclark/regexp2"
	"github.com/syndtr/goleveldb/leveldb"

	"git.code.oa.com/cloud-msp/migration-lister/api"
	"git.code.oa.com/cloud-msp/migration-lister/internal/service/consts"
	"git.code.oa.com/cloud-msp/migration-lister/pkg/utils"
	"git.code.oa.com/cloud-msp/migration-lister/tools"
	"git.code.oa.com/pulse-line/pl_boot"

	"go.uber.org/atomic"
	"go.uber.org/zap"
)

// 备份存储目录, 增量任务的增量消息存储目录
var (
	BackupDir  = ".agent_state/"
	IncrMsgDir = ".leveldb/"
)

// TaskService 接口管理 task 执行结果
type TaskService interface {
	// 添加分片结果
	AddFragResult(fragResult *FragResult)
}

// Task task 基础信息以及 task 执行结果
type Task struct {
	Id                   string
	Name                 string
	ListerIp             string
	SpeedLimit           int
	AgentSpeedLimit      int
	QpsLimit             int64
	SpeedLimitInfo       string
	Priority             int // 优先级
	ChanLevel            int
	IncrSrcInfo          string // 增量源, 非空代表增量任务
	JobInfo              *tools.JobInfo
	ExistFileSuccessNum  *atomic.Int64 // 已有的迁移量
	ExistFileSuccessSize *atomic.Int64
	Retry                bool
	// 以上是基础信息
	// 以下是动态信息
	QuickListFileNum *atomic.Int64
	QuickListSize    *atomic.Int64
	QuickListOver    *atomic.Bool
	QuickListSkipped *atomic.Int64
	SlowListFileNum  *atomic.Int64
	SlowListSize     *atomic.Int64
	SlowListFragNum  *atomic.Int64
	SlowListOver     *atomic.Bool
	State            *atomic.Int64 // migrateproject.TaskState
	QuickCurMarkers  *sync.Map
	SlowCurMarkers   *sync.Map
	FragId           *atomic.Int64
	// 迁移结果字段
	FragIdSet         *sync.Map     // map[int64]int
	SuccessfulFragNum *atomic.Int64 // 等待任务遍历完成后更新分片数量
	SuccessfulFileNum *atomic.Int64
	FailedFileNum     *atomic.Int64
	SuccessfulSize    *atomic.Int64
	FailedSize        *atomic.Int64

	// external
	WorkUrlTaskResultMap *sync.Map // map[string]*WorkTaskResult
	AgentIpTaskResultMap *sync.Map // map[string]*AgentTaskResult

	Mutex *sync.Mutex

	// workerMetric
	WorkerMetric *sync.Map // map[string]*Metric // key是workerAddr

	WorkSpeedInfo *sync.Map // map[string]*WorkerSpeedInfo // key是workerAddr

	BackupDB *leveldb.DB

	IncrMsgDB *leveldb.DB
}

type WorkerSpeedInfo struct {
	SpeedInUse int64
	SpeedLimit int64
}

// metric
type Metric struct {
	BandWidth int64
	Timestamp time.Time
}

// BaseInfo task 基本信息
type BaseInfo struct {
	TaskId            string
	FragNum           *atomic.Int64
	SuccessfulFileNum *atomic.Int64
	FailedFileNum     *atomic.Int64
	SuccessfulSize    *atomic.Int64
	FailedSize        *atomic.Int64
}

// WorkTaskResult worker task
type WorkTaskResult struct {
	BaseInfo   *BaseInfo
	WorkerIp   string
	WorkerPort int64
}

// AgentTaskResult agent task
type AgentTaskResult struct {
	AgentIp  string
	BaseInfo *BaseInfo
}

func (t *Task) SetId(id string) {
	t.Id = id
	utils.DBPut(t.BackupDB, "Task.Id", t.Id)
}

func (t *Task) SetName(name string) {
	t.Name = name
	utils.DBPut(t.BackupDB, "Task.Name", t.Name)
}

func (t *Task) SetListerIp(ip string) {
	t.ListerIp = ip
	utils.DBPut(t.BackupDB, "Task.ListerIp", t.ListerIp)
}

func (t *Task) SetSpeedLimit(limit int) {
	t.SpeedLimit = limit
	utils.DBPut(t.BackupDB, "Task.SpeedLimit", fmt.Sprint(t.SpeedLimit))
}

func (t *Task) SetAgentSpeedLimit(limit int) {
	t.AgentSpeedLimit = limit
	utils.DBPut(t.BackupDB, "Task.AgentSpeedLimit", fmt.Sprint(t.AgentSpeedLimit))
}

func (t *Task) SetQpsLimit(limit int64) {
	t.QpsLimit = limit
	utils.DBPut(t.BackupDB, "Task.QpsLimit", fmt.Sprint(t.QpsLimit))
}

func (t *Task) SetSpeedLimitInfo(info string) {
	t.SpeedLimitInfo = info
	utils.DBPut(t.BackupDB, "Task.SpeedLimitInfo", t.SpeedLimitInfo)
}

func (t *Task) SetPriority(prio int) {
	t.Priority = prio
	utils.DBPut(t.BackupDB, "Task.Priority", fmt.Sprint(t.Priority))
}

func (t *Task) SetChanLevel(level int) {
	t.ChanLevel = level
	utils.DBPut(t.BackupDB, "Task.ChanLevel", fmt.Sprint(t.ChanLevel))
}

func (t *Task) SetIncrSrcInfo(info string) {
	t.IncrSrcInfo = info
	utils.DBPut(t.BackupDB, "Task.IncrSrcInfo", t.IncrSrcInfo)
}

func (t *Task) SetJobInfo(job *tools.JobInfo) {
	t.JobInfo = job
	data := t.Marshal_JobInfo()
	utils.DBPut(t.BackupDB, "Task.JobInfo", data)
}

func (t *Task) SetQuickListOver(over bool) {
	t.QuickListOver.Store(over)
	utils.DBPut(t.BackupDB, "Task.QuickListOver", t.QuickListOver.String())
}

func (t *Task) SetSlowListOver(over bool) {
	t.SlowListOver.Store(over)
	utils.DBPut(t.BackupDB, "Task.SlowListOver", t.SlowListOver.String())
}

// 添加 worker 上报的 fragResult 累加到内存中的 task
// NOTE: fragId 加入到已完成 set, 用于 fragResult 去重
// 	changed == false 代表去重过滤掉了
func (t *Task) AddFragResult(fragResult *FragResult) (changed bool) {
	// 已经在外层加锁了
	// t.Mutex.Lock()
	// defer t.Mutex.Unlock()

	taskId := t.Id
	fragId := fragResult.BaseInfo.FragId
	logger := pl_boot.AppCtx.GetAppLogger().With(zap.String("jobId", taskId), zap.Int64("fragId", fragId))

	if t.Id != fragResult.BaseInfo.TaskId {
		logger.Warn(
			"frag.TaskId and task.Id mismatch, ignore it",
			zap.String("frag.TaskId", fragResult.BaseInfo.TaskId))
		return false
	}

	result := fragResult.BaseInfo
	// 这个分片的 id 已经有记录, 去重
	_, ok := t.FragIdSet.Load(fragId)
	if ok {
		logger.Warn("duplicated fragId in result set, ignore it")
		return false
	}

	// 累加内存中 task 的计数
	t.SuccessfulFragNum.Add(1)
	t.FragIdSet.Store(fragId, 1)
	t.FailedFileNum.Add(result.FailedNum)
	t.FailedSize.Add(result.FailedSize)
	t.SuccessfulFileNum.Add(result.SuccessfulNum)
	t.SuccessfulSize.Add(result.SuccessfulSize)

	// 统计量的修改实时同步到db
	utils.DBPut(t.BackupDB, "Task.SuccessfulFragNum", t.SuccessfulFragNum.String())
	utils.DBPut(t.BackupDB, fmt.Sprintf("Task.FragIdSet.%d", fragId), "1")
	utils.DBPut(t.BackupDB, "Task.FailedFileNum", t.FailedFileNum.String())
	utils.DBPut(t.BackupDB, "Task.FailedSize", t.FailedSize.String())
	utils.DBPut(t.BackupDB, "Task.SuccessfulFileNum", t.SuccessfulFileNum.String())
	utils.DBPut(t.BackupDB, "Task.SuccessfulSize", t.SuccessfulSize.String())

	// work task result handle
	if value, ok := t.WorkUrlTaskResultMap.Load(result.WorkerUrl); !ok {
		workTaskResult := &WorkTaskResult{
			BaseInfo: &BaseInfo{
				TaskId:            result.TaskId,
				FragNum:           atomic.NewInt64(0),
				SuccessfulFileNum: atomic.NewInt64(0),
				FailedFileNum:     atomic.NewInt64(0),
				SuccessfulSize:    atomic.NewInt64(0),
				FailedSize:        atomic.NewInt64(0),
			},
			WorkerIp:   result.WorkerIp,
			WorkerPort: result.WorkerPort,
		}
		workTaskResult.AddFragResult(fragResult)
		t.WorkUrlTaskResultMap.Store(result.WorkerUrl, workTaskResult)
	} else {
		// 如果存在直接累加
		workTaskResult := value.(*WorkTaskResult)
		workTaskResult.AddFragResult(fragResult)
	}
	utils.DBPut(t.BackupDB, "Task.WorkUrlTaskResultMap", t.Marshal_WorkUrlTaskResultMap())

	// agent task result handle
	if value, ok := t.AgentIpTaskResultMap.Load(result.WorkerIp); !ok {
		agentTaskResult := &AgentTaskResult{
			AgentIp: result.WorkerIp,
			BaseInfo: &BaseInfo{
				TaskId:            result.TaskId,
				FragNum:           atomic.NewInt64(0),
				SuccessfulFileNum: atomic.NewInt64(0),
				FailedFileNum:     atomic.NewInt64(0),
				SuccessfulSize:    atomic.NewInt64(0),
				FailedSize:        atomic.NewInt64(0),
			},
		}
		agentTaskResult.AddFragResult(fragResult)
		t.AgentIpTaskResultMap.Store(result.WorkerIp, agentTaskResult)
	} else {
		agentTaskResult := value.(*AgentTaskResult)
		agentTaskResult.AddFragResult(fragResult)
	}
	utils.DBPut(t.BackupDB, "Task.AgentIpTaskResultMap", t.Marshal_AgentIpTaskResultMap())

	return true
}

// IsEnd 任务是否完成
func (t *Task) IsEnd() bool {
	logger := pl_boot.AppCtx.GetAppLogger().With(zap.String("jobId", t.Id))
	logger.Info(
		"task is end or not",
		zap.Bool("QuickListOver", t.QuickListOver.Load()),
		zap.Bool("SlowListOver", t.SlowListOver.Load()),
		zap.Int64("SuccessfulFragNum", t.SuccessfulFragNum.Load()),
		zap.Int64("SlowListFragNum", t.SlowListFragNum.Load()))
	return t.QuickListOver.Load() && t.SlowListOver.Load() && (t.SuccessfulFragNum.Load() == t.SlowListFragNum.Load())
}

// GetPrefixes 返回 task 配置的 prefix 列表
func (t *Task) GetPrefixes() ([]string, error) {
	if t.JobInfo == nil || t.JobInfo.MigrationRule == nil {
		// empty job info or empty MigrationRule
		return nil, nil
	}

	rule := *t.JobInfo.MigrationRule
	if len(rule) == 0 {
		// no rules specified
		return nil, nil
	}

	prefixes := make([]string, 0)
	if err := json.Unmarshal([]byte(rule), &prefixes); err != nil {
		return nil, err
	}

	pl_boot.AppCtx.GetAppLogger().Info("get prefixes", zap.Any("prefix", prefixes))
	return prefixes, nil
}

// GetRegexps 返回 task 配置的 regex 过滤列表 (migration rule type == regex)
func (t *Task) GetRegexps() ([]*regexp2.Regexp, error) {
	prefixes, err := t.GetPrefixes()
	if err != nil {
		return nil, err
	}
	// compile regex
	exps := make([]*regexp2.Regexp, 0)
	for _, p := range prefixes {
		pl_boot.AppCtx.GetAppLogger().Info("GetRegexps", zap.Any("prefix", p))
		reg, err := regexp2.Compile(p, regexp2.None)
		if err != nil {
			return nil, err
		}
		exps = append(exps, reg)
	}
	return exps, nil
}

// HasRegexFilter task 是否设置了按照 regex 过滤文件
func (t *Task) HasRegexFilter() bool {
	if t.JobInfo == nil || t.JobInfo.MigrationRuleType == nil || *t.JobInfo.
		MigrationRuleType != consts.MigrationRuleTypeRegex {
		return false
	}
	return true
}

// HasTimeSpanFilter task 是否设置了按照时间区间过滤文件
func (t *Task) HasTimeSpanFilter() bool {
	if t.JobInfo == nil || t.JobInfo.HasFileTimeFilter == nil || *t.JobInfo.HasFileTimeFilter <= 0 {
		return false
	}
	return true
}

// GetTimeSpan 获取 task 设置的时间过滤区间
func (t *Task) GetTimeSpan() (startT, endT time.Time) {
	startT = time.Unix(int64(*t.JobInfo.FileStartTime), 0)
	endT = time.Unix(int64(*t.JobInfo.FileEndTime), 0)
	return
}

// LoadSlowListMarker 用 prefix 作为 key, 获取对应的 marker.
// Marker is per prefix and could be later used to indicate where in the bucket listing begins.
func (t *Task) LoadQuickListMarker(prefix string) (marker string, ok bool) {
	mk, ok := t.QuickCurMarkers.Load(prefix)
	if !ok {
		return
	}

	marker, ok = mk.(string)
	return
}

// StoreSlowListMarker 存储 quick list 进度中的 prefix: marker 键值对.
// Marker is per prefix and could be later used to indicate where in the bucket listing begins.
func (t *Task) StoreQuickListMarker(prefix string, marker string) {
	t.QuickCurMarkers.Store(prefix, marker)

	value := t.Marshal_QuickCurMarkers()
	utils.DBPut(t.BackupDB, "Task.QuickCurMarkers", value)
}

// StoreSlowListMarker 存储 slow list 进度中的 prefix: marker 键值对.
// Marker is per prefix and could be later used to indicate where in the bucket listing begins.
func (t *Task) StoreSlowListMarker(prefix string, marker string) {
	t.SlowCurMarkers.Store(prefix, marker)

	value := t.Marshal_SlowCurMarkers()
	utils.DBPut(t.BackupDB, "Task.SlowCurMarkers", value)
}

// LoadSlowListMarker 用 prefix 作为 key, 获取对应的 marker.
// Marker is per prefix and could be later used to indicate where in the bucket listing begins.
func (t *Task) LoadSlowListMarker(prefix string) (marker string, ok bool) {
	mk, ok := t.SlowCurMarkers.Load(prefix)
	if !ok {
		return
	}

	marker, ok = mk.(string)
	return
}

// AddQuickListingStat accumulates size and count of listed objects in quick listing progress.
func (t *Task) AddQuickListingStat(size, cnt int64) {
	t.QuickListSize.Add(size)
	t.QuickListFileNum.Add(cnt)

	utils.DBPut(t.BackupDB, "Task.QuickListSize", t.QuickListSize.String())
	utils.DBPut(t.BackupDB, "Task.QuickListFileNum", t.QuickListFileNum.String())
}

// GetQuickListingStat 获取 quick listing cnt, size
func (t *Task) GetQuickListingStat() (size, cnt int64) {
	size = t.QuickListSize.Load()
	cnt = t.QuickListFileNum.Load()
	return
}

// AddSlowListingStat accumulates size and count of objects in slow listing progress.
func (t *Task) AddSlowListingStat(size, cnt int64) {
	t.SlowListSize.Add(size)
	t.SlowListFileNum.Add(cnt)

	utils.DBPut(t.BackupDB, "Task.SlowListSize", t.SlowListSize.String())
	utils.DBPut(t.BackupDB, "Task.SlowListFileNum", t.SlowListFileNum.String())
}

// GetSlowListingStat 获取慢速遍历的文件 cnt, size
func (t *Task) GetSlowListingStat() (size, cnt int64) {
	size = t.SlowListSize.Load()
	cnt = t.SlowListFileNum.Load()
	return
}

// GetExistingStat 获取重试任务已经存在的文件 cnt, size
func (t *Task) GetExistingStat() (size, cnt int64) {
	size = t.ExistFileSuccessSize.Load()
	cnt = t.ExistFileSuccessNum.Load()
	return
}

// AddSkipped 累加跳过的文件数量
func (t *Task) AddSkipped(cnt int64) {
	t.QuickListSkipped.Add(cnt)
	utils.DBPut(t.BackupDB, "Task.QuickListSkipped", t.QuickListSkipped.String())
}

//
func (t *Task) GetSkipped() (cnt int64) {
	cnt = t.QuickListSkipped.Load()
	return
}

// NextFragmentId returns the next fragment ID.
func (t *Task) NextFragmentId() int64 {
	nextId := t.FragId.Add(1)
	utils.DBPut(t.BackupDB, "Task.FragId", fmt.Sprint(nextId))
	return nextId
}

// IncrFragmentCnt increases count fo successfully distributed fragments.
func (t *Task) IncrFragmentCnt() {
	t.SlowListFragNum.Add(1)
	utils.DBPut(t.BackupDB, "Task.SlowListFragNum", t.SlowListFragNum.String())
}

// GetFragmentCnt 获取分片计数
func (t *Task) GetFragmentCnt() int64 {
	return t.SlowListFileNum.Load()
}

// SetState 设置当前任务的 state
func (t *Task) SetState(state api.TaskState) {
	t.State.Store(int64(state))
	utils.DBPut(t.BackupDB, "Task.State", t.State.String())
}

// GetState 获取当前任务的 state
func (t *Task) GetState() api.TaskState {
	state := t.State.Load()
	return api.TaskState(state)
}

// SetRetry
func (t *Task) SetRetry(retry bool) {
	t.Retry = retry
	utils.DBPut(t.BackupDB, "Task.Retry", fmt.Sprint(t.Retry))
}

// SetExistFileSuccessNum
func (t *Task) SetExistFileSuccessNum(num int64) {
	t.ExistFileSuccessNum.Store(num)
	utils.DBPut(t.BackupDB, "Task.ExistFileSuccessNum", t.ExistFileSuccessNum.String())
}

// SetExistFileSuccessSize
func (t *Task) SetExistFileSuccessSize(size int64) {
	t.ExistFileSuccessSize.Store(size)
	utils.DBPut(t.BackupDB, "Task.ExistFileSuccessSize", t.ExistFileSuccessSize.String())
}

// Marshal_WorkUrlTaskResultMap
func (t *Task) Marshal_WorkUrlTaskResultMap() string {
	m := make(map[string]WorkTaskResult)
	t.WorkUrlTaskResultMap.Range(func(k, v interface{}) bool {
		key := k.(string)
		m[key] = *(v.(*WorkTaskResult))
		return true
	})
	b, err := json.Marshal(m)
	if err != nil {
		panic(err)
	}
	return string(b)
}

// Marshal_AgentIpTaskResultMap
func (t *Task) Marshal_AgentIpTaskResultMap() string {
	m := make(map[string]AgentTaskResult)
	t.AgentIpTaskResultMap.Range(func(k, v interface{}) bool {
		key := k.(string)
		m[key] = *(v.(*AgentTaskResult))
		return true
	})
	b, err := json.Marshal(m)
	if err != nil {
		panic(err)
	}
	return string(b)
}

// Marshal_QuickCurMarkers
func (t *Task) Marshal_QuickCurMarkers() string {
	m := make(map[string]string)
	t.QuickCurMarkers.Range(func(k, v interface{}) bool {
		key := k.(string)
		m[key] = v.(string)
		return true
	})
	b, err := json.Marshal(m)
	if err != nil {
		panic(err)
	}
	return string(b)
}

// Marshal_SlowCurMarkers
func (t *Task) Marshal_SlowCurMarkers() string {
	m := make(map[string]string)
	t.SlowCurMarkers.Range(func(k, v interface{}) bool {
		key := k.(string)
		m[key] = v.(string)
		return true
	})
	b, err := json.Marshal(m)
	if err != nil {
		panic(err)
	}
	return string(b)
}

// Marshal_JobInfo
func (t *Task) Marshal_JobInfo() string {
	b, err := json.Marshal(t.JobInfo)
	if err != nil {
		panic(err)
	}
	return string(b)
}

// AddFragResult worker task 添加分片执行结果
func (w *WorkTaskResult) AddFragResult(fragResult *FragResult) {
	result := fragResult.BaseInfo
	w.BaseInfo.FragNum.Add(1)
	w.BaseInfo.SuccessfulFileNum.Add(result.SuccessfulNum)
	w.BaseInfo.FailedFileNum.Add(result.FailedNum)
	w.BaseInfo.SuccessfulSize.Add(result.SuccessfulSize)
	w.BaseInfo.FailedSize.Add(result.FailedSize)
}

// AddFragResult agent task 添加分片执行结果
func (a *AgentTaskResult) AddFragResult(fragResult *FragResult) {
	result := fragResult.BaseInfo
	a.BaseInfo.FragNum.Add(1)
	a.BaseInfo.SuccessfulFileNum.Add(result.SuccessfulNum)
	a.BaseInfo.FailedFileNum.Add(result.FailedNum)
	a.BaseInfo.SuccessfulSize.Add(result.SuccessfulSize)
	a.BaseInfo.FailedSize.Add(result.FailedSize)
}

// NewTask 构造 *Task 实例
func NewTask() *Task {
	return &Task{
		Id:                   "",
		Name:                 "",
		ListerIp:             "",
		SpeedLimit:           0,
		AgentSpeedLimit:      0,
		QpsLimit:             0,
		Priority:             0,
		ChanLevel:            0,
		JobInfo:              &tools.JobInfo{},
		ExistFileSuccessNum:  &atomic.Int64{},
		ExistFileSuccessSize: &atomic.Int64{},
		QuickListFileNum:     &atomic.Int64{},
		QuickListSize:        &atomic.Int64{},
		QuickListOver:        &atomic.Bool{},
		QuickListSkipped:     &atomic.Int64{},
		SlowListFileNum:      &atomic.Int64{},
		SlowListSize:         &atomic.Int64{},
		SlowListFragNum:      &atomic.Int64{},
		SlowListOver:         &atomic.Bool{},
		State:                &atomic.Int64{},
		QuickCurMarkers:      &sync.Map{},
		SlowCurMarkers:       &sync.Map{},
		FragId:               atomic.NewInt64(0),
		FragIdSet:            &sync.Map{},
		SuccessfulFragNum:    &atomic.Int64{},
		SuccessfulFileNum:    &atomic.Int64{},
		FailedFileNum:        &atomic.Int64{},
		SuccessfulSize:       &atomic.Int64{},
		FailedSize:           &atomic.Int64{},
		WorkUrlTaskResultMap: &sync.Map{},
		AgentIpTaskResultMap: &sync.Map{},
		Mutex:                &sync.Mutex{},
		WorkerMetric:         &sync.Map{},
		WorkSpeedInfo:        &sync.Map{},
	}
}

// 获取 task 当前的所有计数
func (t *Task) StatSnapshot() map[string]int64 {
	return map[string]int64{
		"ExistFileSuccessNum":  t.ExistFileSuccessNum.Load(),
		"ExistFileSuccessSize": t.ExistFileSuccessSize.Load(),
		"QuickListFileNum":     t.QuickListFileNum.Load(),
		"QuickListSize":        t.QuickListSize.Load(),
		"SlowListFileNum":      t.SlowListFileNum.Load(),
		"SlowListSize":         t.SlowListSize.Load(),
		"SuccessfulFileNum":    t.SuccessfulFileNum.Load(),
		"SuccessfulSize":       t.SuccessfulSize.Load(),
		"FailedSize":           t.FailedSize.Load(),
		"FailedFileNum":        t.FailedFileNum.Load(),
	}
}

func (t *Task) CloseBackupDB() error {
	err := utils.DBClose(BackupDir + t.Id)
	if err != nil {
		return err
	}
	err = os.RemoveAll(BackupDir + t.Id)
	return err
}

func (t *Task) CloseIncrDB() error {
	err := utils.DBClose(IncrMsgDir + t.Id)
	if err != nil {
		return err
	}
	err = os.RemoveAll(IncrMsgDir + t.Id)
	return err
}
