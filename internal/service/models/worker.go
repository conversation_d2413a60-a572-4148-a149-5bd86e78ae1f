package models

import (
	"fmt"
	"sync"
)

// WorkerAddr Cpu Memory SpeedInUse Speed FailRate TimeoutRate

// 维护worker的心跳、主从状态、新旧状态等信息
type WorkerInfo struct {
	// worker标记
	Ip   string
	Port int
	// worker基础信息统计
	BaseInfo WorkerBaseInfo
	// task任务相关
	TaskInfo *sync.Map //map[string]WorkerTaskInfo
	// worker状态
	Available bool

	IsStke bool
	Region string
}

type WorkerBaseInfo struct {
	Cpu       float64
	Memory    float64
	LastHeart int64
}

type WorkerTaskInfo struct {
	SpeedInUse         int64
	SpeedAllocated     int64
	FailRate           float64
	TimeoutRate        float64
	ConcurrentFileNum  int64
	ConcurrentFileSize int64
	FragSet            []int64
	LastHeart          int64
}

// Url worker URL
func (w *WorkerInfo) Url() string {
	return fmt.Sprintf("http://%s:%d", w.Ip, w.Port)
}

// Addr
func (w *WorkerInfo) Addr() string {
	return fmt.Sprintf("%s:%d", w.Ip, w.Port)
}

// GetWorkerTaskInfoByTaskId
func (w *WorkerInfo) GetWorkerTaskInfoByTaskId(taskId string) (wti WorkerTaskInfo, ok bool) {
	rawTaskInfo, ok := w.TaskInfo.Load(taskId)
	if !ok {
		return wti, false
	}

	wti, ok = rawTaskInfo.(WorkerTaskInfo)
	return
}

// GetTaskInfo
func (w *WorkerInfo) GetTaskInfo() map[string]WorkerTaskInfo {
	var res = make(map[string]WorkerTaskInfo)
	w.TaskInfo.Range(func(k, v interface{}) bool {
		taskId, ok := k.(string)
		if !ok {
			return false
		}
		taskInfo, ok := v.(WorkerTaskInfo)
		if !ok {
			return false
		}
		res[taskId] = taskInfo
		return true
	})
	return res
}
