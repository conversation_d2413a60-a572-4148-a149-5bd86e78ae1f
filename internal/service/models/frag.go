package models

import (
	"time"

	"git.code.oa.com/cloud-msp/migration-lister/api"
	"git.code.oa.com/cloud-msp/migration-lister/pkg/utils"
	"go.uber.org/atomic"
)

// FragRuntimeInfo 分片的运行时数据, 分片下发时才会产生
type FragRuntimeInfo struct {
	FirstHeart *atomic.Value // time.Time
	LastHeart  *atomic.Value // time.Time
	Retry      *atomic.Int64 // int64
}

// 创建一个空的分片运行时数据
func NewFragRuntimeInfo() *FragRuntimeInfo {
	return &FragRuntimeInfo{
		FirstHeart: &atomic.Value{},
		LastHeart:  &atomic.Value{},
		Retry:      atomic.NewInt64(0),
	}
}

// FragBaseInfo 分片基本数据, 在分片组装的时候产生
type FragBaseInfo struct {
	TaskId string
	FragId int64
	Files  []*api.File
}

// Frag 完整分片数据, 由 base info 和 runtime info 组成
type Frag struct {
	BaseInfo    *FragBaseInfo
	RuntimeInfo *FragRuntimeInfo
	Over        *atomic.Bool //bool
}

// UpdateFirstHeart 更新分片第一次心跳时间
func (f *Frag) UpdateFirstHeart() {
	f.RuntimeInfo.FirstHeart.Store(time.Now())
}

func (f *Frag) InitFirstHeart() {
	f.RuntimeInfo.FirstHeart.Store(utils.FUTURE_DATE)
}

// UpdateLastHeart 更新分片最近一次心跳时间
func (f *Frag) UpdateLastHeart() {
	f.RuntimeInfo.LastHeart.Store(time.Now())
}

func (f *Frag) InitLastHeart() {
	f.RuntimeInfo.LastHeart.Store(utils.FUTURE_DATE)
}

// IncrRetry 分片重试次数 +1
func (f *Frag) IncrRetry() {
	f.RuntimeInfo.Retry.Add(1)
}

// SetOve 设置分片为 over 状态
func (f *Frag) SetOver() {
	f.Over.Store(true)
}
