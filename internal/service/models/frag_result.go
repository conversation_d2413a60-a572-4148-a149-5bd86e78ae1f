package models

import (
	"git.code.oa.com/cloud-msp/migration-lister/api"
	"git.code.oa.com/pulse-line/pl_boot"
	"go.uber.org/zap"
)

// FragResult 分片处理结果
type FragResult struct {
	BaseInfo *api.FragResult
}

// ConstructFragResult 根据分片构造分片处理结果 (BO)
func ConstructFragResult(frag *Frag) *FragResult {
	return &FragResult{
		BaseInfo: &api.FragResult{
			TaskId:                frag.BaseInfo.TaskId,
			FragId:                frag.BaseInfo.FragId,
			GenFragListerAgentUrl: "",
			WorkerIp:              "",
			WorkerPort:            0,
			WorkerUrl:             "",
			RetryNum:              frag.RuntimeInfo.Retry.Load(),
			FileIdToResult:        map[int64]bool{},
			SuccessFiles:          map[int64]*api.File{},
			FailedFiles:           map[int64]*api.File{},
			WaitMigrateFiles:      map[int64]*api.File{},
			FileNum:               int64(len(frag.BaseInfo.Files)),
			WaitMigrateNum:        int64(len(frag.BaseInfo.Files)), // 待迁移即file文件长度
			SuccessfulNum:         0,
			SuccessfulSize:        0,
			FailedSize:            0,
			FailedNum:             0,
		},
	}
}

// ConvertToFragResult 分片处理结果从 VO 转换成 BO
func ConvertToFragResult(fragResult *api.FragResult) *FragResult {
	return &FragResult{
		BaseInfo: fragResult,
	}
}

/**
 * 向fragResult 添加文件
 * @param fileId 文件id
 * @param file 实体文件
 * @param status 文件状态 true 成功文件 false 失败文件
 */
func (f *FragResult) AddFile(fileId int64, file *api.File, status bool) {
	value, ok := f.BaseInfo.FileIdToResult[fileId]
	if !ok { // 不存在 则增加
		if status { // 成功文件
			f.addSucFile(fileId, file)
		} else { // 失败文件
			f.addFailFile(fileId, file)
		}
		f.removeWaitFile(fileId)
		f.BaseInfo.FileIdToResult[fileId] = status
	} else if status != value { // 存在结果时且结果不同则覆盖则更新
		if status { // 成功文件
			delete(f.BaseInfo.FailedFiles, fileId)
			f.addSucFile(fileId, file)
		} else { // 失败文件
			delete(f.BaseInfo.SuccessFiles, fileId)
			f.addFailFile(fileId, file)
		}
	}
	pl_boot.AppCtx.GetAppLogger().Info("FragResult.AddFile ", zap.String("taskId", f.BaseInfo.TaskId),
		zap.Int64("fragId", f.BaseInfo.FragId), zap.Int64("fileId", fileId), zap.Int("suc len", len(f.BaseInfo.SuccessFiles)),
		zap.Int("fail len", len(f.BaseInfo.FailedFiles)), zap.Bool("status", status), zap.Bool("value", value))
}

/**
 * 添加成功文件
 */
func (f *FragResult) addSucFile(fileId int64, file *api.File) {
	f.BaseInfo.SuccessfulSize += file.Size
	f.BaseInfo.SuccessFiles[fileId] = file
	f.BaseInfo.SuccessfulNum += 1
}

/**
 * 添加失败文件
 */
func (f *FragResult) addFailFile(fileId int64, file *api.File) {
	f.BaseInfo.FailedSize += file.Size
	f.BaseInfo.FailedFiles[fileId] = file
	f.BaseInfo.FailedNum += 1
}

func (f *FragResult) removeWaitFile(fileId int64) {
	delete(f.BaseInfo.WaitMigrateFiles, fileId)
	f.BaseInfo.WaitMigrateNum -= 1
}

// IsEnd 分片是否处理结束
func (f *FragResult) IsEnd() bool {
	return f.BaseInfo.WaitMigrateNum == 0
}
