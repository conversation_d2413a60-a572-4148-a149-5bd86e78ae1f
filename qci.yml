version: '2.0'
env:
  RainbowEnv: test
  NameSpace: msp-migration
  Project: migration-master
stages:
- stage: 编译打包前准备
  tasks:
  - task: 编译打包前准备
    cmds:
    - plugin: cmds
      params:
        cmds:
        - echo "VERSION=$(date "+%Y%m%d%H%M%S")" >> $QCI_ENV_FILE
        - echo "LATEST_TAG=csighub.tencentyun.com/${NameSpace}/${Project}:latest"
          >> $QCI_ENV_FILE
        - echo "DATE_TAG=csighub.tencentyun.com/${NameSpace}/${Project}:$VERSION"
          >> $QCI_ENV_FILE
        - echo "SG_LATEST_TAG=csighub.tencentyun.com/${NameSpace}/${Project}-sg:latest"
          >> $QCI_ENV_FILE
        - echo "SG_DATE_TAG=csighub.tencentyun.com/${NameSpace}/${Project}-sg:$VERSION"
          >> $QCI_ENV_FILE
        - echo "TEST_DATE_TAG=csighub.tencentyun.com/${NameSpace}/${Project}-test:$VERSION"
          >> $QCI_ENV_FILE
        - echo "TEST_LATEST_TAG=csighub.tencentyun.com/${NameSpace}/${Project}-test:latest"
          >> $QCI_ENV_FILE
        - echo -n $TEST_DATE_TAG
        - echo -n $TEST_LATEST_TAG
        - echo -n $LATEST_TAG
        - echo -n $DATE_TAG
        - export GONOSUMDB='*.oa.com,*.tencent.com,*.woa.com'
        - export GOPRIVATE='git.code.oa.com,git.woa.com'
        - docker login ${DOCKERHUB_REPO} --username ${DOCKERHUB_USER} --password ${DOCKERHUB_TOKEN}
        - echo "docker login success"
- stage: 编译
  tasks:
  - task: 编译
    cmds:
    - plugin: cmds
      params:
        cmds:
        - go build -ldflags "-X google.golang.org/protobuf/reflect/protoregistry.conflictPolicy=warn"
          -o migration-master cmd/lister/lister.go
- stage: 设置配置文件环境
  tasks:
  - if: ${QCI_REPO_TAG} is not None
    task: 设置配置文件环境
    cmds:
    - plugin: cmds
      params:
        cmds:
        - echo "RainbowEnv=prod" >> $QCI_ENV_FILE
- stage: 拉取配置文件
  tasks:
  - task: 拉取配置文件
    cmds:
    - plugin: cmds
      params:
        cmds:
        - mkdir -p configs
    - plugin: Get_RainBow_File_To_Local
      params:
        project_env: $RainbowEnv
        is_signle_file: '0'
        multi_file_key:
        - pl_config.yaml
        - app_logger_config.yaml
        - query_logger_config.yaml
        multi_file_target_path:
        - configs/pl_config.yaml
        - configs/app_logger_config.yaml
        - configs/query_logger_config.yaml
        user_id: $RainbowUserId
        secret_key: $RainbowSecret
        project_appid: $RainbowAppId
        project_group: master
      label: 拉取配置
- stage: 构建镜像
  tasks:
  - task: 测试环境
    if: ${QCI_REPO_TAG} is None
    cmds:
    - plugin: cmds
      params:
        cmds:
        - docker build --force-rm --network=host -t $TEST_DATE_TAG -f Dockerfile --build-arg
          BUILD_INFO_GIT_HASH=$QCI_REPO_COMMIT --build-arg BUILD_INFO_GIT_BRANCH=$QCI_REPO_BRANCH
          ./
        - docker tag $TEST_DATE_TAG $TEST_LATEST_TAG
        - docker push $TEST_DATE_TAG
        - docker push $TEST_LATEST_TAG
  - task: 正式环境
    if: ${QCI_REPO_TAG} is not None
    cmds:
    - plugin: cmds
      params:
        cmds:
        - docker build --force-rm --network=host -t $DATE_TAG  -f Dockerfile --build-arg
          BUILD_INFO_GIT_HASH=$QCI_REPO_COMMIT --build-arg BUILD_INFO_GIT_BRANCH=$QCI_REPO_BRANCH
          ./
        - docker tag $DATE_TAG $LATEST_TAG
        - docker push $LATEST_TAG
        - docker push $DATE_TAG
- stage: 拉取国际站配置文件
  tasks:
  - task: 拉取国际站配置文件
    if: ${QCI_REPO_TAG} is not None
    cmds:
    - plugin: cmds
      params:
        cmds:
        - mkdir -p configs
    - plugin: Get_RainBow_File_To_Local
      params:
        project_env: $RainbowEnv
        is_signle_file: '0'
        multi_file_key:
        - pl_config.yaml
        - app_logger_config.yaml
        - query_logger_config.yaml
        multi_file_target_path:
        - configs/pl_config.yaml
        - configs/app_logger_config.yaml
        - configs/query_logger_config.yaml
        user_id: $RainbowUserId
        secret_key: $RainbowSecret
        project_appid: $RainbowAppId
        project_group: master-SG
      label: 拉取配置
- stage: 构建国际站镜像
  tasks:
  - task: 正式环境
    if: ${QCI_REPO_TAG} is not None
    cmds:
    - plugin: cmds
      params:
        cmds:
        - docker build --force-rm --network=host -t $SG_DATE_TAG  -f Dockerfile --build-arg
          BUILD_INFO_GIT_HASH=$QCI_REPO_COMMIT --build-arg BUILD_INFO_GIT_BRANCH=$QCI_REPO_BRANCH
          ./
        - docker tag $SG_DATE_TAG $SG_LATEST_TAG
        - docker push $SG_LATEST_TAG
        - docker push $SG_DATE_TAG
worker:
  language: go-18
  label: JOB_MATRIX_DEVCLOUD
  tools: []
