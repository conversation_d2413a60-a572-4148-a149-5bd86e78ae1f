#!/bin/sh

function process_args() {
  while [ "$#" -gt "0" ]
  do
    local key="$1"
    shift
    case $key in
      --git-repo)
        echo "git-repo"
        GIT_REPO="$1"
        shift
        ;;
      --git-url)
        echo "git-url"
        GIT_URL="$1"
        shift
        ;;
      --repo-user)
        echo "repo-user"
        REPO_USER="$1"
        shift
        ;;
      --repo-password)
        echo "repo-password"
        REPO_PASSWORD="$1"
        shift
        ;;
      --project-name)
        echo "project-name"
        PROJECT_NAME="$1"
        shift
        ;;
      --version)
        echo "version"
        VERSION="$1"
        shift
        ;;
      --dry-run)
        DRY_RUN=true
        shift
        ;;
      --help|-help|-h)
        print_usage
        exit 13
        ;;
      *)
        echo "ERROR: Unknown argument '$key'"
        exit -1
    esac
  done
}

function print_usage() {
  cat <<EOF
usage: $0

    --git-repo <repo>               The git repo where APIs are stored in suffix with .git
                                    The repo should be publicly available or providing user/passwd

    --git-url <url>                 The git url such as git.code.oa.com without http or https prefix.

    --repo-user <user>              The user name of git repo.
                                    We will assume git repo is publicly available if not provided.

    --repo-password <password>      The password of git repo

    --project-name <name>           The project name wish to be added in remote repo.


    --version   <version>           If specifying a version, then we will override
                                    api files in the specified version directory.

                                    We strongly recommend add version always.
                                    By default, pulse line style project will bellow format.

                                    Recommendations:
                                        production stage: v1, v2 ...
                                        beta stage:       beta-v1, beta-v2 ...
                                        dev stage:        dev-v1, dev-v2
                                        custom stage:     <xxx>-v1

    --dry-run                       Do not push to API git repository

Caution: Do not create any directory named as remote-api-temp-dir since it is used by default.
EOF
}

# Defining Path
# The purpose of this function is looking for api path in current package
# Currently, we only support
function define_path() {
    # Get script path
    SCRIPT_PATH=`dirname $0`

    # Define api path
    API_PATH=${SCRIPT_PATH}/../api

    # Define root path
    ROOT_PATH=${SCRIPT_PATH}/..

    # Define temp dir name
    TEMP_DIR_NAME=remote-api-temp-dir
}

# Create a temp directory for cloning api service
function create_temp_dir() {
    mkdir -p $ROOT_PATH/$TEMP_DIR_NAME
}

# Clone remote git repository
function clone_remote_api_repo() {
    cd $ROOT_PATH/$TEMP_DIR_NAME

    # git clone the APIs
    if [ -z "$REPO_PASSWORD" ]; then
        echo $GIT_URL/$REPO_USER/$GIT_REPO.git
        git clone https://$GIT_URL/$REPO_USER/$GIT_REPO.git
    else
        git clone https://$REPO_USER:$REPO_PASSWORD@$GIT_URL/$REPO_USER/$GIT_REPO.git
    fi
}

# Copy all the files into git repo
function copy_to_repo() {
    # cd into repo
    cd *

    # copy APIs to destination which is formatted as
    # project_name/version/
    rm -rf $PROJECT_NAME/$VERSION/
    mkdir -p $PROJECT_NAME/$VERSION/

    cp -r ../../api/* $PROJECT_NAME/$VERSION/
}

# Push
function push_to_git_repo() {
    git add *

    git commit -m "[APIs] Update APIs for $PROJECT_NAME:$VERSION [commit:pl_api_sync]"

    if [ -z "$REPO_PASSWORD" ]; then
        git push https://$GIT_URL/$REPO_USER/$GIT_REPO.git
    else
        git push https://$REPO_USER:$REPO_PASSWORD@$GIT_URL/$REPO_USER/$GIT_REPO.git
    fi
}

# Tearing down
function clear_temp_dir() {
    cd ../../

    rm -rf $TEMP_DIR_NAME
}

process_args $@

printf '%s\n' "Step 1: Defining Path."
define_path

printf '%s\n' "Step 2: Creating temp directory."
create_temp_dir

printf '%s\n' "Step 3: Cloning remote git."
clone_remote_api_repo

printf '%s\n' "Step 4: Copying to remote repo."
copy_to_repo

printf '%s\n' "Step 5: Pushing to remote git repo."
push_to_git_repo

printf '%s\n' "Step 5: Tearing down."
clear_temp_dir