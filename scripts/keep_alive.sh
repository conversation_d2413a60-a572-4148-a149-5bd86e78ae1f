#!/bin/sh
BASE_PATH=$(cd `dirname $0`;pwd)
if [ -z ${DOMAIN} ]
then
    export $(egrep -v '^#' ${BASE_PATH}/../.env | xargs)
else
    export $(egrep -v '^#' ${BASE_PATH}/../.env | grep -v DOMAIN | xargs)
fi
SERVICE=${PROJECT_NAME}

cd ${BASE_PATH}
pid=$(ps -ef | grep ${SERVICE} | grep -v "grep" | grep -v "scripts/keep_alive" | awk '{print $2}')
if [ -z "$pid" ]
then
    echo "${SERVICE} not exist"
    sh ${BASE_PATH}/start_daemon.sh
fi

# * * * * * /usr/local/services/${SERVICE}/scripts/keep_alive.sh > /dev/null 2>&1