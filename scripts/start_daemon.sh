#!/bin/sh
BASE_PATH=$(cd `dirname $0`;pwd)
if [ -z ${DOMAIN} ]
then
    export $(egrep -v '^#' ${BASE_PATH}/../.env | xargs)
else
    export $(egrep -v '^#' ${BASE_PATH}/../.env | grep -v DOMAIN | xargs)
fi
SERVICE=${PROJECT_NAME}
BIN_PATH=${BASE_PATH}/../bin
ROOT_PATH=${BASE_PATH}/..

chmod +x ${BIN_PATH}/*
echo "start rpc server..."
cd ${ROOT_PATH}

if [ -f "./bin/${SERVICE}" ]
then
	sync
	nohup ./bin/${SERVICE}  >/dev/stdout 2>&1 &
	#check if start successfully
	pid=$(ps -ef | grep ${SERVICE} | grep -v "grep" | grep -v "scripts/start" | awk '{print $1}')
	if [ -z "$pid" ]
	then
	    echo "start ${SERVICE} failed"
	else
	    echo "start ${SERVICE} successfully"
        fi
else
	echo "${SERVICE} not exist"
	exit -2
fi
