#!/bin/sh

set -e
BASE_PATH=$(cd `dirname $0`;pwd)
if  [ $# != 1 ] ;then
    echo "Usage: sh profile_analysis.sh [cpu|mem|block|mutex|goroutine|thread|allocs|trace] "
    exit 1;
fi


if ! [ -x "$(command -v pprof)" ]
then
    echo "install pprof"
    go get -u github.com/google/pprof
fi

Kernel=$(uname -s)
case "$Kernel" in
    Linux)  Kernel="linux"              ;;
    Darwin) Kernel="mac"                ;;
    FreeBSD)    Kernel="freebsd"            ;;
* ) echo "Your Operating System -> ITS NOT SUPPORTED" && exit -1 ;;
esac
if [ ${Kernel} = "linux" ]
then
    if [ -f /etc/redhat-release ];
    then
        SoftTools="yum"
        System="centos"
    fi

    if [ -f /etc/lsb-release ];
    then
        SoftTools="apt-get"
        System="ubuntu"
    fi
else
    SoftTools="brew"
    System="mac"
fi

# check if graphviz has been installed
if [ ${System} = "mac" ] && [ -z "$(brew list | grep graphviz)" ]
then
    brew install graphviz
elif [ ${System} = "centos" ] && [ -z "$(rpm -qa | grep graphviz)" ]
then
    yum -y install graphviz
elif [ ${System} = "ubuntu" ] && [ $(dpkg-query -W -f='${Status}' graphviz 2>/dev/null | grep -c "ok installed") -eq 0 ]
then
    apt-get update
    apt-get -y install graphviz
fi

if [ $1 = "cpu" ] || [ $1 = "mem" ] || [ $1 = "block" ] || [ $1 = "mutex" ] || [ $1 = "goroutine" ] || [ $1 = "thread" ] || [ $1 = "allocs" ];then
    if [ -f ${BASE_PATH}/../profile/$1_profile.out ];then
        go tool pprof -http=:8090 ${BASE_PATH}/../profile/$1_profile.out
    else
        echo "no $1 profile exist, check if $1 profiling has been enabled"
        exit 0
    fi
elif [ $1 = "trace" ];then
        if [ -f ${BASE_PATH}/../profile/$1_profile.out ];then
        go tool trace -http=localhost:8060 ${BASE_PATH}/../profile/$1_profile.out
    else
        echo "no $1 profile exist, check if $1 profiling has been enabled"
        exit 0
    fi
else
    echo "unsupported option, make sure to be one of them(cpu|mem|block|mutex|goroutine|thread|allocs|trace)"
fi