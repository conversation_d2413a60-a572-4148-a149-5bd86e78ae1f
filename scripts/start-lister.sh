#!/bin/sh
BASE_PATH=$(
  cd $(dirname $0)
  pwd
)
#if [ -z ${DOMAIN} ]
#then
#    export $(egrep -v '^#' ${BASE_PATH}/../.env | xargs)
#else
#    export $(egrep -v '^#' ${BASE_PATH}/../.env | grep -v DOMAIN | xargs)
#fi
SERVICE="migration-lister"
BIN_PATH=${BASE_PATH}/../bin
ROOT_PATH=${BASE_PATH}/..

chmod +x ${BIN_PATH}/*
cd ${ROOT_PATH}

sync

nohup ./bin/${SERVICE} >/dev/stdout 2>&1 &

ps_tail_line=`ps -ef | grep "tail -f ./log/lister.log" | awk 'END{print NR}'`
if [ "${ps_tail_line}" -lt 2 ]
then
  echo "tail panic and err log"
  nohup tail -f ./log/lister.log | grep -E "ERROR|panic" >> ./log/ErrorPanic.log &
else
  echo "tail panic and err log skip "
fi

