#!/bin/sh
BASE_PATH=$(cd `dirname $0`;pwd)
if [ -z ${DOMAIN} ]
then
    export $(egrep -v '^#' ${BASE_PATH}/../.env | xargs)
else
    export $(egrep -v '^#' ${BASE_PATH}/../.env | grep -v DOMAIN | xargs)
fi
SERVICE=${PROJECT_NAME}

pid=$(ps -ef | grep ${SERVICE} | grep -v "grep" | grep -v "scripts/start" | awk '{print $2}')
if [[ -z "$pid" ]]
then
    echo "${SERVICE} not exist"
else
    kill -INT ${pid}
 	echo "send signal to stop ${SERVICE}"
fi