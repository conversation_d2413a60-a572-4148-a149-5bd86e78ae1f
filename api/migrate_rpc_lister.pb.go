// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.26.0
// 	protoc        v3.5.1
// source: api/migrate_rpc_lister.proto

package api

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type TaskState int32

const (
	TaskState_ABSTAIN_STATUS     TaskState = 0   // proto3 枚举必须0开始，该值无意义
	TaskState_CREATED            TaskState = 2   //   创建完成
	TaskState_CHECKED_SUCCESSFUL TaskState = 4   //   校验通过
	TaskState_RUNNING            TaskState = 7   //   任务运行
	TaskState_SUCCESS            TaskState = 9   //   任务成功
	TaskState_FAILURE            TaskState = 10  //   任务失败
	TaskState_DELETION           TaskState = 13  //   任务删除
	TaskState_STOP               TaskState = 101 //   任务中止
	TaskState_RETRY              TaskState = 102 //   任务重试
	//以下是运行时可能用到的状态
	TaskState_UNKNOWN              TaskState = 500  //   未知
	TaskState_UNKNOWN_SERVER_STATE TaskState = 1001 //   未知的服务器端返回状态
	TaskState_UNKNOWN_AGENT_STATE  TaskState = 1002 //   未知的Agent返回状态
	TaskState_UNKNOWN_RPC_STATE    TaskState = 1003 //   未知的RPC返回状态
	TaskState_UNKNOWN_WORKER_STATE TaskState = 1004 //   未知的worker处理状态
)

// Enum value maps for TaskState.
var (
	TaskState_name = map[int32]string{
		0:    "ABSTAIN_STATUS",
		2:    "CREATED",
		4:    "CHECKED_SUCCESSFUL",
		7:    "RUNNING",
		9:    "SUCCESS",
		10:   "FAILURE",
		13:   "DELETION",
		101:  "STOP",
		102:  "RETRY",
		500:  "UNKNOWN",
		1001: "UNKNOWN_SERVER_STATE",
		1002: "UNKNOWN_AGENT_STATE",
		1003: "UNKNOWN_RPC_STATE",
		1004: "UNKNOWN_WORKER_STATE",
	}
	TaskState_value = map[string]int32{
		"ABSTAIN_STATUS":       0,
		"CREATED":              2,
		"CHECKED_SUCCESSFUL":   4,
		"RUNNING":              7,
		"SUCCESS":              9,
		"FAILURE":              10,
		"DELETION":             13,
		"STOP":                 101,
		"RETRY":                102,
		"UNKNOWN":              500,
		"UNKNOWN_SERVER_STATE": 1001,
		"UNKNOWN_AGENT_STATE":  1002,
		"UNKNOWN_RPC_STATE":    1003,
		"UNKNOWN_WORKER_STATE": 1004,
	}
)

func (x TaskState) Enum() *TaskState {
	p := new(TaskState)
	*p = x
	return p
}

func (x TaskState) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TaskState) Descriptor() protoreflect.EnumDescriptor {
	return file_api_migrate_rpc_lister_proto_enumTypes[0].Descriptor()
}

func (TaskState) Type() protoreflect.EnumType {
	return &file_api_migrate_rpc_lister_proto_enumTypes[0]
}

func (x TaskState) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TaskState.Descriptor instead.
func (TaskState) EnumDescriptor() ([]byte, []int) {
	return file_api_migrate_rpc_lister_proto_rawDescGZIP(), []int{0}
}

type ReceiveFragRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WorkerIp   string `protobuf:"bytes,1,opt,name=WorkerIp,proto3" json:"WorkerIp,omitempty"`
	WorkerPort int64  `protobuf:"varint,2,opt,name=WorkerPort,proto3" json:"WorkerPort,omitempty"`
	IsStke     bool   `protobuf:"varint,3,opt,name=IsStke,proto3" json:"IsStke,omitempty"`
	Region     string `protobuf:"bytes,4,opt,name=Region,proto3" json:"Region,omitempty"`
	JobId      string `protobuf:"bytes,5,opt,name=JobId,proto3" json:"JobId,omitempty"`
}

func (x *ReceiveFragRequest) Reset() {
	*x = ReceiveFragRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_migrate_rpc_lister_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReceiveFragRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReceiveFragRequest) ProtoMessage() {}

func (x *ReceiveFragRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_migrate_rpc_lister_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReceiveFragRequest.ProtoReflect.Descriptor instead.
func (*ReceiveFragRequest) Descriptor() ([]byte, []int) {
	return file_api_migrate_rpc_lister_proto_rawDescGZIP(), []int{0}
}

func (x *ReceiveFragRequest) GetWorkerIp() string {
	if x != nil {
		return x.WorkerIp
	}
	return ""
}

func (x *ReceiveFragRequest) GetWorkerPort() int64 {
	if x != nil {
		return x.WorkerPort
	}
	return 0
}

func (x *ReceiveFragRequest) GetIsStke() bool {
	if x != nil {
		return x.IsStke
	}
	return false
}

func (x *ReceiveFragRequest) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

func (x *ReceiveFragRequest) GetJobId() string {
	if x != nil {
		return x.JobId
	}
	return ""
}

type ReceiveFragResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Frag *Frag `protobuf:"bytes,1,opt,name=Frag,proto3" json:"Frag,omitempty"`
}

func (x *ReceiveFragResponse) Reset() {
	*x = ReceiveFragResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_migrate_rpc_lister_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReceiveFragResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReceiveFragResponse) ProtoMessage() {}

func (x *ReceiveFragResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_migrate_rpc_lister_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReceiveFragResponse.ProtoReflect.Descriptor instead.
func (*ReceiveFragResponse) Descriptor() ([]byte, []int) {
	return file_api_migrate_rpc_lister_proto_rawDescGZIP(), []int{1}
}

func (x *ReceiveFragResponse) GetFrag() *Frag {
	if x != nil {
		return x.Frag
	}
	return nil
}

type SendFile struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Path     string `protobuf:"bytes,1,opt,name=Path,proto3" json:"Path,omitempty"`         // 路径
	Size     int64  `protobuf:"varint,2,opt,name=Size,proto3" json:"Size,omitempty"`        // 大小
	UploadId string `protobuf:"bytes,3,opt,name=UploadId,proto3" json:"UploadId,omitempty"` // 已有分块上传的ID
}

func (x *SendFile) Reset() {
	*x = SendFile{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_migrate_rpc_lister_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SendFile) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendFile) ProtoMessage() {}

func (x *SendFile) ProtoReflect() protoreflect.Message {
	mi := &file_api_migrate_rpc_lister_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendFile.ProtoReflect.Descriptor instead.
func (*SendFile) Descriptor() ([]byte, []int) {
	return file_api_migrate_rpc_lister_proto_rawDescGZIP(), []int{2}
}

func (x *SendFile) GetPath() string {
	if x != nil {
		return x.Path
	}
	return ""
}

func (x *SendFile) GetSize() int64 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *SendFile) GetUploadId() string {
	if x != nil {
		return x.UploadId
	}
	return ""
}

//发送给worker要做迁移的分片
type Frag struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TaskId   string      `protobuf:"bytes,1,opt,name=TaskId,proto3" json:"TaskId,omitempty"`
	FragId   int64       `protobuf:"varint,2,opt,name=FragId,proto3" json:"FragId,omitempty"`
	Files    []*SendFile `protobuf:"bytes,3,rep,name=Files,proto3" json:"Files,omitempty"`
	RetryNum int64       `protobuf:"varint,4,opt,name=RetryNum,proto3" json:"RetryNum,omitempty"`
	ScanSize int64       `protobuf:"varint,5,opt,name=ScanSize,proto3" json:"ScanSize,omitempty"`
}

func (x *Frag) Reset() {
	*x = Frag{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_migrate_rpc_lister_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Frag) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Frag) ProtoMessage() {}

func (x *Frag) ProtoReflect() protoreflect.Message {
	mi := &file_api_migrate_rpc_lister_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Frag.ProtoReflect.Descriptor instead.
func (*Frag) Descriptor() ([]byte, []int) {
	return file_api_migrate_rpc_lister_proto_rawDescGZIP(), []int{3}
}

func (x *Frag) GetTaskId() string {
	if x != nil {
		return x.TaskId
	}
	return ""
}

func (x *Frag) GetFragId() int64 {
	if x != nil {
		return x.FragId
	}
	return 0
}

func (x *Frag) GetFiles() []*SendFile {
	if x != nil {
		return x.Files
	}
	return nil
}

func (x *Frag) GetRetryNum() int64 {
	if x != nil {
		return x.RetryNum
	}
	return 0
}

func (x *Frag) GetScanSize() int64 {
	if x != nil {
		return x.ScanSize
	}
	return 0
}

type ReceiveExecutedJobInfoRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WorkerIp      string   `protobuf:"bytes,1,opt,name=WorkerIp,proto3" json:"WorkerIp,omitempty"`
	WorkerPort    int64    `protobuf:"varint,2,opt,name=WorkerPort,proto3" json:"WorkerPort,omitempty"`
	IsStke        bool     `protobuf:"varint,3,opt,name=IsStke,proto3" json:"IsStke,omitempty"`
	Region        string   `protobuf:"bytes,4,opt,name=Region,proto3" json:"Region,omitempty"`
	CurrentJobIds []string `protobuf:"bytes,6,rep,name=CurrentJobIds,proto3" json:"CurrentJobIds,omitempty"`
}

func (x *ReceiveExecutedJobInfoRequest) Reset() {
	*x = ReceiveExecutedJobInfoRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_migrate_rpc_lister_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReceiveExecutedJobInfoRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReceiveExecutedJobInfoRequest) ProtoMessage() {}

func (x *ReceiveExecutedJobInfoRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_migrate_rpc_lister_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReceiveExecutedJobInfoRequest.ProtoReflect.Descriptor instead.
func (*ReceiveExecutedJobInfoRequest) Descriptor() ([]byte, []int) {
	return file_api_migrate_rpc_lister_proto_rawDescGZIP(), []int{4}
}

func (x *ReceiveExecutedJobInfoRequest) GetWorkerIp() string {
	if x != nil {
		return x.WorkerIp
	}
	return ""
}

func (x *ReceiveExecutedJobInfoRequest) GetWorkerPort() int64 {
	if x != nil {
		return x.WorkerPort
	}
	return 0
}

func (x *ReceiveExecutedJobInfoRequest) GetIsStke() bool {
	if x != nil {
		return x.IsStke
	}
	return false
}

func (x *ReceiveExecutedJobInfoRequest) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

func (x *ReceiveExecutedJobInfoRequest) GetCurrentJobIds() []string {
	if x != nil {
		return x.CurrentJobIds
	}
	return nil
}

type ReceiveExecutedJobInfoResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	NewJobs map[string]int64 `protobuf:"bytes,7,rep,name=NewJobs,proto3" json:"NewJobs,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
}

func (x *ReceiveExecutedJobInfoResponse) Reset() {
	*x = ReceiveExecutedJobInfoResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_migrate_rpc_lister_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReceiveExecutedJobInfoResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReceiveExecutedJobInfoResponse) ProtoMessage() {}

func (x *ReceiveExecutedJobInfoResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_migrate_rpc_lister_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReceiveExecutedJobInfoResponse.ProtoReflect.Descriptor instead.
func (*ReceiveExecutedJobInfoResponse) Descriptor() ([]byte, []int) {
	return file_api_migrate_rpc_lister_proto_rawDescGZIP(), []int{5}
}

func (x *ReceiveExecutedJobInfoResponse) GetNewJobs() map[string]int64 {
	if x != nil {
		return x.NewJobs
	}
	return nil
}

type NextSpeedRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WorkerIp       string `protobuf:"bytes,1,opt,name=WorkerIp,proto3" json:"WorkerIp,omitempty"`
	WorkerPort     int64  `protobuf:"varint,2,opt,name=WorkerPort,proto3" json:"WorkerPort,omitempty"`
	IsStke         bool   `protobuf:"varint,3,opt,name=IsStke,proto3" json:"IsStke,omitempty"`
	Region         string `protobuf:"bytes,4,opt,name=Region,proto3" json:"Region,omitempty"`
	AllocatedSpeed int64  `protobuf:"varint,6,opt,name=AllocatedSpeed,proto3" json:"AllocatedSpeed,omitempty"`
	RealSpeed      int64  `protobuf:"varint,7,opt,name=RealSpeed,proto3" json:"RealSpeed,omitempty"`
	JobId          string `protobuf:"bytes,8,opt,name=JobId,proto3" json:"JobId,omitempty"`
}

func (x *NextSpeedRequest) Reset() {
	*x = NextSpeedRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_migrate_rpc_lister_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NextSpeedRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NextSpeedRequest) ProtoMessage() {}

func (x *NextSpeedRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_migrate_rpc_lister_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NextSpeedRequest.ProtoReflect.Descriptor instead.
func (*NextSpeedRequest) Descriptor() ([]byte, []int) {
	return file_api_migrate_rpc_lister_proto_rawDescGZIP(), []int{6}
}

func (x *NextSpeedRequest) GetWorkerIp() string {
	if x != nil {
		return x.WorkerIp
	}
	return ""
}

func (x *NextSpeedRequest) GetWorkerPort() int64 {
	if x != nil {
		return x.WorkerPort
	}
	return 0
}

func (x *NextSpeedRequest) GetIsStke() bool {
	if x != nil {
		return x.IsStke
	}
	return false
}

func (x *NextSpeedRequest) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

func (x *NextSpeedRequest) GetAllocatedSpeed() int64 {
	if x != nil {
		return x.AllocatedSpeed
	}
	return 0
}

func (x *NextSpeedRequest) GetRealSpeed() int64 {
	if x != nil {
		return x.RealSpeed
	}
	return 0
}

func (x *NextSpeedRequest) GetJobId() string {
	if x != nil {
		return x.JobId
	}
	return ""
}

type NextSpeedResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	NextSpeed int64 `protobuf:"varint,1,opt,name=NextSpeed,proto3" json:"NextSpeed,omitempty"`
}

func (x *NextSpeedResponse) Reset() {
	*x = NextSpeedResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_migrate_rpc_lister_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NextSpeedResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NextSpeedResponse) ProtoMessage() {}

func (x *NextSpeedResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_migrate_rpc_lister_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NextSpeedResponse.ProtoReflect.Descriptor instead.
func (*NextSpeedResponse) Descriptor() ([]byte, []int) {
	return file_api_migrate_rpc_lister_proto_rawDescGZIP(), []int{7}
}

func (x *NextSpeedResponse) GetNextSpeed() int64 {
	if x != nil {
		return x.NextSpeed
	}
	return 0
}

type JobInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	JobId               string `protobuf:"bytes,1,opt,name=JobId,proto3" json:"JobId,omitempty"`
	SrcService          string `protobuf:"bytes,2,opt,name=SrcService,proto3" json:"SrcService,omitempty"`
	SrcSecretId         string `protobuf:"bytes,3,opt,name=SrcSecretId,proto3" json:"SrcSecretId,omitempty"`
	SrcSecretKey        string `protobuf:"bytes,4,opt,name=SrcSecretKey,proto3" json:"SrcSecretKey,omitempty"`
	SrcBucket           string `protobuf:"bytes,5,opt,name=SrcBucket,proto3" json:"SrcBucket,omitempty"`
	SrcRegion           string `protobuf:"bytes,6,opt,name=SrcRegion,proto3" json:"SrcRegion,omitempty"`
	DstSecretId         string `protobuf:"bytes,9,opt,name=DstSecretId,proto3" json:"DstSecretId,omitempty"`
	DstSecretKey        string `protobuf:"bytes,10,opt,name=DstSecretKey,proto3" json:"DstSecretKey,omitempty"`
	DstBucket           string `protobuf:"bytes,11,opt,name=DstBucket,proto3" json:"DstBucket,omitempty"`
	DstRegion           string `protobuf:"bytes,12,opt,name=DstRegion,proto3" json:"DstRegion,omitempty"`
	UserAppId           int64  `protobuf:"varint,13,opt,name=UserAppId,proto3" json:"UserAppId,omitempty"`
	SrcFileName         string `protobuf:"bytes,14,opt,name=SrcFileName,proto3" json:"SrcFileName,omitempty"`
	SrcFileUrl          string `protobuf:"bytes,15,opt,name=SrcFileUrl,proto3" json:"SrcFileUrl,omitempty"`
	PathConf            int32  `protobuf:"varint,16,opt,name=PathConf,proto3" json:"PathConf,omitempty"` //桶内路径设置，0:将URL中路径用于桶内,1:忽略URL中路径
	SavePath            string `protobuf:"bytes,17,opt,name=SavePath,proto3" json:"SavePath,omitempty"`
	StorageType         int32  `protobuf:"varint,20,opt,name=StorageType,proto3" json:"StorageType,omitempty"`                 // 文件存储方式：全部使用标准存储：StorageType = 1；全部使用低频存储：StorageType = 2；保持原存储属性：StorageType = 3；保存为归档存储：StorageType = 4；
	MigrationHeaderType int32  `protobuf:"varint,21,opt,name=MigrationHeaderType,proto3" json:"MigrationHeaderType,omitempty"` //Header迁移方式,1 保留全部源Header|2 丢弃全部源Header|3 保留部分源Header
	MigrationHeader     string `protobuf:"bytes,22,opt,name=MigrationHeader,proto3" json:"MigrationHeader,omitempty"`          //MigrationHeaderType==3时,存储Header对应关系
	FileOverWrite       int32  `protobuf:"varint,25,opt,name=FileOverWrite,proto3" json:"FileOverWrite,omitempty"`
	LimitQps            int64  `protobuf:"varint,26,opt,name=LimitQps,proto3" json:"LimitQps,omitempty"`
	LimitFlow           int64  `protobuf:"varint,27,opt,name=LimitFlow,proto3" json:"LimitFlow,omitempty"`
	BucketType          string `protobuf:"bytes,28,opt,name=BucketType,proto3" json:"BucketType,omitempty"`
	KodoEndpoint        string `protobuf:"bytes,29,opt,name=KodoEndpoint,proto3" json:"KodoEndpoint,omitempty"`
	NewHeaders          string `protobuf:"bytes,30,opt,name=NewHeaders,proto3" json:"NewHeaders,omitempty"` // 新增头部 JSON字符串
}

func (x *JobInfo) Reset() {
	*x = JobInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_migrate_rpc_lister_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *JobInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*JobInfo) ProtoMessage() {}

func (x *JobInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_migrate_rpc_lister_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use JobInfo.ProtoReflect.Descriptor instead.
func (*JobInfo) Descriptor() ([]byte, []int) {
	return file_api_migrate_rpc_lister_proto_rawDescGZIP(), []int{8}
}

func (x *JobInfo) GetJobId() string {
	if x != nil {
		return x.JobId
	}
	return ""
}

func (x *JobInfo) GetSrcService() string {
	if x != nil {
		return x.SrcService
	}
	return ""
}

func (x *JobInfo) GetSrcSecretId() string {
	if x != nil {
		return x.SrcSecretId
	}
	return ""
}

func (x *JobInfo) GetSrcSecretKey() string {
	if x != nil {
		return x.SrcSecretKey
	}
	return ""
}

func (x *JobInfo) GetSrcBucket() string {
	if x != nil {
		return x.SrcBucket
	}
	return ""
}

func (x *JobInfo) GetSrcRegion() string {
	if x != nil {
		return x.SrcRegion
	}
	return ""
}

func (x *JobInfo) GetDstSecretId() string {
	if x != nil {
		return x.DstSecretId
	}
	return ""
}

func (x *JobInfo) GetDstSecretKey() string {
	if x != nil {
		return x.DstSecretKey
	}
	return ""
}

func (x *JobInfo) GetDstBucket() string {
	if x != nil {
		return x.DstBucket
	}
	return ""
}

func (x *JobInfo) GetDstRegion() string {
	if x != nil {
		return x.DstRegion
	}
	return ""
}

func (x *JobInfo) GetUserAppId() int64 {
	if x != nil {
		return x.UserAppId
	}
	return 0
}

func (x *JobInfo) GetSrcFileName() string {
	if x != nil {
		return x.SrcFileName
	}
	return ""
}

func (x *JobInfo) GetSrcFileUrl() string {
	if x != nil {
		return x.SrcFileUrl
	}
	return ""
}

func (x *JobInfo) GetPathConf() int32 {
	if x != nil {
		return x.PathConf
	}
	return 0
}

func (x *JobInfo) GetSavePath() string {
	if x != nil {
		return x.SavePath
	}
	return ""
}

func (x *JobInfo) GetStorageType() int32 {
	if x != nil {
		return x.StorageType
	}
	return 0
}

func (x *JobInfo) GetMigrationHeaderType() int32 {
	if x != nil {
		return x.MigrationHeaderType
	}
	return 0
}

func (x *JobInfo) GetMigrationHeader() string {
	if x != nil {
		return x.MigrationHeader
	}
	return ""
}

func (x *JobInfo) GetFileOverWrite() int32 {
	if x != nil {
		return x.FileOverWrite
	}
	return 0
}

func (x *JobInfo) GetLimitQps() int64 {
	if x != nil {
		return x.LimitQps
	}
	return 0
}

func (x *JobInfo) GetLimitFlow() int64 {
	if x != nil {
		return x.LimitFlow
	}
	return 0
}

func (x *JobInfo) GetBucketType() string {
	if x != nil {
		return x.BucketType
	}
	return ""
}

func (x *JobInfo) GetKodoEndpoint() string {
	if x != nil {
		return x.KodoEndpoint
	}
	return ""
}

func (x *JobInfo) GetNewHeaders() string {
	if x != nil {
		return x.NewHeaders
	}
	return ""
}

type ReceiveTaskInfoRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TaskId string `protobuf:"bytes,1,opt,name=TaskId,proto3" json:"TaskId,omitempty"`
}

func (x *ReceiveTaskInfoRequest) Reset() {
	*x = ReceiveTaskInfoRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_migrate_rpc_lister_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReceiveTaskInfoRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReceiveTaskInfoRequest) ProtoMessage() {}

func (x *ReceiveTaskInfoRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_migrate_rpc_lister_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReceiveTaskInfoRequest.ProtoReflect.Descriptor instead.
func (*ReceiveTaskInfoRequest) Descriptor() ([]byte, []int) {
	return file_api_migrate_rpc_lister_proto_rawDescGZIP(), []int{9}
}

func (x *ReceiveTaskInfoRequest) GetTaskId() string {
	if x != nil {
		return x.TaskId
	}
	return ""
}

type ReceiveTaskInfoResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	JobInfo *JobInfo `protobuf:"bytes,1,opt,name=JobInfo,proto3" json:"JobInfo,omitempty"`
}

func (x *ReceiveTaskInfoResponse) Reset() {
	*x = ReceiveTaskInfoResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_migrate_rpc_lister_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReceiveTaskInfoResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReceiveTaskInfoResponse) ProtoMessage() {}

func (x *ReceiveTaskInfoResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_migrate_rpc_lister_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReceiveTaskInfoResponse.ProtoReflect.Descriptor instead.
func (*ReceiveTaskInfoResponse) Descriptor() ([]byte, []int) {
	return file_api_migrate_rpc_lister_proto_rawDescGZIP(), []int{10}
}

func (x *ReceiveTaskInfoResponse) GetJobInfo() *JobInfo {
	if x != nil {
		return x.JobInfo
	}
	return nil
}

// A single book in the library.
type ReceiveWorkRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Work *WorkInfo `protobuf:"bytes,1,opt,name=Work,proto3" json:"Work,omitempty"`
}

func (x *ReceiveWorkRequest) Reset() {
	*x = ReceiveWorkRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_migrate_rpc_lister_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReceiveWorkRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReceiveWorkRequest) ProtoMessage() {}

func (x *ReceiveWorkRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_migrate_rpc_lister_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReceiveWorkRequest.ProtoReflect.Descriptor instead.
func (*ReceiveWorkRequest) Descriptor() ([]byte, []int) {
	return file_api_migrate_rpc_lister_proto_rawDescGZIP(), []int{11}
}

func (x *ReceiveWorkRequest) GetWork() *WorkInfo {
	if x != nil {
		return x.Work
	}
	return nil
}

type ReceiveWorkResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Res bool `protobuf:"varint,1,opt,name=Res,proto3" json:"Res,omitempty"`
}

func (x *ReceiveWorkResponse) Reset() {
	*x = ReceiveWorkResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_migrate_rpc_lister_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReceiveWorkResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReceiveWorkResponse) ProtoMessage() {}

func (x *ReceiveWorkResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_migrate_rpc_lister_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReceiveWorkResponse.ProtoReflect.Descriptor instead.
func (*ReceiveWorkResponse) Descriptor() ([]byte, []int) {
	return file_api_migrate_rpc_lister_proto_rawDescGZIP(), []int{12}
}

func (x *ReceiveWorkResponse) GetRes() bool {
	if x != nil {
		return x.Res
	}
	return false
}

type CommonNoReplyResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *CommonNoReplyResponse) Reset() {
	*x = CommonNoReplyResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_migrate_rpc_lister_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CommonNoReplyResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CommonNoReplyResponse) ProtoMessage() {}

func (x *CommonNoReplyResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_migrate_rpc_lister_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CommonNoReplyResponse.ProtoReflect.Descriptor instead.
func (*CommonNoReplyResponse) Descriptor() ([]byte, []int) {
	return file_api_migrate_rpc_lister_proto_rawDescGZIP(), []int{13}
}

type ReceiveFragResultRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FragResult *FragResult `protobuf:"bytes,1,opt,name=FragResult,proto3" json:"FragResult,omitempty"`
}

func (x *ReceiveFragResultRequest) Reset() {
	*x = ReceiveFragResultRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_migrate_rpc_lister_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReceiveFragResultRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReceiveFragResultRequest) ProtoMessage() {}

func (x *ReceiveFragResultRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_migrate_rpc_lister_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReceiveFragResultRequest.ProtoReflect.Descriptor instead.
func (*ReceiveFragResultRequest) Descriptor() ([]byte, []int) {
	return file_api_migrate_rpc_lister_proto_rawDescGZIP(), []int{14}
}

func (x *ReceiveFragResultRequest) GetFragResult() *FragResult {
	if x != nil {
		return x.FragResult
	}
	return nil
}

type ReceiveFragResultResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Res bool `protobuf:"varint,1,opt,name=Res,proto3" json:"Res,omitempty"`
}

func (x *ReceiveFragResultResponse) Reset() {
	*x = ReceiveFragResultResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_migrate_rpc_lister_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReceiveFragResultResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReceiveFragResultResponse) ProtoMessage() {}

func (x *ReceiveFragResultResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_migrate_rpc_lister_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReceiveFragResultResponse.ProtoReflect.Descriptor instead.
func (*ReceiveFragResultResponse) Descriptor() ([]byte, []int) {
	return file_api_migrate_rpc_lister_proto_rawDescGZIP(), []int{15}
}

func (x *ReceiveFragResultResponse) GetRes() bool {
	if x != nil {
		return x.Res
	}
	return false
}

type ReceiveTaskStateRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TaskId string `protobuf:"bytes,1,opt,name=TaskId,proto3" json:"TaskId,omitempty"`
}

func (x *ReceiveTaskStateRequest) Reset() {
	*x = ReceiveTaskStateRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_migrate_rpc_lister_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReceiveTaskStateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReceiveTaskStateRequest) ProtoMessage() {}

func (x *ReceiveTaskStateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_migrate_rpc_lister_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReceiveTaskStateRequest.ProtoReflect.Descriptor instead.
func (*ReceiveTaskStateRequest) Descriptor() ([]byte, []int) {
	return file_api_migrate_rpc_lister_proto_rawDescGZIP(), []int{16}
}

func (x *ReceiveTaskStateRequest) GetTaskId() string {
	if x != nil {
		return x.TaskId
	}
	return ""
}

type ReceiveTaskStateResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	QpsLimit  int64     `protobuf:"varint,1,opt,name=QpsLimit,proto3" json:"QpsLimit,omitempty"`
	TaskState TaskState `protobuf:"varint,2,opt,name=TaskState,proto3,enum=api.TaskState" json:"TaskState,omitempty"`
}

func (x *ReceiveTaskStateResponse) Reset() {
	*x = ReceiveTaskStateResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_migrate_rpc_lister_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReceiveTaskStateResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReceiveTaskStateResponse) ProtoMessage() {}

func (x *ReceiveTaskStateResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_migrate_rpc_lister_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReceiveTaskStateResponse.ProtoReflect.Descriptor instead.
func (*ReceiveTaskStateResponse) Descriptor() ([]byte, []int) {
	return file_api_migrate_rpc_lister_proto_rawDescGZIP(), []int{17}
}

func (x *ReceiveTaskStateResponse) GetQpsLimit() int64 {
	if x != nil {
		return x.QpsLimit
	}
	return 0
}

func (x *ReceiveTaskStateResponse) GetTaskState() TaskState {
	if x != nil {
		return x.TaskState
	}
	return TaskState_ABSTAIN_STATUS
}

type ReceiveTaskQpsConsumeRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TaskId       string `protobuf:"bytes,1,opt,name=TaskId,proto3" json:"TaskId,omitempty"`
	NeedQpsToken int64  `protobuf:"varint,2,opt,name=NeedQpsToken,proto3" json:"NeedQpsToken,omitempty"`
}

func (x *ReceiveTaskQpsConsumeRequest) Reset() {
	*x = ReceiveTaskQpsConsumeRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_migrate_rpc_lister_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReceiveTaskQpsConsumeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReceiveTaskQpsConsumeRequest) ProtoMessage() {}

func (x *ReceiveTaskQpsConsumeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_migrate_rpc_lister_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReceiveTaskQpsConsumeRequest.ProtoReflect.Descriptor instead.
func (*ReceiveTaskQpsConsumeRequest) Descriptor() ([]byte, []int) {
	return file_api_migrate_rpc_lister_proto_rawDescGZIP(), []int{18}
}

func (x *ReceiveTaskQpsConsumeRequest) GetTaskId() string {
	if x != nil {
		return x.TaskId
	}
	return ""
}

func (x *ReceiveTaskQpsConsumeRequest) GetNeedQpsToken() int64 {
	if x != nil {
		return x.NeedQpsToken
	}
	return 0
}

type ReceiveTaskQpsConsumeResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TaskQpsLimit int64 `protobuf:"varint,1,opt,name=TaskQpsLimit,proto3" json:"TaskQpsLimit,omitempty"`
}

func (x *ReceiveTaskQpsConsumeResponse) Reset() {
	*x = ReceiveTaskQpsConsumeResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_migrate_rpc_lister_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReceiveTaskQpsConsumeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReceiveTaskQpsConsumeResponse) ProtoMessage() {}

func (x *ReceiveTaskQpsConsumeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_migrate_rpc_lister_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReceiveTaskQpsConsumeResponse.ProtoReflect.Descriptor instead.
func (*ReceiveTaskQpsConsumeResponse) Descriptor() ([]byte, []int) {
	return file_api_migrate_rpc_lister_proto_rawDescGZIP(), []int{19}
}

func (x *ReceiveTaskQpsConsumeResponse) GetTaskQpsLimit() int64 {
	if x != nil {
		return x.TaskQpsLimit
	}
	return 0
}

type ReceiveTaskFlowConsumeRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TaskId        string `protobuf:"bytes,1,opt,name=TaskId,proto3" json:"TaskId,omitempty"`
	NeedFlowToken int64  `protobuf:"varint,2,opt,name=NeedFlowToken,proto3" json:"NeedFlowToken,omitempty"`
}

func (x *ReceiveTaskFlowConsumeRequest) Reset() {
	*x = ReceiveTaskFlowConsumeRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_migrate_rpc_lister_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReceiveTaskFlowConsumeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReceiveTaskFlowConsumeRequest) ProtoMessage() {}

func (x *ReceiveTaskFlowConsumeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_migrate_rpc_lister_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReceiveTaskFlowConsumeRequest.ProtoReflect.Descriptor instead.
func (*ReceiveTaskFlowConsumeRequest) Descriptor() ([]byte, []int) {
	return file_api_migrate_rpc_lister_proto_rawDescGZIP(), []int{20}
}

func (x *ReceiveTaskFlowConsumeRequest) GetTaskId() string {
	if x != nil {
		return x.TaskId
	}
	return ""
}

func (x *ReceiveTaskFlowConsumeRequest) GetNeedFlowToken() int64 {
	if x != nil {
		return x.NeedFlowToken
	}
	return 0
}

type ReceiveTaskFlowConsumeResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TaskFlowLimit int64 `protobuf:"varint,1,opt,name=TaskFlowLimit,proto3" json:"TaskFlowLimit,omitempty"`
}

func (x *ReceiveTaskFlowConsumeResponse) Reset() {
	*x = ReceiveTaskFlowConsumeResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_migrate_rpc_lister_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReceiveTaskFlowConsumeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReceiveTaskFlowConsumeResponse) ProtoMessage() {}

func (x *ReceiveTaskFlowConsumeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_migrate_rpc_lister_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReceiveTaskFlowConsumeResponse.ProtoReflect.Descriptor instead.
func (*ReceiveTaskFlowConsumeResponse) Descriptor() ([]byte, []int) {
	return file_api_migrate_rpc_lister_proto_rawDescGZIP(), []int{21}
}

func (x *ReceiveTaskFlowConsumeResponse) GetTaskFlowLimit() int64 {
	if x != nil {
		return x.TaskFlowLimit
	}
	return 0
}

type WorkInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WorkerIp   string          `protobuf:"bytes,3,opt,name=WorkerIp,proto3" json:"WorkerIp,omitempty"`
	WorkerPort int64           `protobuf:"varint,4,opt,name=WorkerPort,proto3" json:"WorkerPort,omitempty"`
	BaseInfo   *WorkerBaseInfo `protobuf:"bytes,5,opt,name=BaseInfo,proto3" json:"BaseInfo,omitempty"`
	TaskInfo   *WorkerTaskInfo `protobuf:"bytes,6,opt,name=TaskInfo,proto3" json:"TaskInfo,omitempty"`
	IsStke     bool            `protobuf:"varint,7,opt,name=IsStke,proto3" json:"IsStke,omitempty"`
	Region     string          `protobuf:"bytes,9,opt,name=Region,proto3" json:"Region,omitempty"`
}

func (x *WorkInfo) Reset() {
	*x = WorkInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_migrate_rpc_lister_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WorkInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WorkInfo) ProtoMessage() {}

func (x *WorkInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_migrate_rpc_lister_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WorkInfo.ProtoReflect.Descriptor instead.
func (*WorkInfo) Descriptor() ([]byte, []int) {
	return file_api_migrate_rpc_lister_proto_rawDescGZIP(), []int{22}
}

func (x *WorkInfo) GetWorkerIp() string {
	if x != nil {
		return x.WorkerIp
	}
	return ""
}

func (x *WorkInfo) GetWorkerPort() int64 {
	if x != nil {
		return x.WorkerPort
	}
	return 0
}

func (x *WorkInfo) GetBaseInfo() *WorkerBaseInfo {
	if x != nil {
		return x.BaseInfo
	}
	return nil
}

func (x *WorkInfo) GetTaskInfo() *WorkerTaskInfo {
	if x != nil {
		return x.TaskInfo
	}
	return nil
}

func (x *WorkInfo) GetIsStke() bool {
	if x != nil {
		return x.IsStke
	}
	return false
}

func (x *WorkInfo) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

type WorkerBaseInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Cpu       float32 `protobuf:"fixed32,8,opt,name=Cpu,proto3" json:"Cpu,omitempty"`
	Memory    float32 `protobuf:"fixed32,9,opt,name=Memory,proto3" json:"Memory,omitempty"`
	Timestamp int64   `protobuf:"varint,10,opt,name=Timestamp,proto3" json:"Timestamp,omitempty"`
}

func (x *WorkerBaseInfo) Reset() {
	*x = WorkerBaseInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_migrate_rpc_lister_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WorkerBaseInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WorkerBaseInfo) ProtoMessage() {}

func (x *WorkerBaseInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_migrate_rpc_lister_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WorkerBaseInfo.ProtoReflect.Descriptor instead.
func (*WorkerBaseInfo) Descriptor() ([]byte, []int) {
	return file_api_migrate_rpc_lister_proto_rawDescGZIP(), []int{23}
}

func (x *WorkerBaseInfo) GetCpu() float32 {
	if x != nil {
		return x.Cpu
	}
	return 0
}

func (x *WorkerBaseInfo) GetMemory() float32 {
	if x != nil {
		return x.Memory
	}
	return 0
}

func (x *WorkerBaseInfo) GetTimestamp() int64 {
	if x != nil {
		return x.Timestamp
	}
	return 0
}

type WorkerTaskInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TaskId             string  `protobuf:"bytes,9,opt,name=TaskId,proto3" json:"TaskId,omitempty"`
	SpeedInUse         int64   `protobuf:"varint,10,opt,name=SpeedInUse,proto3" json:"SpeedInUse,omitempty"`
	SpeedAllocated     int64   `protobuf:"varint,11,opt,name=SpeedAllocated,proto3" json:"SpeedAllocated,omitempty"`
	FailRate           float32 `protobuf:"fixed32,12,opt,name=FailRate,proto3" json:"FailRate,omitempty"`
	TimeoutRate        float32 `protobuf:"fixed32,13,opt,name=TimeoutRate,proto3" json:"TimeoutRate,omitempty"`
	ConcurrentFileNum  int64   `protobuf:"varint,14,opt,name=ConcurrentFileNum,proto3" json:"ConcurrentFileNum,omitempty"`
	ConcurrentFileSize int64   `protobuf:"varint,15,opt,name=ConcurrentFileSize,proto3" json:"ConcurrentFileSize,omitempty"`
	// 用于rpc传输后记录简要信息的结构: [15, 16] // 按照task上报，而不是worker上报
	FragIdSet []int64 `protobuf:"varint,16,rep,packed,name=FragIdSet,proto3" json:"FragIdSet,omitempty"`
}

func (x *WorkerTaskInfo) Reset() {
	*x = WorkerTaskInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_migrate_rpc_lister_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WorkerTaskInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WorkerTaskInfo) ProtoMessage() {}

func (x *WorkerTaskInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_migrate_rpc_lister_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WorkerTaskInfo.ProtoReflect.Descriptor instead.
func (*WorkerTaskInfo) Descriptor() ([]byte, []int) {
	return file_api_migrate_rpc_lister_proto_rawDescGZIP(), []int{24}
}

func (x *WorkerTaskInfo) GetTaskId() string {
	if x != nil {
		return x.TaskId
	}
	return ""
}

func (x *WorkerTaskInfo) GetSpeedInUse() int64 {
	if x != nil {
		return x.SpeedInUse
	}
	return 0
}

func (x *WorkerTaskInfo) GetSpeedAllocated() int64 {
	if x != nil {
		return x.SpeedAllocated
	}
	return 0
}

func (x *WorkerTaskInfo) GetFailRate() float32 {
	if x != nil {
		return x.FailRate
	}
	return 0
}

func (x *WorkerTaskInfo) GetTimeoutRate() float32 {
	if x != nil {
		return x.TimeoutRate
	}
	return 0
}

func (x *WorkerTaskInfo) GetConcurrentFileNum() int64 {
	if x != nil {
		return x.ConcurrentFileNum
	}
	return 0
}

func (x *WorkerTaskInfo) GetConcurrentFileSize() int64 {
	if x != nil {
		return x.ConcurrentFileSize
	}
	return 0
}

func (x *WorkerTaskInfo) GetFragIdSet() []int64 {
	if x != nil {
		return x.FragIdSet
	}
	return nil
}

type FragResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TaskId                string `protobuf:"bytes,1,opt,name=TaskId,proto3" json:"TaskId,omitempty"`
	FragId                int64  `protobuf:"varint,2,opt,name=FragId,proto3" json:"FragId,omitempty"`
	GenFragListerAgentUrl string `protobuf:"bytes,3,opt,name=GenFragListerAgentUrl,proto3" json:"GenFragListerAgentUrl,omitempty"`
	WorkerIp              string `protobuf:"bytes,4,opt,name=WorkerIp,proto3" json:"WorkerIp,omitempty"`
	WorkerPort            int64  `protobuf:"varint,5,opt,name=WorkerPort,proto3" json:"WorkerPort,omitempty"`
	WorkerUrl             string `protobuf:"bytes,6,opt,name=WorkerUrl,proto3" json:"WorkerUrl,omitempty"`
	RetryNum              int64  `protobuf:"varint,7,opt,name=RetryNum,proto3" json:"RetryNum,omitempty"`
	//   {FileId: bool} True为成功, False为失败
	FileIdToResult map[int64]bool `protobuf:"bytes,8,rep,name=FileIdToResult,proto3" json:"FileIdToResult,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	//   {FileId: File}
	SuccessFiles map[int64]*File `protobuf:"bytes,9,rep,name=SuccessFiles,proto3" json:"SuccessFiles,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	//   {FileId: File}
	FailedFiles map[int64]*File `protobuf:"bytes,10,rep,name=FailedFiles,proto3" json:"FailedFiles,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	//   浅拷贝dict {FileId: File}
	WaitMigrateFiles map[int64]*File `protobuf:"bytes,11,rep,name=WaitMigrateFiles,proto3" json:"WaitMigrateFiles,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	FileNum          int64           `protobuf:"varint,12,opt,name=FileNum,proto3" json:"FileNum,omitempty"`
	SuccessfulNum    int64           `protobuf:"varint,13,opt,name=SuccessfulNum,proto3" json:"SuccessfulNum,omitempty"`
	FailedNum        int64           `protobuf:"varint,14,opt,name=FailedNum,proto3" json:"FailedNum,omitempty"`
	WaitMigrateNum   int64           `protobuf:"varint,15,opt,name=WaitMigrateNum,proto3" json:"WaitMigrateNum,omitempty"`
	Size             int64           `protobuf:"varint,16,opt,name=Size,proto3" json:"Size,omitempty"`
	SuccessfulSize   int64           `protobuf:"varint,17,opt,name=SuccessfulSize,proto3" json:"SuccessfulSize,omitempty"`
	FailedSize       int64           `protobuf:"varint,18,opt,name=FailedSize,proto3" json:"FailedSize,omitempty"`
}

func (x *FragResult) Reset() {
	*x = FragResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_migrate_rpc_lister_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FragResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FragResult) ProtoMessage() {}

func (x *FragResult) ProtoReflect() protoreflect.Message {
	mi := &file_api_migrate_rpc_lister_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FragResult.ProtoReflect.Descriptor instead.
func (*FragResult) Descriptor() ([]byte, []int) {
	return file_api_migrate_rpc_lister_proto_rawDescGZIP(), []int{25}
}

func (x *FragResult) GetTaskId() string {
	if x != nil {
		return x.TaskId
	}
	return ""
}

func (x *FragResult) GetFragId() int64 {
	if x != nil {
		return x.FragId
	}
	return 0
}

func (x *FragResult) GetGenFragListerAgentUrl() string {
	if x != nil {
		return x.GenFragListerAgentUrl
	}
	return ""
}

func (x *FragResult) GetWorkerIp() string {
	if x != nil {
		return x.WorkerIp
	}
	return ""
}

func (x *FragResult) GetWorkerPort() int64 {
	if x != nil {
		return x.WorkerPort
	}
	return 0
}

func (x *FragResult) GetWorkerUrl() string {
	if x != nil {
		return x.WorkerUrl
	}
	return ""
}

func (x *FragResult) GetRetryNum() int64 {
	if x != nil {
		return x.RetryNum
	}
	return 0
}

func (x *FragResult) GetFileIdToResult() map[int64]bool {
	if x != nil {
		return x.FileIdToResult
	}
	return nil
}

func (x *FragResult) GetSuccessFiles() map[int64]*File {
	if x != nil {
		return x.SuccessFiles
	}
	return nil
}

func (x *FragResult) GetFailedFiles() map[int64]*File {
	if x != nil {
		return x.FailedFiles
	}
	return nil
}

func (x *FragResult) GetWaitMigrateFiles() map[int64]*File {
	if x != nil {
		return x.WaitMigrateFiles
	}
	return nil
}

func (x *FragResult) GetFileNum() int64 {
	if x != nil {
		return x.FileNum
	}
	return 0
}

func (x *FragResult) GetSuccessfulNum() int64 {
	if x != nil {
		return x.SuccessfulNum
	}
	return 0
}

func (x *FragResult) GetFailedNum() int64 {
	if x != nil {
		return x.FailedNum
	}
	return 0
}

func (x *FragResult) GetWaitMigrateNum() int64 {
	if x != nil {
		return x.WaitMigrateNum
	}
	return 0
}

func (x *FragResult) GetSize() int64 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *FragResult) GetSuccessfulSize() int64 {
	if x != nil {
		return x.SuccessfulSize
	}
	return 0
}

func (x *FragResult) GetFailedSize() int64 {
	if x != nil {
		return x.FailedSize
	}
	return 0
}

type File struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FullPath     string `protobuf:"bytes,1,opt,name=FullPath,proto3" json:"FullPath,omitempty"`
	Size         int64  `protobuf:"varint,2,opt,name=Size,proto3" json:"Size,omitempty"`
	CurTime      string `protobuf:"bytes,3,opt,name=CurTime,proto3" json:"CurTime,omitempty"`
	FailedReason string `protobuf:"bytes,4,opt,name=FailedReason,proto3" json:"FailedReason,omitempty"`
	UploadId     string `protobuf:"bytes,5,opt,name=UploadId,proto3" json:"UploadId,omitempty"`
}

func (x *File) Reset() {
	*x = File{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_migrate_rpc_lister_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *File) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*File) ProtoMessage() {}

func (x *File) ProtoReflect() protoreflect.Message {
	mi := &file_api_migrate_rpc_lister_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use File.ProtoReflect.Descriptor instead.
func (*File) Descriptor() ([]byte, []int) {
	return file_api_migrate_rpc_lister_proto_rawDescGZIP(), []int{26}
}

func (x *File) GetFullPath() string {
	if x != nil {
		return x.FullPath
	}
	return ""
}

func (x *File) GetSize() int64 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *File) GetCurTime() string {
	if x != nil {
		return x.CurTime
	}
	return ""
}

func (x *File) GetFailedReason() string {
	if x != nil {
		return x.FailedReason
	}
	return ""
}

func (x *File) GetUploadId() string {
	if x != nil {
		return x.UploadId
	}
	return ""
}

var File_api_migrate_rpc_lister_proto protoreflect.FileDescriptor

var file_api_migrate_rpc_lister_proto_rawDesc = []byte{
	0x0a, 0x1c, 0x61, 0x70, 0x69, 0x2f, 0x6d, 0x69, 0x67, 0x72, 0x61, 0x74, 0x65, 0x5f, 0x72, 0x70,
	0x63, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x03,
	0x61, 0x70, 0x69, 0x22, 0x96, 0x01, 0x0a, 0x12, 0x52, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x46,
	0x72, 0x61, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x57, 0x6f,
	0x72, 0x6b, 0x65, 0x72, 0x49, 0x70, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x57, 0x6f,
	0x72, 0x6b, 0x65, 0x72, 0x49, 0x70, 0x12, 0x1e, 0x0a, 0x0a, 0x57, 0x6f, 0x72, 0x6b, 0x65, 0x72,
	0x50, 0x6f, 0x72, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x57, 0x6f, 0x72, 0x6b,
	0x65, 0x72, 0x50, 0x6f, 0x72, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x49, 0x73, 0x53, 0x74, 0x6b, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x49, 0x73, 0x53, 0x74, 0x6b, 0x65, 0x12, 0x16,
	0x0a, 0x06, 0x52, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x52, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x12, 0x14, 0x0a, 0x05, 0x4a, 0x6f, 0x62, 0x49, 0x64, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x4a, 0x6f, 0x62, 0x49, 0x64, 0x22, 0x34, 0x0a, 0x13,
	0x52, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x46, 0x72, 0x61, 0x67, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x1d, 0x0a, 0x04, 0x46, 0x72, 0x61, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x09, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x46, 0x72, 0x61, 0x67, 0x52, 0x04, 0x46, 0x72,
	0x61, 0x67, 0x22, 0x4e, 0x0a, 0x08, 0x53, 0x65, 0x6e, 0x64, 0x46, 0x69, 0x6c, 0x65, 0x12, 0x12,
	0x0a, 0x04, 0x50, 0x61, 0x74, 0x68, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x50, 0x61,
	0x74, 0x68, 0x12, 0x12, 0x0a, 0x04, 0x53, 0x69, 0x7a, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x04, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64,
	0x49, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64,
	0x49, 0x64, 0x22, 0x93, 0x01, 0x0a, 0x04, 0x46, 0x72, 0x61, 0x67, 0x12, 0x16, 0x0a, 0x06, 0x54,
	0x61, 0x73, 0x6b, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x54, 0x61, 0x73,
	0x6b, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x46, 0x72, 0x61, 0x67, 0x49, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x06, 0x46, 0x72, 0x61, 0x67, 0x49, 0x64, 0x12, 0x23, 0x0a, 0x05, 0x46,
	0x69, 0x6c, 0x65, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x53, 0x65, 0x6e, 0x64, 0x46, 0x69, 0x6c, 0x65, 0x52, 0x05, 0x46, 0x69, 0x6c, 0x65, 0x73,
	0x12, 0x1a, 0x0a, 0x08, 0x52, 0x65, 0x74, 0x72, 0x79, 0x4e, 0x75, 0x6d, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x08, 0x52, 0x65, 0x74, 0x72, 0x79, 0x4e, 0x75, 0x6d, 0x12, 0x1a, 0x0a, 0x08,
	0x53, 0x63, 0x61, 0x6e, 0x53, 0x69, 0x7a, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08,
	0x53, 0x63, 0x61, 0x6e, 0x53, 0x69, 0x7a, 0x65, 0x22, 0xb1, 0x01, 0x0a, 0x1d, 0x52, 0x65, 0x63,
	0x65, 0x69, 0x76, 0x65, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x64, 0x4a, 0x6f, 0x62, 0x49,
	0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x57, 0x6f,
	0x72, 0x6b, 0x65, 0x72, 0x49, 0x70, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x57, 0x6f,
	0x72, 0x6b, 0x65, 0x72, 0x49, 0x70, 0x12, 0x1e, 0x0a, 0x0a, 0x57, 0x6f, 0x72, 0x6b, 0x65, 0x72,
	0x50, 0x6f, 0x72, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x57, 0x6f, 0x72, 0x6b,
	0x65, 0x72, 0x50, 0x6f, 0x72, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x49, 0x73, 0x53, 0x74, 0x6b, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x49, 0x73, 0x53, 0x74, 0x6b, 0x65, 0x12, 0x16,
	0x0a, 0x06, 0x52, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x52, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x12, 0x24, 0x0a, 0x0d, 0x43, 0x75, 0x72, 0x72, 0x65, 0x6e,
	0x74, 0x4a, 0x6f, 0x62, 0x49, 0x64, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0d, 0x43,
	0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x4a, 0x6f, 0x62, 0x49, 0x64, 0x73, 0x22, 0xa8, 0x01, 0x0a,
	0x1e, 0x52, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x64,
	0x4a, 0x6f, 0x62, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x4a, 0x0a, 0x07, 0x4e, 0x65, 0x77, 0x4a, 0x6f, 0x62, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x30, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x52, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x45, 0x78,
	0x65, 0x63, 0x75, 0x74, 0x65, 0x64, 0x4a, 0x6f, 0x62, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x4e, 0x65, 0x77, 0x4a, 0x6f, 0x62, 0x73, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x52, 0x07, 0x4e, 0x65, 0x77, 0x4a, 0x6f, 0x62, 0x73, 0x1a, 0x3a, 0x0a, 0x0c, 0x4e,
	0x65, 0x77, 0x4a, 0x6f, 0x62, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b,
	0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0xda, 0x01, 0x0a, 0x10, 0x4e, 0x65, 0x78, 0x74,
	0x53, 0x70, 0x65, 0x65, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1a, 0x0a, 0x08,
	0x57, 0x6f, 0x72, 0x6b, 0x65, 0x72, 0x49, 0x70, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x57, 0x6f, 0x72, 0x6b, 0x65, 0x72, 0x49, 0x70, 0x12, 0x1e, 0x0a, 0x0a, 0x57, 0x6f, 0x72, 0x6b,
	0x65, 0x72, 0x50, 0x6f, 0x72, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x57, 0x6f,
	0x72, 0x6b, 0x65, 0x72, 0x50, 0x6f, 0x72, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x49, 0x73, 0x53, 0x74,
	0x6b, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x49, 0x73, 0x53, 0x74, 0x6b, 0x65,
	0x12, 0x16, 0x0a, 0x06, 0x52, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x52, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x12, 0x26, 0x0a, 0x0e, 0x41, 0x6c, 0x6c, 0x6f,
	0x63, 0x61, 0x74, 0x65, 0x64, 0x53, 0x70, 0x65, 0x65, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x0e, 0x41, 0x6c, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x65, 0x64, 0x53, 0x70, 0x65, 0x65, 0x64,
	0x12, 0x1c, 0x0a, 0x09, 0x52, 0x65, 0x61, 0x6c, 0x53, 0x70, 0x65, 0x65, 0x64, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x09, 0x52, 0x65, 0x61, 0x6c, 0x53, 0x70, 0x65, 0x65, 0x64, 0x12, 0x14,
	0x0a, 0x05, 0x4a, 0x6f, 0x62, 0x49, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x4a,
	0x6f, 0x62, 0x49, 0x64, 0x22, 0x31, 0x0a, 0x11, 0x4e, 0x65, 0x78, 0x74, 0x53, 0x70, 0x65, 0x65,
	0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x4e, 0x65, 0x78,
	0x74, 0x53, 0x70, 0x65, 0x65, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x4e, 0x65,
	0x78, 0x74, 0x53, 0x70, 0x65, 0x65, 0x64, 0x22, 0x9d, 0x06, 0x0a, 0x07, 0x4a, 0x6f, 0x62, 0x49,
	0x6e, 0x66, 0x6f, 0x12, 0x14, 0x0a, 0x05, 0x4a, 0x6f, 0x62, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x4a, 0x6f, 0x62, 0x49, 0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x53, 0x72, 0x63,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x53,
	0x72, 0x63, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x53, 0x72, 0x63,
	0x53, 0x65, 0x63, 0x72, 0x65, 0x74, 0x49, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b,
	0x53, 0x72, 0x63, 0x53, 0x65, 0x63, 0x72, 0x65, 0x74, 0x49, 0x64, 0x12, 0x22, 0x0a, 0x0c, 0x53,
	0x72, 0x63, 0x53, 0x65, 0x63, 0x72, 0x65, 0x74, 0x4b, 0x65, 0x79, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0c, 0x53, 0x72, 0x63, 0x53, 0x65, 0x63, 0x72, 0x65, 0x74, 0x4b, 0x65, 0x79, 0x12,
	0x1c, 0x0a, 0x09, 0x53, 0x72, 0x63, 0x42, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x53, 0x72, 0x63, 0x42, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x12, 0x1c, 0x0a,
	0x09, 0x53, 0x72, 0x63, 0x52, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x53, 0x72, 0x63, 0x52, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x12, 0x20, 0x0a, 0x0b, 0x44,
	0x73, 0x74, 0x53, 0x65, 0x63, 0x72, 0x65, 0x74, 0x49, 0x64, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0b, 0x44, 0x73, 0x74, 0x53, 0x65, 0x63, 0x72, 0x65, 0x74, 0x49, 0x64, 0x12, 0x22, 0x0a,
	0x0c, 0x44, 0x73, 0x74, 0x53, 0x65, 0x63, 0x72, 0x65, 0x74, 0x4b, 0x65, 0x79, 0x18, 0x0a, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0c, 0x44, 0x73, 0x74, 0x53, 0x65, 0x63, 0x72, 0x65, 0x74, 0x4b, 0x65,
	0x79, 0x12, 0x1c, 0x0a, 0x09, 0x44, 0x73, 0x74, 0x42, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x18, 0x0b,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x44, 0x73, 0x74, 0x42, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x12,
	0x1c, 0x0a, 0x09, 0x44, 0x73, 0x74, 0x52, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x18, 0x0c, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x44, 0x73, 0x74, 0x52, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x12, 0x1c, 0x0a,
	0x09, 0x55, 0x73, 0x65, 0x72, 0x41, 0x70, 0x70, 0x49, 0x64, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x09, 0x55, 0x73, 0x65, 0x72, 0x41, 0x70, 0x70, 0x49, 0x64, 0x12, 0x20, 0x0a, 0x0b, 0x53,
	0x72, 0x63, 0x46, 0x69, 0x6c, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0b, 0x53, 0x72, 0x63, 0x46, 0x69, 0x6c, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1e, 0x0a,
	0x0a, 0x53, 0x72, 0x63, 0x46, 0x69, 0x6c, 0x65, 0x55, 0x72, 0x6c, 0x18, 0x0f, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0a, 0x53, 0x72, 0x63, 0x46, 0x69, 0x6c, 0x65, 0x55, 0x72, 0x6c, 0x12, 0x1a, 0x0a,
	0x08, 0x50, 0x61, 0x74, 0x68, 0x43, 0x6f, 0x6e, 0x66, 0x18, 0x10, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x08, 0x50, 0x61, 0x74, 0x68, 0x43, 0x6f, 0x6e, 0x66, 0x12, 0x1a, 0x0a, 0x08, 0x53, 0x61, 0x76,
	0x65, 0x50, 0x61, 0x74, 0x68, 0x18, 0x11, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x53, 0x61, 0x76,
	0x65, 0x50, 0x61, 0x74, 0x68, 0x12, 0x20, 0x0a, 0x0b, 0x53, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65,
	0x54, 0x79, 0x70, 0x65, 0x18, 0x14, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x53, 0x74, 0x6f, 0x72,
	0x61, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x30, 0x0a, 0x13, 0x4d, 0x69, 0x67, 0x72, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x18, 0x15,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x13, 0x4d, 0x69, 0x67, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x48,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x12, 0x28, 0x0a, 0x0f, 0x4d, 0x69, 0x67,
	0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x16, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0f, 0x4d, 0x69, 0x67, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x48, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x12, 0x24, 0x0a, 0x0d, 0x46, 0x69, 0x6c, 0x65, 0x4f, 0x76, 0x65, 0x72, 0x57,
	0x72, 0x69, 0x74, 0x65, 0x18, 0x19, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0d, 0x46, 0x69, 0x6c, 0x65,
	0x4f, 0x76, 0x65, 0x72, 0x57, 0x72, 0x69, 0x74, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x4c, 0x69, 0x6d,
	0x69, 0x74, 0x51, 0x70, 0x73, 0x18, 0x1a, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x4c, 0x69, 0x6d,
	0x69, 0x74, 0x51, 0x70, 0x73, 0x12, 0x1c, 0x0a, 0x09, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x46, 0x6c,
	0x6f, 0x77, 0x18, 0x1b, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x46,
	0x6c, 0x6f, 0x77, 0x12, 0x1e, 0x0a, 0x0a, 0x42, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x54, 0x79, 0x70,
	0x65, 0x18, 0x1c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x42, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x22, 0x0a, 0x0c, 0x4b, 0x6f, 0x64, 0x6f, 0x45, 0x6e, 0x64, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x18, 0x1d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x4b, 0x6f, 0x64, 0x6f, 0x45,
	0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x12, 0x1e, 0x0a, 0x0a, 0x4e, 0x65, 0x77, 0x48, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x73, 0x18, 0x1e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x4e, 0x65, 0x77,
	0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x73, 0x22, 0x30, 0x0a, 0x16, 0x52, 0x65, 0x63, 0x65, 0x69,
	0x76, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x16, 0x0a, 0x06, 0x54, 0x61, 0x73, 0x6b, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x54, 0x61, 0x73, 0x6b, 0x49, 0x64, 0x22, 0x41, 0x0a, 0x17, 0x52, 0x65, 0x63,
	0x65, 0x69, 0x76, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x26, 0x0a, 0x07, 0x4a, 0x6f, 0x62, 0x49, 0x6e, 0x66, 0x6f, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x4a, 0x6f, 0x62, 0x49,
	0x6e, 0x66, 0x6f, 0x52, 0x07, 0x4a, 0x6f, 0x62, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0x37, 0x0a, 0x12,
	0x52, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x57, 0x6f, 0x72, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x21, 0x0a, 0x04, 0x57, 0x6f, 0x72, 0x6b, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x0d, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x57, 0x6f, 0x72, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x52,
	0x04, 0x57, 0x6f, 0x72, 0x6b, 0x22, 0x27, 0x0a, 0x13, 0x52, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65,
	0x57, 0x6f, 0x72, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x10, 0x0a, 0x03,
	0x52, 0x65, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x03, 0x52, 0x65, 0x73, 0x22, 0x17,
	0x0a, 0x15, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x4e, 0x6f, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x4b, 0x0a, 0x18, 0x52, 0x65, 0x63, 0x65, 0x69,
	0x76, 0x65, 0x46, 0x72, 0x61, 0x67, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x2f, 0x0a, 0x0a, 0x46, 0x72, 0x61, 0x67, 0x52, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x46, 0x72,
	0x61, 0x67, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x0a, 0x46, 0x72, 0x61, 0x67, 0x52, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x22, 0x2d, 0x0a, 0x19, 0x52, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x46,
	0x72, 0x61, 0x67, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x10, 0x0a, 0x03, 0x52, 0x65, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x03,
	0x52, 0x65, 0x73, 0x22, 0x31, 0x0a, 0x17, 0x52, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x54, 0x61,
	0x73, 0x6b, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x16,
	0x0a, 0x06, 0x54, 0x61, 0x73, 0x6b, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x54, 0x61, 0x73, 0x6b, 0x49, 0x64, 0x22, 0x64, 0x0a, 0x18, 0x52, 0x65, 0x63, 0x65, 0x69, 0x76,
	0x65, 0x54, 0x61, 0x73, 0x6b, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x51, 0x70, 0x73, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x51, 0x70, 0x73, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x12, 0x2c,
	0x0a, 0x09, 0x54, 0x61, 0x73, 0x6b, 0x53, 0x74, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x0e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x54, 0x61, 0x73, 0x6b, 0x53, 0x74, 0x61, 0x74,
	0x65, 0x52, 0x09, 0x54, 0x61, 0x73, 0x6b, 0x53, 0x74, 0x61, 0x74, 0x65, 0x22, 0x5a, 0x0a, 0x1c,
	0x52, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x51, 0x70, 0x73, 0x43, 0x6f,
	0x6e, 0x73, 0x75, 0x6d, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x16, 0x0a, 0x06,
	0x54, 0x61, 0x73, 0x6b, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x54, 0x61,
	0x73, 0x6b, 0x49, 0x64, 0x12, 0x22, 0x0a, 0x0c, 0x4e, 0x65, 0x65, 0x64, 0x51, 0x70, 0x73, 0x54,
	0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x4e, 0x65, 0x65, 0x64,
	0x51, 0x70, 0x73, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x22, 0x43, 0x0a, 0x1d, 0x52, 0x65, 0x63, 0x65,
	0x69, 0x76, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x51, 0x70, 0x73, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d,
	0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x22, 0x0a, 0x0c, 0x54, 0x61, 0x73,
	0x6b, 0x51, 0x70, 0x73, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x0c, 0x54, 0x61, 0x73, 0x6b, 0x51, 0x70, 0x73, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x22, 0x5d, 0x0a,
	0x1d, 0x52, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x46, 0x6c, 0x6f, 0x77,
	0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x16,
	0x0a, 0x06, 0x54, 0x61, 0x73, 0x6b, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x54, 0x61, 0x73, 0x6b, 0x49, 0x64, 0x12, 0x24, 0x0a, 0x0d, 0x4e, 0x65, 0x65, 0x64, 0x46, 0x6c,
	0x6f, 0x77, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0d, 0x4e,
	0x65, 0x65, 0x64, 0x46, 0x6c, 0x6f, 0x77, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x22, 0x46, 0x0a, 0x1e,
	0x52, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x46, 0x6c, 0x6f, 0x77, 0x43,
	0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x24,
	0x0a, 0x0d, 0x54, 0x61, 0x73, 0x6b, 0x46, 0x6c, 0x6f, 0x77, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0d, 0x54, 0x61, 0x73, 0x6b, 0x46, 0x6c, 0x6f, 0x77, 0x4c,
	0x69, 0x6d, 0x69, 0x74, 0x22, 0xd8, 0x01, 0x0a, 0x08, 0x57, 0x6f, 0x72, 0x6b, 0x49, 0x6e, 0x66,
	0x6f, 0x12, 0x1a, 0x0a, 0x08, 0x57, 0x6f, 0x72, 0x6b, 0x65, 0x72, 0x49, 0x70, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x57, 0x6f, 0x72, 0x6b, 0x65, 0x72, 0x49, 0x70, 0x12, 0x1e, 0x0a,
	0x0a, 0x57, 0x6f, 0x72, 0x6b, 0x65, 0x72, 0x50, 0x6f, 0x72, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x0a, 0x57, 0x6f, 0x72, 0x6b, 0x65, 0x72, 0x50, 0x6f, 0x72, 0x74, 0x12, 0x2f, 0x0a,
	0x08, 0x42, 0x61, 0x73, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x13, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x57, 0x6f, 0x72, 0x6b, 0x65, 0x72, 0x42, 0x61, 0x73, 0x65,
	0x49, 0x6e, 0x66, 0x6f, 0x52, 0x08, 0x42, 0x61, 0x73, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x2f,
	0x0a, 0x08, 0x54, 0x61, 0x73, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x13, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x57, 0x6f, 0x72, 0x6b, 0x65, 0x72, 0x54, 0x61, 0x73,
	0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x08, 0x54, 0x61, 0x73, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x12,
	0x16, 0x0a, 0x06, 0x49, 0x73, 0x53, 0x74, 0x6b, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x06, 0x49, 0x73, 0x53, 0x74, 0x6b, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x52, 0x65, 0x67, 0x69, 0x6f,
	0x6e, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x52, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x22,
	0x58, 0x0a, 0x0e, 0x57, 0x6f, 0x72, 0x6b, 0x65, 0x72, 0x42, 0x61, 0x73, 0x65, 0x49, 0x6e, 0x66,
	0x6f, 0x12, 0x10, 0x0a, 0x03, 0x43, 0x70, 0x75, 0x18, 0x08, 0x20, 0x01, 0x28, 0x02, 0x52, 0x03,
	0x43, 0x70, 0x75, 0x12, 0x16, 0x0a, 0x06, 0x4d, 0x65, 0x6d, 0x6f, 0x72, 0x79, 0x18, 0x09, 0x20,
	0x01, 0x28, 0x02, 0x52, 0x06, 0x4d, 0x65, 0x6d, 0x6f, 0x72, 0x79, 0x12, 0x1c, 0x0a, 0x09, 0x54,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09,
	0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x22, 0xaa, 0x02, 0x0a, 0x0e, 0x57, 0x6f,
	0x72, 0x6b, 0x65, 0x72, 0x54, 0x61, 0x73, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x16, 0x0a, 0x06,
	0x54, 0x61, 0x73, 0x6b, 0x49, 0x64, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x54, 0x61,
	0x73, 0x6b, 0x49, 0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x53, 0x70, 0x65, 0x65, 0x64, 0x49, 0x6e, 0x55,
	0x73, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x53, 0x70, 0x65, 0x65, 0x64, 0x49,
	0x6e, 0x55, 0x73, 0x65, 0x12, 0x26, 0x0a, 0x0e, 0x53, 0x70, 0x65, 0x65, 0x64, 0x41, 0x6c, 0x6c,
	0x6f, 0x63, 0x61, 0x74, 0x65, 0x64, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0e, 0x53, 0x70,
	0x65, 0x65, 0x64, 0x41, 0x6c, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x65, 0x64, 0x12, 0x1a, 0x0a, 0x08,
	0x46, 0x61, 0x69, 0x6c, 0x52, 0x61, 0x74, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x02, 0x52, 0x08,
	0x46, 0x61, 0x69, 0x6c, 0x52, 0x61, 0x74, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x54, 0x69, 0x6d, 0x65,
	0x6f, 0x75, 0x74, 0x52, 0x61, 0x74, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0b, 0x54,
	0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74, 0x52, 0x61, 0x74, 0x65, 0x12, 0x2c, 0x0a, 0x11, 0x43, 0x6f,
	0x6e, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x46, 0x69, 0x6c, 0x65, 0x4e, 0x75, 0x6d, 0x18,
	0x0e, 0x20, 0x01, 0x28, 0x03, 0x52, 0x11, 0x43, 0x6f, 0x6e, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e,
	0x74, 0x46, 0x69, 0x6c, 0x65, 0x4e, 0x75, 0x6d, 0x12, 0x2e, 0x0a, 0x12, 0x43, 0x6f, 0x6e, 0x63,
	0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x46, 0x69, 0x6c, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x18, 0x0f,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x12, 0x43, 0x6f, 0x6e, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74,
	0x46, 0x69, 0x6c, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x46, 0x72, 0x61, 0x67,
	0x49, 0x64, 0x53, 0x65, 0x74, 0x18, 0x10, 0x20, 0x03, 0x28, 0x03, 0x52, 0x09, 0x46, 0x72, 0x61,
	0x67, 0x49, 0x64, 0x53, 0x65, 0x74, 0x22, 0x9f, 0x08, 0x0a, 0x0a, 0x46, 0x72, 0x61, 0x67, 0x52,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x54, 0x61, 0x73, 0x6b, 0x49, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x54, 0x61, 0x73, 0x6b, 0x49, 0x64, 0x12, 0x16, 0x0a,
	0x06, 0x46, 0x72, 0x61, 0x67, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x46,
	0x72, 0x61, 0x67, 0x49, 0x64, 0x12, 0x34, 0x0a, 0x15, 0x47, 0x65, 0x6e, 0x46, 0x72, 0x61, 0x67,
	0x4c, 0x69, 0x73, 0x74, 0x65, 0x72, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x55, 0x72, 0x6c, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x15, 0x47, 0x65, 0x6e, 0x46, 0x72, 0x61, 0x67, 0x4c, 0x69, 0x73,
	0x74, 0x65, 0x72, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x55, 0x72, 0x6c, 0x12, 0x1a, 0x0a, 0x08, 0x57,
	0x6f, 0x72, 0x6b, 0x65, 0x72, 0x49, 0x70, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x57,
	0x6f, 0x72, 0x6b, 0x65, 0x72, 0x49, 0x70, 0x12, 0x1e, 0x0a, 0x0a, 0x57, 0x6f, 0x72, 0x6b, 0x65,
	0x72, 0x50, 0x6f, 0x72, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x57, 0x6f, 0x72,
	0x6b, 0x65, 0x72, 0x50, 0x6f, 0x72, 0x74, 0x12, 0x1c, 0x0a, 0x09, 0x57, 0x6f, 0x72, 0x6b, 0x65,
	0x72, 0x55, 0x72, 0x6c, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x57, 0x6f, 0x72, 0x6b,
	0x65, 0x72, 0x55, 0x72, 0x6c, 0x12, 0x1a, 0x0a, 0x08, 0x52, 0x65, 0x74, 0x72, 0x79, 0x4e, 0x75,
	0x6d, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x52, 0x65, 0x74, 0x72, 0x79, 0x4e, 0x75,
	0x6d, 0x12, 0x4b, 0x0a, 0x0e, 0x46, 0x69, 0x6c, 0x65, 0x49, 0x64, 0x54, 0x6f, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x18, 0x08, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x46, 0x72, 0x61, 0x67, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x2e, 0x46, 0x69, 0x6c, 0x65, 0x49,
	0x64, 0x54, 0x6f, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0e,
	0x46, 0x69, 0x6c, 0x65, 0x49, 0x64, 0x54, 0x6f, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x45,
	0x0a, 0x0c, 0x53, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x46, 0x69, 0x6c, 0x65, 0x73, 0x18, 0x09,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x46, 0x72, 0x61, 0x67, 0x52,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x2e, 0x53, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x46, 0x69, 0x6c,
	0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0c, 0x53, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73,
	0x46, 0x69, 0x6c, 0x65, 0x73, 0x12, 0x42, 0x0a, 0x0b, 0x46, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x46,
	0x69, 0x6c, 0x65, 0x73, 0x18, 0x0a, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x46, 0x72, 0x61, 0x67, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x2e, 0x46, 0x61, 0x69, 0x6c,
	0x65, 0x64, 0x46, 0x69, 0x6c, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0b, 0x46, 0x61,
	0x69, 0x6c, 0x65, 0x64, 0x46, 0x69, 0x6c, 0x65, 0x73, 0x12, 0x51, 0x0a, 0x10, 0x57, 0x61, 0x69,
	0x74, 0x4d, 0x69, 0x67, 0x72, 0x61, 0x74, 0x65, 0x46, 0x69, 0x6c, 0x65, 0x73, 0x18, 0x0b, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x46, 0x72, 0x61, 0x67, 0x52, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x2e, 0x57, 0x61, 0x69, 0x74, 0x4d, 0x69, 0x67, 0x72, 0x61, 0x74, 0x65,
	0x46, 0x69, 0x6c, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x10, 0x57, 0x61, 0x69, 0x74,
	0x4d, 0x69, 0x67, 0x72, 0x61, 0x74, 0x65, 0x46, 0x69, 0x6c, 0x65, 0x73, 0x12, 0x18, 0x0a, 0x07,
	0x46, 0x69, 0x6c, 0x65, 0x4e, 0x75, 0x6d, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x46,
	0x69, 0x6c, 0x65, 0x4e, 0x75, 0x6d, 0x12, 0x24, 0x0a, 0x0d, 0x53, 0x75, 0x63, 0x63, 0x65, 0x73,
	0x73, 0x66, 0x75, 0x6c, 0x4e, 0x75, 0x6d, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0d, 0x53,
	0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x66, 0x75, 0x6c, 0x4e, 0x75, 0x6d, 0x12, 0x1c, 0x0a, 0x09,
	0x46, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x4e, 0x75, 0x6d, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x09, 0x46, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x4e, 0x75, 0x6d, 0x12, 0x26, 0x0a, 0x0e, 0x57, 0x61,
	0x69, 0x74, 0x4d, 0x69, 0x67, 0x72, 0x61, 0x74, 0x65, 0x4e, 0x75, 0x6d, 0x18, 0x0f, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x0e, 0x57, 0x61, 0x69, 0x74, 0x4d, 0x69, 0x67, 0x72, 0x61, 0x74, 0x65, 0x4e,
	0x75, 0x6d, 0x12, 0x12, 0x0a, 0x04, 0x53, 0x69, 0x7a, 0x65, 0x18, 0x10, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x04, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x26, 0x0a, 0x0e, 0x53, 0x75, 0x63, 0x63, 0x65, 0x73,
	0x73, 0x66, 0x75, 0x6c, 0x53, 0x69, 0x7a, 0x65, 0x18, 0x11, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0e,
	0x53, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x66, 0x75, 0x6c, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x1e,
	0x0a, 0x0a, 0x46, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x53, 0x69, 0x7a, 0x65, 0x18, 0x12, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x0a, 0x46, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x53, 0x69, 0x7a, 0x65, 0x1a, 0x41,
	0x0a, 0x13, 0x46, 0x69, 0x6c, 0x65, 0x49, 0x64, 0x54, 0x6f, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38,
	0x01, 0x1a, 0x4a, 0x0a, 0x11, 0x53, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x46, 0x69, 0x6c, 0x65,
	0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x1f, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x09, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x46, 0x69,
	0x6c, 0x65, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x49, 0x0a,
	0x10, 0x46, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x46, 0x69, 0x6c, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x03,
	0x6b, 0x65, 0x79, 0x12, 0x1f, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x09, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x46, 0x69, 0x6c, 0x65, 0x52, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x4e, 0x0a, 0x15, 0x57, 0x61, 0x69, 0x74,
	0x4d, 0x69, 0x67, 0x72, 0x61, 0x74, 0x65, 0x46, 0x69, 0x6c, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x03,
	0x6b, 0x65, 0x79, 0x12, 0x1f, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x09, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x46, 0x69, 0x6c, 0x65, 0x52, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x90, 0x01, 0x0a, 0x04, 0x46, 0x69, 0x6c,
	0x65, 0x12, 0x1a, 0x0a, 0x08, 0x46, 0x75, 0x6c, 0x6c, 0x50, 0x61, 0x74, 0x68, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x46, 0x75, 0x6c, 0x6c, 0x50, 0x61, 0x74, 0x68, 0x12, 0x12, 0x0a,
	0x04, 0x53, 0x69, 0x7a, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x04, 0x53, 0x69, 0x7a,
	0x65, 0x12, 0x18, 0x0a, 0x07, 0x43, 0x75, 0x72, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x43, 0x75, 0x72, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x22, 0x0a, 0x0c, 0x46,
	0x61, 0x69, 0x6c, 0x65, 0x64, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0c, 0x46, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12,
	0x1a, 0x0a, 0x08, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x49, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x49, 0x64, 0x2a, 0x84, 0x02, 0x0a, 0x09,
	0x54, 0x61, 0x73, 0x6b, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x12, 0x0a, 0x0e, 0x41, 0x42, 0x53,
	0x54, 0x41, 0x49, 0x4e, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x10, 0x00, 0x12, 0x0b, 0x0a,
	0x07, 0x43, 0x52, 0x45, 0x41, 0x54, 0x45, 0x44, 0x10, 0x02, 0x12, 0x16, 0x0a, 0x12, 0x43, 0x48,
	0x45, 0x43, 0x4b, 0x45, 0x44, 0x5f, 0x53, 0x55, 0x43, 0x43, 0x45, 0x53, 0x53, 0x46, 0x55, 0x4c,
	0x10, 0x04, 0x12, 0x0b, 0x0a, 0x07, 0x52, 0x55, 0x4e, 0x4e, 0x49, 0x4e, 0x47, 0x10, 0x07, 0x12,
	0x0b, 0x0a, 0x07, 0x53, 0x55, 0x43, 0x43, 0x45, 0x53, 0x53, 0x10, 0x09, 0x12, 0x0b, 0x0a, 0x07,
	0x46, 0x41, 0x49, 0x4c, 0x55, 0x52, 0x45, 0x10, 0x0a, 0x12, 0x0c, 0x0a, 0x08, 0x44, 0x45, 0x4c,
	0x45, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x0d, 0x12, 0x08, 0x0a, 0x04, 0x53, 0x54, 0x4f, 0x50, 0x10,
	0x65, 0x12, 0x09, 0x0a, 0x05, 0x52, 0x45, 0x54, 0x52, 0x59, 0x10, 0x66, 0x12, 0x0c, 0x0a, 0x07,
	0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0xf4, 0x03, 0x12, 0x19, 0x0a, 0x14, 0x55, 0x4e,
	0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x5f, 0x53, 0x45, 0x52, 0x56, 0x45, 0x52, 0x5f, 0x53, 0x54, 0x41,
	0x54, 0x45, 0x10, 0xe9, 0x07, 0x12, 0x18, 0x0a, 0x13, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e,
	0x5f, 0x41, 0x47, 0x45, 0x4e, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x45, 0x10, 0xea, 0x07, 0x12,
	0x16, 0x0a, 0x11, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x5f, 0x52, 0x50, 0x43, 0x5f, 0x53,
	0x54, 0x41, 0x54, 0x45, 0x10, 0xeb, 0x07, 0x12, 0x19, 0x0a, 0x14, 0x55, 0x4e, 0x4b, 0x4e, 0x4f,
	0x57, 0x4e, 0x5f, 0x57, 0x4f, 0x52, 0x4b, 0x45, 0x52, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x45, 0x10,
	0xec, 0x07, 0x32, 0x87, 0x06, 0x0a, 0x10, 0x4d, 0x69, 0x67, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x42, 0x0a, 0x0b, 0x52, 0x65, 0x63, 0x65, 0x69,
	0x76, 0x65, 0x57, 0x6f, 0x72, 0x6b, 0x12, 0x17, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x52, 0x65, 0x63,
	0x65, 0x69, 0x76, 0x65, 0x57, 0x6f, 0x72, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x52, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x57, 0x6f, 0x72,
	0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x54, 0x0a, 0x11, 0x52,
	0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x46, 0x72, 0x61, 0x67, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x12, 0x1d, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x52, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x46, 0x72,
	0x61, 0x67, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x1e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x52, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x46, 0x72, 0x61,
	0x67, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22,
	0x00, 0x12, 0x51, 0x0a, 0x10, 0x52, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x54, 0x61, 0x73, 0x6b,
	0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x1c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x52, 0x65, 0x63, 0x65,
	0x69, 0x76, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x1d, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x52, 0x65, 0x63, 0x65, 0x69, 0x76,
	0x65, 0x54, 0x61, 0x73, 0x6b, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x22, 0x00, 0x12, 0x60, 0x0a, 0x15, 0x52, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x54,
	0x61, 0x73, 0x6b, 0x51, 0x70, 0x73, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x12, 0x21, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x52, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x51,
	0x70, 0x73, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x22, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x52, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x54, 0x61,
	0x73, 0x6b, 0x51, 0x70, 0x73, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x63, 0x0a, 0x16, 0x52, 0x65, 0x63, 0x65, 0x69, 0x76,
	0x65, 0x54, 0x61, 0x73, 0x6b, 0x46, 0x6c, 0x6f, 0x77, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65,
	0x12, 0x22, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x52, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x54, 0x61,
	0x73, 0x6b, 0x46, 0x6c, 0x6f, 0x77, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x23, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x52, 0x65, 0x63, 0x65, 0x69,
	0x76, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x46, 0x6c, 0x6f, 0x77, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d,
	0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x4e, 0x0a, 0x0f, 0x52,
	0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1b,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x52, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x54, 0x61, 0x73, 0x6b,
	0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1c, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x52, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x49, 0x6e, 0x66,
	0x6f, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x46, 0x0a, 0x13, 0x52,
	0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x4a, 0x6f, 0x62, 0x4e, 0x65, 0x78, 0x74, 0x53, 0x70, 0x65,
	0x65, 0x64, 0x12, 0x15, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x4e, 0x65, 0x78, 0x74, 0x53, 0x70, 0x65,
	0x65, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x4e, 0x65, 0x78, 0x74, 0x53, 0x70, 0x65, 0x65, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x22, 0x00, 0x12, 0x63, 0x0a, 0x16, 0x52, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x45, 0x78,
	0x65, 0x63, 0x75, 0x74, 0x65, 0x64, 0x4a, 0x6f, 0x62, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x22, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x52, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x45, 0x78, 0x65, 0x63, 0x75,
	0x74, 0x65, 0x64, 0x4a, 0x6f, 0x62, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x23, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x52, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x45,
	0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x64, 0x4a, 0x6f, 0x62, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x42, 0x0a, 0x0b, 0x52, 0x65, 0x63, 0x65,
	0x69, 0x76, 0x65, 0x46, 0x72, 0x61, 0x67, 0x12, 0x17, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x52, 0x65,
	0x63, 0x65, 0x69, 0x76, 0x65, 0x46, 0x72, 0x61, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x52, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x46, 0x72,
	0x61, 0x67, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x42, 0x0b, 0x5a, 0x09,
	0x2e, 0x2f, 0x61, 0x70, 0x69, 0x3b, 0x61, 0x70, 0x69, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x33,
}

var (
	file_api_migrate_rpc_lister_proto_rawDescOnce sync.Once
	file_api_migrate_rpc_lister_proto_rawDescData = file_api_migrate_rpc_lister_proto_rawDesc
)

func file_api_migrate_rpc_lister_proto_rawDescGZIP() []byte {
	file_api_migrate_rpc_lister_proto_rawDescOnce.Do(func() {
		file_api_migrate_rpc_lister_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_migrate_rpc_lister_proto_rawDescData)
	})
	return file_api_migrate_rpc_lister_proto_rawDescData
}

var file_api_migrate_rpc_lister_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_api_migrate_rpc_lister_proto_msgTypes = make([]protoimpl.MessageInfo, 32)
var file_api_migrate_rpc_lister_proto_goTypes = []interface{}{
	(TaskState)(0),                         // 0: api.TaskState
	(*ReceiveFragRequest)(nil),             // 1: api.ReceiveFragRequest
	(*ReceiveFragResponse)(nil),            // 2: api.ReceiveFragResponse
	(*SendFile)(nil),                       // 3: api.SendFile
	(*Frag)(nil),                           // 4: api.Frag
	(*ReceiveExecutedJobInfoRequest)(nil),  // 5: api.ReceiveExecutedJobInfoRequest
	(*ReceiveExecutedJobInfoResponse)(nil), // 6: api.ReceiveExecutedJobInfoResponse
	(*NextSpeedRequest)(nil),               // 7: api.NextSpeedRequest
	(*NextSpeedResponse)(nil),              // 8: api.NextSpeedResponse
	(*JobInfo)(nil),                        // 9: api.JobInfo
	(*ReceiveTaskInfoRequest)(nil),         // 10: api.ReceiveTaskInfoRequest
	(*ReceiveTaskInfoResponse)(nil),        // 11: api.ReceiveTaskInfoResponse
	(*ReceiveWorkRequest)(nil),             // 12: api.ReceiveWorkRequest
	(*ReceiveWorkResponse)(nil),            // 13: api.ReceiveWorkResponse
	(*CommonNoReplyResponse)(nil),          // 14: api.CommonNoReplyResponse
	(*ReceiveFragResultRequest)(nil),       // 15: api.ReceiveFragResultRequest
	(*ReceiveFragResultResponse)(nil),      // 16: api.ReceiveFragResultResponse
	(*ReceiveTaskStateRequest)(nil),        // 17: api.ReceiveTaskStateRequest
	(*ReceiveTaskStateResponse)(nil),       // 18: api.ReceiveTaskStateResponse
	(*ReceiveTaskQpsConsumeRequest)(nil),   // 19: api.ReceiveTaskQpsConsumeRequest
	(*ReceiveTaskQpsConsumeResponse)(nil),  // 20: api.ReceiveTaskQpsConsumeResponse
	(*ReceiveTaskFlowConsumeRequest)(nil),  // 21: api.ReceiveTaskFlowConsumeRequest
	(*ReceiveTaskFlowConsumeResponse)(nil), // 22: api.ReceiveTaskFlowConsumeResponse
	(*WorkInfo)(nil),                       // 23: api.WorkInfo
	(*WorkerBaseInfo)(nil),                 // 24: api.WorkerBaseInfo
	(*WorkerTaskInfo)(nil),                 // 25: api.WorkerTaskInfo
	(*FragResult)(nil),                     // 26: api.FragResult
	(*File)(nil),                           // 27: api.File
	nil,                                    // 28: api.ReceiveExecutedJobInfoResponse.NewJobsEntry
	nil,                                    // 29: api.FragResult.FileIdToResultEntry
	nil,                                    // 30: api.FragResult.SuccessFilesEntry
	nil,                                    // 31: api.FragResult.FailedFilesEntry
	nil,                                    // 32: api.FragResult.WaitMigrateFilesEntry
}
var file_api_migrate_rpc_lister_proto_depIdxs = []int32{
	4,  // 0: api.ReceiveFragResponse.Frag:type_name -> api.Frag
	3,  // 1: api.Frag.Files:type_name -> api.SendFile
	28, // 2: api.ReceiveExecutedJobInfoResponse.NewJobs:type_name -> api.ReceiveExecutedJobInfoResponse.NewJobsEntry
	9,  // 3: api.ReceiveTaskInfoResponse.JobInfo:type_name -> api.JobInfo
	23, // 4: api.ReceiveWorkRequest.Work:type_name -> api.WorkInfo
	26, // 5: api.ReceiveFragResultRequest.FragResult:type_name -> api.FragResult
	0,  // 6: api.ReceiveTaskStateResponse.TaskState:type_name -> api.TaskState
	24, // 7: api.WorkInfo.BaseInfo:type_name -> api.WorkerBaseInfo
	25, // 8: api.WorkInfo.TaskInfo:type_name -> api.WorkerTaskInfo
	29, // 9: api.FragResult.FileIdToResult:type_name -> api.FragResult.FileIdToResultEntry
	30, // 10: api.FragResult.SuccessFiles:type_name -> api.FragResult.SuccessFilesEntry
	31, // 11: api.FragResult.FailedFiles:type_name -> api.FragResult.FailedFilesEntry
	32, // 12: api.FragResult.WaitMigrateFiles:type_name -> api.FragResult.WaitMigrateFilesEntry
	27, // 13: api.FragResult.SuccessFilesEntry.value:type_name -> api.File
	27, // 14: api.FragResult.FailedFilesEntry.value:type_name -> api.File
	27, // 15: api.FragResult.WaitMigrateFilesEntry.value:type_name -> api.File
	12, // 16: api.MigrationService.ReceiveWork:input_type -> api.ReceiveWorkRequest
	15, // 17: api.MigrationService.ReceiveFragResult:input_type -> api.ReceiveFragResultRequest
	17, // 18: api.MigrationService.ReceiveTaskState:input_type -> api.ReceiveTaskStateRequest
	19, // 19: api.MigrationService.ReceiveTaskQpsConsume:input_type -> api.ReceiveTaskQpsConsumeRequest
	21, // 20: api.MigrationService.ReceiveTaskFlowConsume:input_type -> api.ReceiveTaskFlowConsumeRequest
	10, // 21: api.MigrationService.ReceiveTaskInfo:input_type -> api.ReceiveTaskInfoRequest
	7,  // 22: api.MigrationService.ReceiveJobNextSpeed:input_type -> api.NextSpeedRequest
	5,  // 23: api.MigrationService.ReceiveExecutedJobInfo:input_type -> api.ReceiveExecutedJobInfoRequest
	1,  // 24: api.MigrationService.ReceiveFrag:input_type -> api.ReceiveFragRequest
	13, // 25: api.MigrationService.ReceiveWork:output_type -> api.ReceiveWorkResponse
	16, // 26: api.MigrationService.ReceiveFragResult:output_type -> api.ReceiveFragResultResponse
	18, // 27: api.MigrationService.ReceiveTaskState:output_type -> api.ReceiveTaskStateResponse
	20, // 28: api.MigrationService.ReceiveTaskQpsConsume:output_type -> api.ReceiveTaskQpsConsumeResponse
	22, // 29: api.MigrationService.ReceiveTaskFlowConsume:output_type -> api.ReceiveTaskFlowConsumeResponse
	11, // 30: api.MigrationService.ReceiveTaskInfo:output_type -> api.ReceiveTaskInfoResponse
	8,  // 31: api.MigrationService.ReceiveJobNextSpeed:output_type -> api.NextSpeedResponse
	6,  // 32: api.MigrationService.ReceiveExecutedJobInfo:output_type -> api.ReceiveExecutedJobInfoResponse
	2,  // 33: api.MigrationService.ReceiveFrag:output_type -> api.ReceiveFragResponse
	25, // [25:34] is the sub-list for method output_type
	16, // [16:25] is the sub-list for method input_type
	16, // [16:16] is the sub-list for extension type_name
	16, // [16:16] is the sub-list for extension extendee
	0,  // [0:16] is the sub-list for field type_name
}

func init() { file_api_migrate_rpc_lister_proto_init() }
func file_api_migrate_rpc_lister_proto_init() {
	if File_api_migrate_rpc_lister_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_migrate_rpc_lister_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReceiveFragRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_migrate_rpc_lister_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReceiveFragResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_migrate_rpc_lister_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SendFile); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_migrate_rpc_lister_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Frag); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_migrate_rpc_lister_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReceiveExecutedJobInfoRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_migrate_rpc_lister_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReceiveExecutedJobInfoResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_migrate_rpc_lister_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NextSpeedRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_migrate_rpc_lister_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NextSpeedResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_migrate_rpc_lister_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*JobInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_migrate_rpc_lister_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReceiveTaskInfoRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_migrate_rpc_lister_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReceiveTaskInfoResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_migrate_rpc_lister_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReceiveWorkRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_migrate_rpc_lister_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReceiveWorkResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_migrate_rpc_lister_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CommonNoReplyResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_migrate_rpc_lister_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReceiveFragResultRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_migrate_rpc_lister_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReceiveFragResultResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_migrate_rpc_lister_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReceiveTaskStateRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_migrate_rpc_lister_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReceiveTaskStateResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_migrate_rpc_lister_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReceiveTaskQpsConsumeRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_migrate_rpc_lister_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReceiveTaskQpsConsumeResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_migrate_rpc_lister_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReceiveTaskFlowConsumeRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_migrate_rpc_lister_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReceiveTaskFlowConsumeResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_migrate_rpc_lister_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WorkInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_migrate_rpc_lister_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WorkerBaseInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_migrate_rpc_lister_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WorkerTaskInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_migrate_rpc_lister_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FragResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_migrate_rpc_lister_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*File); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_migrate_rpc_lister_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   32,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_migrate_rpc_lister_proto_goTypes,
		DependencyIndexes: file_api_migrate_rpc_lister_proto_depIdxs,
		EnumInfos:         file_api_migrate_rpc_lister_proto_enumTypes,
		MessageInfos:      file_api_migrate_rpc_lister_proto_msgTypes,
	}.Build()
	File_api_migrate_rpc_lister_proto = out.File
	file_api_migrate_rpc_lister_proto_rawDesc = nil
	file_api_migrate_rpc_lister_proto_goTypes = nil
	file_api_migrate_rpc_lister_proto_depIdxs = nil
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConnInterface

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion6

// MigrationServiceClient is the client API for MigrationService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type MigrationServiceClient interface {
	//  接收worker
	//  将worker信息更新到WorkerGroup
	//  将WorkerGroup发到各TaskLister（此步由WorkerGroup的变量共享完成）
	//  将属于各任务的分片心跳发到各TaskLister（用于TaskLister重发超时分片）
	ReceiveWork(ctx context.Context, in *ReceiveWorkRequest, opts ...grpc.CallOption) (*ReceiveWorkResponse, error)
	//  接收分片迁移结果
	//  将属于各任务的分片结束信息发到各TaskLister
	//  param frag_result_str:
	ReceiveFragResult(ctx context.Context, in *ReceiveFragResultRequest, opts ...grpc.CallOption) (*ReceiveFragResultResponse, error)
	//  接收任务状态、qps限制的查询
	ReceiveTaskState(ctx context.Context, in *ReceiveTaskStateRequest, opts ...grpc.CallOption) (*ReceiveTaskStateResponse, error)
	// 接收qps token的消费.
	ReceiveTaskQpsConsume(ctx context.Context, in *ReceiveTaskQpsConsumeRequest, opts ...grpc.CallOption) (*ReceiveTaskQpsConsumeResponse, error)
	// 接收flow token的消费
	ReceiveTaskFlowConsume(ctx context.Context, in *ReceiveTaskFlowConsumeRequest, opts ...grpc.CallOption) (*ReceiveTaskFlowConsumeResponse, error)
	//worker向lister获取任务基础信息
	ReceiveTaskInfo(ctx context.Context, in *ReceiveTaskInfoRequest, opts ...grpc.CallOption) (*ReceiveTaskInfoResponse, error)
	// worker查询任务可用速度
	ReceiveJobNextSpeed(ctx context.Context, in *NextSpeedRequest, opts ...grpc.CallOption) (*NextSpeedResponse, error)
	// worker 定时查询可执行任务，用于后面获取任务资源分配
	ReceiveExecutedJobInfo(ctx context.Context, in *ReceiveExecutedJobInfoRequest, opts ...grpc.CallOption) (*ReceiveExecutedJobInfoResponse, error)
	// rpc consume frag
	ReceiveFrag(ctx context.Context, in *ReceiveFragRequest, opts ...grpc.CallOption) (*ReceiveFragResponse, error)
}

type migrationServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewMigrationServiceClient(cc grpc.ClientConnInterface) MigrationServiceClient {
	return &migrationServiceClient{cc}
}

func (c *migrationServiceClient) ReceiveWork(ctx context.Context, in *ReceiveWorkRequest, opts ...grpc.CallOption) (*ReceiveWorkResponse, error) {
	out := new(ReceiveWorkResponse)
	err := c.cc.Invoke(ctx, "/api.MigrationService/ReceiveWork", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *migrationServiceClient) ReceiveFragResult(ctx context.Context, in *ReceiveFragResultRequest, opts ...grpc.CallOption) (*ReceiveFragResultResponse, error) {
	out := new(ReceiveFragResultResponse)
	err := c.cc.Invoke(ctx, "/api.MigrationService/ReceiveFragResult", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *migrationServiceClient) ReceiveTaskState(ctx context.Context, in *ReceiveTaskStateRequest, opts ...grpc.CallOption) (*ReceiveTaskStateResponse, error) {
	out := new(ReceiveTaskStateResponse)
	err := c.cc.Invoke(ctx, "/api.MigrationService/ReceiveTaskState", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *migrationServiceClient) ReceiveTaskQpsConsume(ctx context.Context, in *ReceiveTaskQpsConsumeRequest, opts ...grpc.CallOption) (*ReceiveTaskQpsConsumeResponse, error) {
	out := new(ReceiveTaskQpsConsumeResponse)
	err := c.cc.Invoke(ctx, "/api.MigrationService/ReceiveTaskQpsConsume", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *migrationServiceClient) ReceiveTaskFlowConsume(ctx context.Context, in *ReceiveTaskFlowConsumeRequest, opts ...grpc.CallOption) (*ReceiveTaskFlowConsumeResponse, error) {
	out := new(ReceiveTaskFlowConsumeResponse)
	err := c.cc.Invoke(ctx, "/api.MigrationService/ReceiveTaskFlowConsume", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *migrationServiceClient) ReceiveTaskInfo(ctx context.Context, in *ReceiveTaskInfoRequest, opts ...grpc.CallOption) (*ReceiveTaskInfoResponse, error) {
	out := new(ReceiveTaskInfoResponse)
	err := c.cc.Invoke(ctx, "/api.MigrationService/ReceiveTaskInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *migrationServiceClient) ReceiveJobNextSpeed(ctx context.Context, in *NextSpeedRequest, opts ...grpc.CallOption) (*NextSpeedResponse, error) {
	out := new(NextSpeedResponse)
	err := c.cc.Invoke(ctx, "/api.MigrationService/ReceiveJobNextSpeed", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *migrationServiceClient) ReceiveExecutedJobInfo(ctx context.Context, in *ReceiveExecutedJobInfoRequest, opts ...grpc.CallOption) (*ReceiveExecutedJobInfoResponse, error) {
	out := new(ReceiveExecutedJobInfoResponse)
	err := c.cc.Invoke(ctx, "/api.MigrationService/ReceiveExecutedJobInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *migrationServiceClient) ReceiveFrag(ctx context.Context, in *ReceiveFragRequest, opts ...grpc.CallOption) (*ReceiveFragResponse, error) {
	out := new(ReceiveFragResponse)
	err := c.cc.Invoke(ctx, "/api.MigrationService/ReceiveFrag", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// MigrationServiceServer is the server API for MigrationService service.
type MigrationServiceServer interface {
	//  接收worker
	//  将worker信息更新到WorkerGroup
	//  将WorkerGroup发到各TaskLister（此步由WorkerGroup的变量共享完成）
	//  将属于各任务的分片心跳发到各TaskLister（用于TaskLister重发超时分片）
	ReceiveWork(context.Context, *ReceiveWorkRequest) (*ReceiveWorkResponse, error)
	//  接收分片迁移结果
	//  将属于各任务的分片结束信息发到各TaskLister
	//  param frag_result_str:
	ReceiveFragResult(context.Context, *ReceiveFragResultRequest) (*ReceiveFragResultResponse, error)
	//  接收任务状态、qps限制的查询
	ReceiveTaskState(context.Context, *ReceiveTaskStateRequest) (*ReceiveTaskStateResponse, error)
	// 接收qps token的消费.
	ReceiveTaskQpsConsume(context.Context, *ReceiveTaskQpsConsumeRequest) (*ReceiveTaskQpsConsumeResponse, error)
	// 接收flow token的消费
	ReceiveTaskFlowConsume(context.Context, *ReceiveTaskFlowConsumeRequest) (*ReceiveTaskFlowConsumeResponse, error)
	//worker向lister获取任务基础信息
	ReceiveTaskInfo(context.Context, *ReceiveTaskInfoRequest) (*ReceiveTaskInfoResponse, error)
	// worker查询任务可用速度
	ReceiveJobNextSpeed(context.Context, *NextSpeedRequest) (*NextSpeedResponse, error)
	// worker 定时查询可执行任务，用于后面获取任务资源分配
	ReceiveExecutedJobInfo(context.Context, *ReceiveExecutedJobInfoRequest) (*ReceiveExecutedJobInfoResponse, error)
	// rpc consume frag
	ReceiveFrag(context.Context, *ReceiveFragRequest) (*ReceiveFragResponse, error)
}

// UnimplementedMigrationServiceServer can be embedded to have forward compatible implementations.
type UnimplementedMigrationServiceServer struct {
}

func (*UnimplementedMigrationServiceServer) ReceiveWork(context.Context, *ReceiveWorkRequest) (*ReceiveWorkResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ReceiveWork not implemented")
}
func (*UnimplementedMigrationServiceServer) ReceiveFragResult(context.Context, *ReceiveFragResultRequest) (*ReceiveFragResultResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ReceiveFragResult not implemented")
}
func (*UnimplementedMigrationServiceServer) ReceiveTaskState(context.Context, *ReceiveTaskStateRequest) (*ReceiveTaskStateResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ReceiveTaskState not implemented")
}
func (*UnimplementedMigrationServiceServer) ReceiveTaskQpsConsume(context.Context, *ReceiveTaskQpsConsumeRequest) (*ReceiveTaskQpsConsumeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ReceiveTaskQpsConsume not implemented")
}
func (*UnimplementedMigrationServiceServer) ReceiveTaskFlowConsume(context.Context, *ReceiveTaskFlowConsumeRequest) (*ReceiveTaskFlowConsumeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ReceiveTaskFlowConsume not implemented")
}
func (*UnimplementedMigrationServiceServer) ReceiveTaskInfo(context.Context, *ReceiveTaskInfoRequest) (*ReceiveTaskInfoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ReceiveTaskInfo not implemented")
}
func (*UnimplementedMigrationServiceServer) ReceiveJobNextSpeed(context.Context, *NextSpeedRequest) (*NextSpeedResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ReceiveJobNextSpeed not implemented")
}
func (*UnimplementedMigrationServiceServer) ReceiveExecutedJobInfo(context.Context, *ReceiveExecutedJobInfoRequest) (*ReceiveExecutedJobInfoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ReceiveExecutedJobInfo not implemented")
}
func (*UnimplementedMigrationServiceServer) ReceiveFrag(context.Context, *ReceiveFragRequest) (*ReceiveFragResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ReceiveFrag not implemented")
}

func RegisterMigrationServiceServer(s *grpc.Server, srv MigrationServiceServer) {
	s.RegisterService(&_MigrationService_serviceDesc, srv)
}

func _MigrationService_ReceiveWork_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReceiveWorkRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MigrationServiceServer).ReceiveWork(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.MigrationService/ReceiveWork",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MigrationServiceServer).ReceiveWork(ctx, req.(*ReceiveWorkRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MigrationService_ReceiveFragResult_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReceiveFragResultRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MigrationServiceServer).ReceiveFragResult(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.MigrationService/ReceiveFragResult",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MigrationServiceServer).ReceiveFragResult(ctx, req.(*ReceiveFragResultRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MigrationService_ReceiveTaskState_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReceiveTaskStateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MigrationServiceServer).ReceiveTaskState(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.MigrationService/ReceiveTaskState",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MigrationServiceServer).ReceiveTaskState(ctx, req.(*ReceiveTaskStateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MigrationService_ReceiveTaskQpsConsume_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReceiveTaskQpsConsumeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MigrationServiceServer).ReceiveTaskQpsConsume(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.MigrationService/ReceiveTaskQpsConsume",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MigrationServiceServer).ReceiveTaskQpsConsume(ctx, req.(*ReceiveTaskQpsConsumeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MigrationService_ReceiveTaskFlowConsume_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReceiveTaskFlowConsumeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MigrationServiceServer).ReceiveTaskFlowConsume(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.MigrationService/ReceiveTaskFlowConsume",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MigrationServiceServer).ReceiveTaskFlowConsume(ctx, req.(*ReceiveTaskFlowConsumeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MigrationService_ReceiveTaskInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReceiveTaskInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MigrationServiceServer).ReceiveTaskInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.MigrationService/ReceiveTaskInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MigrationServiceServer).ReceiveTaskInfo(ctx, req.(*ReceiveTaskInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MigrationService_ReceiveJobNextSpeed_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(NextSpeedRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MigrationServiceServer).ReceiveJobNextSpeed(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.MigrationService/ReceiveJobNextSpeed",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MigrationServiceServer).ReceiveJobNextSpeed(ctx, req.(*NextSpeedRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MigrationService_ReceiveExecutedJobInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReceiveExecutedJobInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MigrationServiceServer).ReceiveExecutedJobInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.MigrationService/ReceiveExecutedJobInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MigrationServiceServer).ReceiveExecutedJobInfo(ctx, req.(*ReceiveExecutedJobInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MigrationService_ReceiveFrag_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReceiveFragRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MigrationServiceServer).ReceiveFrag(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.MigrationService/ReceiveFrag",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MigrationServiceServer).ReceiveFrag(ctx, req.(*ReceiveFragRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _MigrationService_serviceDesc = grpc.ServiceDesc{
	ServiceName: "api.MigrationService",
	HandlerType: (*MigrationServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ReceiveWork",
			Handler:    _MigrationService_ReceiveWork_Handler,
		},
		{
			MethodName: "ReceiveFragResult",
			Handler:    _MigrationService_ReceiveFragResult_Handler,
		},
		{
			MethodName: "ReceiveTaskState",
			Handler:    _MigrationService_ReceiveTaskState_Handler,
		},
		{
			MethodName: "ReceiveTaskQpsConsume",
			Handler:    _MigrationService_ReceiveTaskQpsConsume_Handler,
		},
		{
			MethodName: "ReceiveTaskFlowConsume",
			Handler:    _MigrationService_ReceiveTaskFlowConsume_Handler,
		},
		{
			MethodName: "ReceiveTaskInfo",
			Handler:    _MigrationService_ReceiveTaskInfo_Handler,
		},
		{
			MethodName: "ReceiveJobNextSpeed",
			Handler:    _MigrationService_ReceiveJobNextSpeed_Handler,
		},
		{
			MethodName: "ReceiveExecutedJobInfo",
			Handler:    _MigrationService_ReceiveExecutedJobInfo_Handler,
		},
		{
			MethodName: "ReceiveFrag",
			Handler:    _MigrationService_ReceiveFrag_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/migrate_rpc_lister.proto",
}
