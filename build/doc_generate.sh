#!/bin/sh
set -e
BASE_PATH=$(cd `dirname $0`;pwd)
CODE_PATH=${BASE_PATH}/..
DOCKER_IMAGE='csighub.tencentyun.com/pulse-line/golang-app-builder'
export $(egrep -v '^#' ${BASE_PATH}/../.env | xargs)


while getopts "d:" arg
do
    case ${arg} in
        d)
            docker_build=${OPTARG}
            ;;
    esac
done

GOOGLE_PB_PKG_PATH="${CODE_PATH}/api"
GOOGLE_PB_MOD_PATH="$(go list -f '{{ .Dir }}' -m github.com/grpc-ecosystem/grpc-gateway)"
if [ ! -z ${GOOGLE_PB_MOD_PATH} ]; then
    chmod -R 777 ${GOOGLE_PB_MOD_PATH}/third_party/googleapis
    cp -r ${GOOGLE_PB_MOD_PATH}/third_party/googleapis ${GOOGLE_PB_PKG_PATH}
fi

if [ -z ${docker_build} ];then
    echo "generate documents based local tools"
else
    if [ ${docker_build} == "yes" ];then
        if [ -z $(docker images -q ${DOCKER_IMAGE}) ];then
            echo "${DOCKER_IMAGE} not exist"
            docker pull ${DOCKER_IMAGE}
        fi
        alias protoc="docker run --rm -it \
                         -v ${CODE_PATH}:/data -w \
                         /data ${DOCKER_IMAGE} protoc"
    # use latest googleapis in mod path or in api/googleapis
    GOOGLE_PB_PKG_PATH="/data/api"
    echo "generate documents based tools in docker"
    else
        echo "generate documents based local tools"
    fi
fi

HTML_DOC_GENERATE_CMD="protoc -I. -I api -I "${GOOGLE_PB_PKG_PATH}"/googleapis -I api/third_party --doc_out=./docs/ --doc_opt=html,$PROJECT_NAME.html api/*.proto"
MD_DOC_GENERATE_CMD="protoc -I. -I api -I "${GOOGLE_PB_PKG_PATH}"/googleapis -I api/third_party --doc_out=./docs/ --doc_opt=markdown,$PROJECT_NAME.md api/*.proto"

eval ${HTML_DOC_GENERATE_CMD}
eval ${MD_DOC_GENERATE_CMD}