#!/bin/sh
set -e
BASE_PATH=$(cd `dirname $0`;pwd)
CODE_PATH=${BASE_PATH}/..
DOCKER_IMAGE='csighub.tencentyun.com/pulse-line/golang-app-builder'
export $(egrep -v '^#' ${BASE_PATH}/../.env | xargs)


while getopts "d:" arg
do
    case ${arg} in
        d)
            docker_build=${OPTARG}
            ;;
    esac
done

GOOGLE_PB_PKG_PATH="${CODE_PATH}/api"
GOOGLE_PB_MOD_PATH="$(go list -f '{{ .Dir }}' -m github.com/grpc-ecosystem/grpc-gateway)"
if [ ! -z ${GOOGLE_PB_MOD_PATH} ]; then
    chmod -R 777 ${GOOGLE_PB_MOD_PATH}/third_party/googleapis
    cp -r ${GOOGLE_PB_MOD_PATH}/third_party/googleapis ${GOOGLE_PB_PKG_PATH}
fi


if [ -z ${docker_build} ];then
    echo "generate pb based local tools"
else
    if [ ${docker_build} == "yes" ];then
        if [ -z $(docker images -q ${DOCKER_IMAGE}) ];then
            echo "${DOCKER_IMAGE} not exist"
            docker pull ${DOCKER_IMAGE}
        fi
        alias protoc="docker run --rm -it \
                         -v ${CODE_PATH}:/data -w \
                         /data ${DOCKER_IMAGE} protoc"
    # use latest googleapis in mod path or in api/googleapis
    GOOGLE_PB_PKG_PATH="/data/api"
    echo "generate pb based tools in docker"
    else
        echo "generate pb based local tools"
    fi
fi

PB_GENERATE_CMD="protoc -I. -I api -I "${GOOGLE_PB_PKG_PATH}"/googleapis -I api/third_party --go_out=plugins=grpc:. api/*.proto"
PB_PROXY_GENERATE_CMD="protoc -I. -I api -I "${GOOGLE_PB_PKG_PATH}"/googleapis -I api/third_party --grpc-gateway_out=logtostderr=true:. api/*.proto"
#PB_SW_GENERATE_CMD="protoc -I. -I api -I "${GOOGLE_PB_PKG_PATH}"/googleapis -I api/third_party --swagger_out=logtostderr=true:. api/*.proto"
PB_QCLOUD_GENERATE_CMD="protoc -I. -I api -I "${GOOGLE_PB_PKG_PATH}"/googleapis -I api/third_party --qcloudapi_out=logtostderr=true:. api/*.proto"

eval ${PB_GENERATE_CMD}
eval ${PB_PROXY_GENERATE_CMD}
#eval ${PB_SW_GENERATE_CMD}
eval ${PB_QCLOUD_GENERATE_CMD}


#swCount=$(ls ./api/*.swagger.json | grep -v api.swagger.json | wc -l)
#if [ $swCount -gt 1 ]; then
#    MIXIN_CMD="swagger -q mixin $(ls ./api/*.swagger.json | grep -v api.swagger.json) -o ./api/api.swagger.json"
#    eval ${MIXIN_CMD} || true
#elif [ $swCount -eq 1 ]; then
#    # rename the only swagger file to api.swagger.json
#    CP_CMD='cp $(ls ./api/*.swagger.json | grep -v api.swagger.json) ./api/api.swagger.json'
#    eval ${CP_CMD}
#fi
