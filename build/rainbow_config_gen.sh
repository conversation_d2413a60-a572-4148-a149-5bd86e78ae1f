#!/bin/sh

BASE_PATH=$(cd `dirname $0`;pwd)
CODE_PATH=${BASE_PATH}/..

if ! [ -x "$(command -v rainbow_confd)" ]
then
    echo "rainbow_confd not found"
    kernel=$(uname -s | tr '[:upper:]' '[:lower:]')
    curl https://mirrors.tencent.com/repository/generic/rainbow/confd/1.0.0/rainbow_confd.${kernel} -o ${GOPATH}/bin/rainbow_confd \
        && chmod +x ${GOPATH}/bin/rainbow_confd
fi

## Merge local config and remote rainbow config to generate fully pl config
CONFIG_PATH=${CODE_PATH}/configs/rainbow_config

cd ${CONFIG_PATH}
echo "Merge local config and remote rainbow config to generate fully pl config"

rainbow_confd -onetime -conf_file=./confd.toml

