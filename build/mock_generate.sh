#!/bin/sh
set -e
BASE_PATH=$(cd `dirname $0`;pwd)
CODE_PATH=${BASE_PATH}/..
DOCKER_IMAGE='csighub.tencentyun.com/pulse-line/golang-app-builder'
export $(egrep -v '^#' ${BASE_PATH}/../.env | xargs)
while getopts "d:" arg
do
    case ${arg} in
        d)
            docker_build=${OPTARG}
            ;;
    esac
done

if [ -z ${docker_build} ];then
    echo "generate mock based local tools"
else
    if [ ${docker_build} == "yes" ];then
        if [ -z $(docker images -q ${DOCKER_IMAGE}) ];then
            echo "${DOCKER_IMAGE} not exist"
            docker pull ${DOCKER_IMAGE}
        fi
        alias mockgen="docker run --rm -it \
                         -v ${CODE_PATH}:/data \
                         -v $GOPATH/pkg:/go/pkg \
                         -w /data ${DOCKER_IMAGE} mockgen"
    # use latest googleapis in mod path or in api/googleapis
    echo "generate mock based tools in docker"
    else
        echo "generate mock based local tools"
    fi
fi

cd ./api
mkdir -p mock
for pb in *.pb.go;do
    serviceName=$(echo ${pb} |awk -F '.' '{print $1}')
    MOCK_GENERATE_CMD="mockgen --source=${pb} --destination=./mock/$serviceName""_mock.go"
    #TODO:some package dependencies in docker
    eval ${MOCK_GENERATE_CMD}
done

