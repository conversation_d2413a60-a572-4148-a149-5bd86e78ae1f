#!/bin/sh

# args:
# -a : machine arch, support linux, mac, windows, linux as default.
# -e : build environment, support prod, dev. dev as default.
# -v : enable vendor build mode, support enable,disable. disable as default.
# -d : use build tools in docker or local, support yes or no. no as default.
# 0: check env
set -e
while getopts "a:e:v:d:" arg; do
  case ${arg} in
  a)
    system=${OPTARG}
    ;;
  e)
    build_env=${OPTARG}
    ;;
  v)
    vendor_build=${OPTARG}
    ;;
  d)
    docker_build=${OPTARG}
    ;;
  esac
done

BASE_PATH=$(
  cd $(dirname $0)
  pwd
)
CODE_PATH=${BASE_PATH}/..
cd ${CODE_PATH}
DOCKER_IMAGE='csighub.tencentyun.com/pulse-line/golang-app-builder'

if [ -z ${system} ]; then
  GS=$(uname -s)
  case "${GS}" in
  Linux) GS="linux" ;;
  Darwin) GS="darwin" ;;
  FreeBSD) GS="freebsd" ;;
  *) echo "Your Operating System -> ITS NOT SUPPORTED" ;;
  esac
else
  if [ ${system} = "windows" ]; then
    GS="windows"
  elif [ ${system} = "mac" ]; then
    GS="darwin"
  elif [ ${system} = "linux" ]; then
    GS="linux"
  else
    GS="linux"
  fi
fi

if [ -z ${build_env} ]; then
  BUILD_ENV="dev"
else
  if [ ${build_env} = "dev" ] || [ ${build_env} = "prod" ]; then
    BUILD_ENV=${build_env}
  else
    BUILD_ENV="dev"
  fi
fi

echo "machine env: "${GS}
echo "build env: "${BUILD_ENV}
# 1: Declare to project root
echo "Step 1: Declare ENV ..."
export $(egrep -v '^#' ${BASE_PATH}/../.env | xargs)

if [ -z ${PROJECT_NAME} ]; then
  PROJECT_NAME="pulse_line_demo"
fi

echo "PROJECT_NAME = ${PROJECT_NAME}"

# go mod init
if [ -z ${vendor_build} ]; then
  vendor_build="disable"
fi

GOOGLE_PB_PKG_PATH="${CODE_PATH}/api"
if [ ${vendor_build} = "enable" ]; then
  echo "step 2: skip go mod"
  BUILD=" build -mod=vendor "
else
  echo "step 2: go mod to init dependencies"
  BUILD=" build "
  go mod download
  rm -rf ${BASE_PATH}/../vendor
  go mod vendor
  # sync googleapis to local
  GOOGLE_PB_MOD_PATH="$(go list -f '{{ .Dir }}' -m github.com/grpc-ecosystem/grpc-gateway)"
  if [ ! -z ${GOOGLE_PB_MOD_PATH} ]; then
    chmod -R 777 ${GOOGLE_PB_MOD_PATH}/third_party/googleapis
    cp -r ${GOOGLE_PB_MOD_PATH}/third_party/googleapis ${GOOGLE_PB_PKG_PATH}
  fi
fi

# alias to docker cmd in docker-build mode
TOOL_LIST="protoc swagger go-bindata mockgen "
if [ -z ${docker_build} ]; then
  echo "local build"
else
  if [ ${docker_build} = "yes" ]; then
    if [ -z $(docker images -q ${DOCKER_IMAGE}) ]; then
      echo "${DOCKER_IMAGE} not exist"
      docker pull ${DOCKER_IMAGE}
    fi
    # alias tool
    for tool in ${TOOL_LIST}; do
      alias ${tool}="docker run --rm -it \
                            -v ${CODE_PATH}:/data -w \
                            /data ${DOCKER_IMAGE} ${tool}"
    done
    # use latest googleapis in mod path or in api/googleapis
    GOOGLE_PB_PKG_PATH="/data/api"
  else
    echo "local build"
  fi
fi

PB_GENERATE_CMD="protoc -I. -I api -I "${GOOGLE_PB_PKG_PATH}"/googleapis -I api/third_party --go_out=plugins=grpc:. api/*.proto"
PB_PROXY_GENERATE_CMD="protoc -I. -I api -I "${GOOGLE_PB_PKG_PATH}"/googleapis -I api/third_party --grpc-gateway_out=logtostderr=true:. api/*.proto"
PB_SW_GENERATE_CMD="protoc -I. -I api -I "${GOOGLE_PB_PKG_PATH}"/googleapis -I api/third_party --swagger_out=logtostderr=true:. api/*.proto"
PB_QCLOUD_GENERATE_CMD="protoc -I. -I api -I "${GOOGLE_PB_PKG_PATH}"/googleapis -I api/third_party --qcloudapi_out=logtostderr=true:. api/*.proto"
HTML_DOC_GENERATE_CMD="protoc -I. -I api -I "${GOOGLE_PB_PKG_PATH}"/googleapis -I api/third_party --doc_out=./docs/ --doc_opt=html,$PROJECT_NAME.html api/*.proto"
MD_DOC_GENERATE_CMD="protoc -I. -I api -I "${GOOGLE_PB_PKG_PATH}"/googleapis -I api/third_party --doc_out=./docs/ --doc_opt=markdown,$PROJECT_NAME.md api/*.proto"

# 3: Compile proto file
#echo "Step 3.1: Compile proto file at api/*.proto ..."
#eval ${PB_GENERATE_CMD}
#
#echo "Step 3.2: Compile reverse proxy file at api/*.proto ..."
#eval ${PB_PROXY_GENERATE_CMD}
#
#echo "Step 3.3: Compile swagger file at api/*.proto ..."
#eval ${PB_SW_GENERATE_CMD}
#
#echo "Step 3.4: generate api documents in html and markdown formats"
#rm -rf /api/*
#eval ${HTML_DOC_GENERATE_CMD}
#eval ${MD_DOC_GENERATE_CMD}

#echo "Step 3.5: Generate qcloud api v3"
#eval ${PB_QCLOUD_GENERATE_CMD}

mkdir -p build_tmp
# 4: Build main file
echo "Step 4.1: Build main file at cmd/* ..."
GOOS=${GS} GOARCH=amd64 go build -ldflags "-X google.golang.org/protobuf/reflect/protoregistry.conflictPolicy=warn" -o ./build_tmp/lister cmd/lister/lister.go

# 4.1 Construct k8s yaml file
echo "Step 4.2: Build manifests"
export $(egrep -v '^#' .env | grep 'PORT' | xargs)
export $(egrep -v '^#' .env | grep 'APP_VERSION' | xargs)
DEPLOYMENT_TEMPLATE=deployments/template/deployment-template.yaml
SERVICE_TEMPLATE=deployments/template/service-template.yaml

SED_PARAM='-i.bak'

for file in deployments/manifests/*; do
  DEPLOYMENT_OUTPUT=${file}/deployment.yaml
  SERVICE_OUTPUT=${file}/service.yaml

  # Remove original files
  rm -f ${file}/deployment.yaml
  rm -f ${file}/service.yaml

  # Copy template to new
  cp ${DEPLOYMENT_TEMPLATE} ${DEPLOYMENT_OUTPUT}
  cp ${SERVICE_TEMPLATE} ${SERVICE_OUTPUT}

  export $(egrep -v '^#' ${file}/.env | xargs)
  # For Deployment
  sed ${SED_PARAM} "s/GRPC_PORT/${GRPC_PORT}/g" ${DEPLOYMENT_OUTPUT}
  sed ${SED_PARAM} "s/HTTP_PORT/${HTTP_PORT}/g" ${DEPLOYMENT_OUTPUT}
  sed ${SED_PARAM} "s/SWAGGER_PORT/${SWAGGER_PORT}/g" ${DEPLOYMENT_OUTPUT}
  sed ${SED_PARAM} "s/PROM_PORT/${PROM_PORT}/g" ${DEPLOYMENT_OUTPUT}
  sed ${SED_PARAM} "s/QCLOUDAPI_PORT/${QCLOUDAPI_PORT}/g" ${DEPLOYMENT_OUTPUT}
  sed ${SED_PARAM} "s/value: REALM/value: ${REALM}/g" ${DEPLOYMENT_OUTPUT}
  sed ${SED_PARAM} "s/value: REGION/value: ${REGION}/g" ${DEPLOYMENT_OUTPUT}
  sed ${SED_PARAM} "s/value: AZ/value: ${AZ}/g" ${DEPLOYMENT_OUTPUT}
  sed ${SED_PARAM} "s/value: DOMAIN/value: ${DOMAIN}/g" ${DEPLOYMENT_OUTPUT}
  sed ${SED_PARAM} "s/value: \"APP_VERSION\"/value: \"${APP_VERSION}\"/g" ${DEPLOYMENT_OUTPUT}
  # For Service
  sed ${SED_PARAM} "s/GRPC_PORT/${GRPC_PORT}/g" ${SERVICE_OUTPUT}
  sed ${SED_PARAM} "s/HTTP_PORT/${HTTP_PORT}/g" ${SERVICE_OUTPUT}
  sed ${SED_PARAM} "s/SWAGGER_PORT/${SWAGGER_PORT}/g" ${SERVICE_OUTPUT}
  sed ${SED_PARAM} "s/PROM_PORT/${PROM_PORT}/g" ${SERVICE_OUTPUT}
  sed ${SED_PARAM} "s/QCLOUDAPI_PORT/${QCLOUDAPI_PORT}/g" ${SERVICE_OUTPUT}
  sed ${SED_PARAM} "s/CLUSTER_ID/${CLUSTER_ID}/g" ${SERVICE_OUTPUT}
  sed ${SED_PARAM} "s/VPC_SUBNET_ID/${VPC_SUBNET_ID}/g" ${SERVICE_OUTPUT}
  sed ${SED_PARAM} "s/value: REALM/value: ${REALM_VALUE}/g" ${SERVICE_OUTPUT}
  sed ${SED_PARAM} "s/value: REGION/value: ${REGION_VALUE}/g" ${SERVICE_OUTPUT}
  sed ${SED_PARAM} "s/value: AZ/value: ${AZ_VALUE}/g" ${SERVICE_OUTPUT}
  sed ${SED_PARAM} "s/value: DOMAIN/value: ${DOMAIN_VALUE}/g" ${SERVICE_OUTPUT}

  # Remove .bak file
  # Why? Because sed is not working as the same way in GNU and BSD
  rm ${file}/*.bak
done

# 5: Make target package
echo "Step 5: Make target package ..."
rm -rf target

mkdir target \
  target/${PROJECT_NAME}
mkdir target/${PROJECT_NAME}/bin \
  target/${PROJECT_NAME}/cmd \
  target/${PROJECT_NAME}/tools \
  target/${PROJECT_NAME}/env \
  target/${PROJECT_NAME}/init \
  target/${PROJECT_NAME}/scripts \
  target/${PROJECT_NAME}/var \
  target/${PROJECT_NAME}/logs \
  target/${PROJECT_NAME}/configs \
  target/${PROJECT_NAME}/api \
  target/${PROJECT_NAME}/assets

# 6: Copy related files to target folder
echo "Step 6: Copy related files to target folder..."
mv ./build_tmp/* target/${PROJECT_NAME}/bin/

if [ -f "./build_tmp/CommonClient" ]; then
  mv ./build_tmp/CommonClient target/${PROJECT_NAME}/bin/CommonClient
fi

rm -rf build_tmp

chmod +x target/${PROJECT_NAME}/bin/*

#cp -r configs/* target/${PROJECT_NAME}/configs/
#cp -r scripts/* target/${PROJECT_NAME}/scripts/
#cp -r api/*.swagger.json target/${PROJECT_NAME}/api/
#cp .env target/${PROJECT_NAME}/
#if [ ! -z "$(ls assets)" ]; then
#  cp -r assets/* target/${PROJECT_NAME}/assets/
#fi
