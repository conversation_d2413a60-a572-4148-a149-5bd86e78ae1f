---
level: info
development: false
disableCaller: false
disableStacktrace: false
encoding: console
outputPaths: []
# Full path or relative path
#- stdout
#- "logs/app.log"
errorOutputPaths: []
#- stderr
#- "logs/app.log"
initialFields: {}
encoderConfig:
  messageKey: msg
  levelKey: level
  timeKey: timestamp
  nameKey: logger
  callerKey: caller
  stacktraceKey: stacktrace
  lineEnding: "\n"
  levelEncoder: capital
  timeEncoder: iso8601
  durationEncoder: ""
  callerEncoder: short
  nameEncoder: full
# Init lumberjack config
lumberjackConfig:
  # MaxSize is the maximum size in megabytes of the log file before it gets
  # rotated. It defaults to 100 megabytes.
  maxsize: 1024
  # MaxAge is the maximum number of days to retain old log files based on the
  # timestamp encoded in their filename.  Note that a day is defined as 24
  # hours and may not exactly correspond to calendar days due to daylight
  # savings, leap seconds, etc. The default is not to remove old log files
  # based on age.
  maxage: 7
  # MaxBackups is the maximum number of old log files to retain.  The default
  # is to retain all old log files (though Max<PERSON>ge may still cause them to get
  # deleted.)
  maxbackups: 3
  # LocalTime determines if the time used for formatting the timestamps in
  # backup files is the computer's local time.  The default is to use UTC
  # time.
  localtime: true
  # Compress determines if the rotated log files should be compressed
  # using gzip. The default is not to perform compression.
  compress: false