package defaultCosClient

import (
	"fmt"
	"net/http"
	"net/url"
	"runtime/debug"
	"strings"
	"sync"
	"time"

	"git.code.oa.com/cloud-msp/migration-lister/internal/service/apicall"
	"git.code.oa.com/cloud-msp/migration-lister/pkg/conf"
	"git.code.oa.com/cloud-msp/migration-lister/tools"
	"git.code.oa.com/pulse-line/pl_boot"
	"github.com/tencentyun/cos-go-sdk-v5"
	"go.uber.org/zap"
)

// 在这里进行默认的cos客户端管理，使用的账号是默认的请求mspServer获取的临时账号
// 供给failFile上传管理、failFile重试遍历、采用上传到默认cos的文件的遍历使用
// 区分测试环境和正式环境，APPID不同，目前写死，怎么切换？

var (
	defaultCosClient *cos.Client
	appId            int64
	bucket           string
	region           string
	logger           *zap.Logger
	mu               sync.Mutex
)

const (
	AccountAreaInternal = 0
	AccountAreaOverSea  = 1
)

// init
func Init() {
	logger = pl_boot.AppCtx.GetAppLogger()
	if strings.Contains(conf.ListerConfig.TencentCloud.Host, "test") {
		appId = **********
	} else {
		for {
			resp, err := apicall.GetApiCall().DescribeAccountInfo()
			if err != nil {
				logger.Error("init Account fail", zap.Error(err))
				time.Sleep(2 * time.Second)
				continue
			}

			if int(resp.Response.AccountArea) == AccountAreaInternal {
				appId = **********
				region = "ap-guangzhou"
			} else {
				appId = **********
				region = "ap-singapore"
			}
			break
		}
	}

	if conf.ListerConfig.GlobalConf.UseAccelerateDomain {
		region = "accelerate"
	}

	bucket = fmt.Sprintf("migrate-%d", appId)

	// 使用配置的bucket region
	if conf.ListerConfig.TencentCloud.Region != "" && conf.ListerConfig.TencentCloud.Bucket != "" {
		region = conf.ListerConfig.TencentCloud.Region
		bucket = conf.ListerConfig.TencentCloud.Bucket
	}

	sId, sKey, token, err := getTmpSecret("")
	if err != nil {
		logger.Fatal("init default cos error:" + err.Error())
	}

	if sId == "" && err == nil {
		logger.Info("outer mode and task is empty, return")
	}

	defaultCosClient = newCosClient(region, bucket, sId, sKey, token)
}

// GetClient
func GetClient() *cos.Client {
	mu.Lock()
	defer mu.Unlock()

	return defaultCosClient
}

func getTmpSecret(taskId string) (secId, secKey, token string, err error) {
	if conf.ListerConfig.ListerInfo.Mode == conf.InnerMode {
		return conf.ListerConfig.TencentCloud.SecretId, conf.ListerConfig.TencentCloud.SecretKey, "", nil
	}

	if conf.ListerConfig.TencentCloud.Region != "" && conf.ListerConfig.TencentCloud.Bucket != "" {
		return conf.ListerConfig.TencentCloud.SecretId, conf.ListerConfig.TencentCloud.SecretKey, "", nil
	}

	if taskId == "" {
		logger.Warn("outer mode and task is empty, please wait")
		return
	}

	resp, err := apicall.GetApiCall().DescribeTmpSecret(taskId)
	if err != nil {
		logger.Error("DescribeTmpSecret Error", zap.Error(err))
		return
	}
	logger.Info("GetFederationToken",
		zap.String("ReqId", *resp.Response.RequestId),
		zap.String("stsToken.TmpSecretId", resp.Response.TmpSecretId),
		zap.String("stsToken.TmpSecretKey", resp.Response.TmpSecretKey),
		zap.String("stsToken.SessionToken", resp.Response.SessionToken),
	)

	secId, err = tools.DecryptSecret(resp.Response.TmpSecretId)
	if err != nil {
		logger.Error("DecryptSecretError",
			zap.String("ReqId", *resp.Response.RequestId),
			zap.String("stsToken.TmpSecretId", resp.Response.TmpSecretId),
			zap.Error(err),
		)
		return
	}

	secKey, err = tools.DecryptSecret(resp.Response.TmpSecretKey)
	if err != nil {
		logger.Error("DecryptSecretError",
			zap.String("ReqId", *resp.Response.RequestId),
			zap.String("stsToken.TmpSecretKey", resp.Response.TmpSecretId),
			zap.Error(err),
		)
		return
	}

	token, err = tools.DecryptSecret(resp.Response.SessionToken)
	if err != nil {
		logger.Error("DecryptSecretError",
			zap.String("ReqId", *resp.Response.RequestId),
			zap.String("stsToken.TmpSessionToken", resp.Response.TmpSecretId),
			zap.Error(err),
		)
		return
	}

	return
}

func newCosClient(region, bucket, secretID, secretKey, token string) *cos.Client {
	// concatenate from region and bucket
	var bucketURL string
	if conf.ListerConfig.GlobalConf.UseInternalDomain {
		bucketURL = fmt.Sprintf("http://%s.cos-internal.%s.tencentcos.cn", bucket, region)
	} else {
		bucketURL = fmt.Sprintf("https://%s.cos.%s.myqcloud.com", bucket, region)
	}

	u, _ := url.Parse(bucketURL)

	bu := &cos.BaseURL{BucketURL: u}
	cosClient := cos.NewClient(bu, &http.Client{
		Transport: &cos.AuthorizationTransport{
			SecretID:     secretID,
			SecretKey:    secretKey,
			SessionToken: token,
			Transport: &http.Transport{
				MaxIdleConns:        1000,
				MaxIdleConnsPerHost: 1000,
			},
		},
	})

	return cosClient
}

// UpdateCos
func UpdateCos(taskId string) {
	defer func() {
		if e := recover(); e != nil {
			pl_boot.AppCtx.GetAppLogger().Error(
				"defaultCos Update panic",
				zap.Any("error", e),
				zap.Any("stack", string(debug.Stack()[:])),
			)
		}
	}()

	sId, sKey, token, err := getTmpSecret(taskId)
	if err != nil {
		logger.Error("getTmpSecret error this turn", zap.Error(err))
		return
	}

	c := newCosClient(region, bucket, sId, sKey, token)
	mu.Lock()
	defaultCosClient = c
	mu.Unlock()
}
