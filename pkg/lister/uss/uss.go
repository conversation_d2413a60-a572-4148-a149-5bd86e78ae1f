package uss

import (
	"errors"
	"fmt"
	"net/url"
	"path"
	"strings"

	"git.code.oa.com/cloud-msp/migration-lister/pkg/conf"
	"git.code.oa.com/cloud-msp/migration-lister/pkg/lister"
	"git.code.oa.com/cloud-msp/migration-lister/third-party/upyun"
	secapi "git.code.oa.com/sec-api/go/scurl"
	"go.uber.org/zap"
	"golang.org/x/net/context"
)

// ussObjectLister iterate all objects of a upyun bucket.
type ussObjectLister struct {
	accessKey string
	secretKey string
	Bucket    string
	svc       *upyun.UpYun
}

// NewUSSObjectLister create a new ussObject instance.
func NewUSSObjectLister(accessKey, secretKey, bucket, cdnDomain string) (*ussObjectLister, error) {
	if len(accessKey) <= 0 || len(secretKey) <= 0 || len(bucket) <= 0 {
		return nil, errors.New("invalid argument")
	}

	if !strings.HasPrefix(cdnDomain, "http") {
		cdnDomain = "http://" + cdnDomain
	}

	// 内部的模式才做安全检测
	if conf.ListerConfig.ListerInfo.Mode == conf.InnerMode {
		secapi.WithAllowPorts([]string{"allow_all"})
		secCli := secapi.NewSafeClient()
		_, err := secCli.Get(cdnDomain)
		if err != nil {
			return nil, fmt.Errorf("ssrf warning > invalid endpoint: %v can not get public ip > %v ", cdnDomain, err)
		}
	}
	upyunConf := upyun.UpYunConfig{
		Operator: accessKey,
		Password: secretKey,
		Bucket:   bucket,
	}

	svc := upyun.NewUpYun(&upyunConf)

	return &ussObjectLister{
		accessKey: accessKey,
		secretKey: secretKey,
		Bucket:    bucket,
		svc:       svc,
	}, nil
}

// List
func (l *ussObjectLister) List(
	ctx context.Context,
	taskId, prefix, marker string,
	fragFileNum, fragMaxSize int,
	handler lister.ResultHandler,
	logger *zap.Logger,
	filters ...lister.ObjectFilter,
) (err error) {
	logger = logger.With(zap.String("platform", "USS"), zap.String("bucket", l.Bucket),
		zap.String("region", ""), zap.String("prefix", prefix))

	logger.Info("start listing objects",
		zap.Int("fragFileNum", fragFileNum), zap.Int("fragMaxSize", fragMaxSize))

	objsCh := make(chan *upyun.FileInfo)
	quitCh := make(chan bool)

	var input = &upyun.GetObjectsConfig{
		Path:         "/" + prefix,
		Headers:      map[string]string{},
		ObjectsChan:  objsCh,
		QuitChan:     quitCh,
		MaxListLevel: -1, // all level
	}

	input.Headers["X-List-Limit"] = fmt.Sprint(lister.DefaultPageSize)

	var retry = lister.MaxRetry

	err = l.do(ctx, retry, logger, taskId, prefix, marker, fragFileNum, fragMaxSize, handler,
		input, objsCh, quitCh,
		filters...)

	return err
}

func (l *ussObjectLister) do(
	ctx context.Context,
	retry int, logger *zap.Logger,
	taskId, prefix, marker string,
	fragFileNum, fragMaxSize int,
	handler lister.ResultHandler,
	input *upyun.GetObjectsConfig,
	objsCh chan *upyun.FileInfo,
	quitCh chan bool,
	filters ...lister.ObjectFilter,
) (err error) {
	svc := l.svc
	result := &lister.Result{TaskId: taskId, Prefix: prefix}
	for i := 0; i < retry; i++ {
		logger.Info("listing objects", zap.String("marker", marker))
		if marker != "" {
			input.Headers["X-List-Iter"] = marker
		}

		errCh := make(chan error)

		go func() {
			err := svc.List(input)
			if err != nil {
				logger.Error("ListReturnError", zap.Error(err), zap.Any("input", input))
				errCh <- err
			}

			// return nil while finished
			close(errCh)
		}()

		isFinished := false

	FOR_BRK:
		for {
			select {
			case <-ctx.Done():
				logger.Info("listing objects canceled")
				// quit List object
				quitCh <- true

				return ctx.Err()
			default:
			}

			select {
			case err := <-errCh:
				if err != nil {
					logger.Error("failed to list objects",
						zap.Error(err), zap.Int("retry", retry), zap.String("marker", marker))
				} else {
					isFinished = true
				}
				// break the for loop but not the select
				break FOR_BRK

			case obj := <-objsCh:
				if obj == nil {
					isFinished = true
					break FOR_BRK
				}

				marker = l.doResultProcess(result, obj, marker, fragFileNum, fragMaxSize, taskId, logger, handler,
					retry, filters...)
				logger.Info("listing objects", zap.Any("marker", marker))
			}
		}

		if !isFinished {
			continue
		}

		logger.Info("listing objects finished")
		if len(result.Contents) == 0 && result.Skip == 0 {
			return nil
		}

		// still some
		// result.Marker = marker
		// 把 marker 清空, master进程重启后从头list, 否则会少扫描文件
		// 比如 marker=aaa/bbb/test.jpg, prefix(也就是 uipyun list 接口的 uri 参数)必须是当前目录 aaa/bbb, 不能是为空(为空时表示根目录), 也不能是上级目录 aaa/,
		// 否则 upyun list 接口会失败
		result.Marker = ""
		if handlerErr := handler(taskId, result); handlerErr != nil {
			logger.Error("result handler error", zap.Error(handlerErr), zap.Int("retry", retry))
		}
		return nil
	}

	return fmt.Errorf("max retry limit reached")
}

func (l *ussObjectLister) doResultProcess(result *lister.Result, obj *upyun.FileInfo,
	rawMarker string,
	fragFileNum int,
	fragMaxSize int,
	taskId string,
	logger *zap.Logger,
	handler lister.ResultHandler,
	retry int,
	filters ...lister.ObjectFilter,
) (marker string) {
	marker = rawMarker
	listObj := toListerObject(obj, result.Prefix)
	listObj.Key = url.QueryEscape(listObj.Key)
	if !lister.FilterObject(listObj, filters...) {
		result.Skip++
	} else {
		result.Contents = append(result.Contents, listObj)
		result.Num += 1
		result.Size += int(listObj.Size)
	}

	if obj.NextMarker != "" {
		marker = obj.NextMarker
	}

	if result.Num < fragFileNum && result.Size < fragMaxSize && result.Skip < fragFileNum {
		return
	}

	// result.Marker = marker
	// 把 marker 清空, master进程重启后从头list, 否则会少扫描文件
	// 比如 marker=aaa/bbb/test.jpg, prefix(也就是 uipyun list 接口的 uri 参数)必须是当前目录 aaa/bbb, 不能是为空(为空时表示根目录), 也不能是上级目录 aaa/,
	// 否则 upyun list 接口会失败
	result.Marker = ""
	if handlerErr := handler(taskId, result); handlerErr != nil {
		logger.Error("result handler error", zap.Error(handlerErr), zap.Int("retry", retry))
	}

	result.Rest()
	return
}
func toListerObject(obj *upyun.FileInfo, prefix string) *lister.Content {
	return &lister.Content{
		Key:          path.Join(prefix, obj.Name),
		LastModified: obj.Time,
		Size:         obj.Size,
	}
}
