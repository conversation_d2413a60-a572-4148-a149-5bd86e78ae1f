package uss

import (
	"context"
	"fmt"
	"testing"

	"git.code.oa.com/cloud-msp/migration-lister/pkg/lister"
	"github.com/prashantv/gostub"
	"go.hlw.tencent.com/alego/components/log"
	"go.uber.org/zap"
)

var (
	ak        = ""
	sk        = ""
	bucket    = ""
	prefix    = ""
	cdnDomain = ""
)

func TestNewUSSLister(t *testing.T) {
	_, err := NewUSSObjectLister(ak, sk, bucket, cdnDomain)
	if err == nil {
		t.Errorf("should return err")
	}
}

func TestUSSObjectLister_cancelled_before_list(t *testing.T) {
	logger := log.GetLogger()

	var (
		jobId = "uss-list-cancelled"
	)

	ul, _ := NewUSSObjectLister(ak, sk, bucket, cdnDomain)

	ctx, cancel := context.WithCancel(context.Background())
	cancel()
	err := ul.List(
		ctx, jobId, "", "", 0, 0,
		func(string, *lister.Result) error {
			panic("listing shoule be cancelled")
		},
		logger,
	)

	if err != context.Canceled {
		t.E<PERSON>rf("want cancelled, got %v", err)
	}
}

func TestUSSObjectLister_cancelled_while_list(t *testing.T) {
	stubs := gostub.Stub(&lister.DefaultPageSize, 10)
	defer stubs.Reset()

	logger := log.GetLogger()

	var (
		jobId = "uss-list-cancelled"
	)

	ul, _ := NewUSSObjectLister(ak, sk, bucket, cdnDomain)

	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	err := ul.List(
		ctx, jobId, "", "", 0, 0,
		func(string, *lister.Result) error {
			// first page 的时候 cancel
			cancel()
			return nil
		},
		logger,
	)

	if err != context.Canceled {
		t.Errorf("want cancelled, got %v", err)
	}
}

func ExampleUSSObjectLister_list_all() {
	stubs := gostub.Stub(&lister.DefaultPageSize, 10)
	defer stubs.Reset()

	logger := log.GetLogger()

	ul, _ := NewUSSObjectLister(ak, sk, bucket, cdnDomain)

	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	var (
		size  int
		cnt   int
		jobId = "uss-list-cancelled"
	)
	resultHandler := func(jobId string, result *lister.Result) error {
		size += result.Size
		cnt += result.Num
		logger.Info("get result:", zap.Any("result", *result))
		return nil
	}

	err := ul.List(
		ctx, jobId, "", "", 0, 0,
		resultHandler,
		logger,
	)
	if err != nil {
		fmt.Printf("list err=%v", err)
	}

	fmt.Printf("size=%d, cnt=%d\n", size, cnt)
	// Output: size=62660970, cnt=3
}

func ExampleUSSObjectLister_list_prefix() {
	stubs := gostub.Stub(&lister.DefaultPageSize, 10)
	defer stubs.Reset()

	logger := log.GetLogger()

	ul, _ := NewUSSObjectLister(ak, sk, bucket, cdnDomain)

	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	var (
		size  int
		cnt   int
		jobId = "uss-list-cancelled"
	)
	resultHandler := func(jobId string, result *lister.Result) error {
		size += result.Size
		cnt += result.Num
		logger.Info("get result:", zap.Any("result", *result))
		return nil
	}

	err := ul.List(
		ctx, jobId, prefix, "", 0, 0,
		resultHandler,
		logger,
	)
	if err != nil {
		fmt.Printf("list err=%v", err)
	}

	fmt.Printf("size=%d, cnt=%d\n", size, cnt)
	// Output: size=62660970, cnt=3
}
