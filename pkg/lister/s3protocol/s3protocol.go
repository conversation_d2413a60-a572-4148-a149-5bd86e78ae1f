package s3protocol

import (
	"context"
	"errors"
	"fmt"
	"net/url"
	"strings"
	"time"

	"git.code.oa.com/cloud-msp/migration-lister/pkg/conf"

	"git.code.oa.com/cloud-msp/migration-lister/pkg/lister"
	secapi "git.code.oa.com/sec-api/go/scurl"
	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/aws/credentials"
	"github.com/aws/aws-sdk-go/aws/session"
	"github.com/aws/aws-sdk-go/service/s3"
	"go.uber.org/zap"
)

// s3ObjectLister iterate over all objects in a QiNiu Bucket.
type s3ObjectLister struct {
	accessKey string
	secretKey string
	bucket    string
	endpoint  string
	svc       *s3.S3
	region    string
}

// NewS3Lister S3 兼容模式 quick/slow 遍历.
//
// param endpoint: 访问 Bucket 的 endpoint, 如果传空字符串, 会使用 Region 参数查询 endpoint.
// NOTE: endpoint 和 Region 至少提供其一.
func NewS3Lister(
	accessKeyID string,
	secretAccessKey string,
	bucket string,
	endpoint string,
	region string,
) (*s3ObjectLister, error) {

	// use static credential AK/SK pair
	cred := credentials.NewStaticCredentials(accessKeyID, secretAccessKey, "")

	cfg := &aws.Config{
		Credentials:      cred,
		Region:           aws.String("virtual-region"),
		S3ForcePathStyle: aws.Bool(true),
		DisableSSL:       aws.Bool(true),
	}

	if region != "" {
		cfg.Region = aws.String(region)
	}

	if endpoint == "" {
		return nil, errors.New("invalid argument: no endpoint provided")
	}

	// use user input endpoint first, look up by Region if not specified
	cfg.Endpoint = aws.String(endpoint)

	var testEndpoint = endpoint

	// parse endpoint
	if strings.HasPrefix(endpoint, "http") {
		if strings.HasPrefix(endpoint, "https") {
			cfg.DisableSSL = aws.Bool(false)
		}

		u, err := url.Parse(endpoint)
		if err != nil {
			return nil, err
		}

		cfg.Endpoint = aws.String(u.Host)
	} else {
		testEndpoint = "http://" + endpoint
	}
	// 内部的模式才做安全检测
	if conf.ListerConfig.ListerInfo.Mode == conf.InnerMode {
		secapi.WithAllowPorts([]string{"allow_all"})
		secCli := secapi.NewSafeClient()
		_, err := secCli.Get(testEndpoint)
		if err != nil {
			return nil, fmt.Errorf("ssrf warning > invalid endpoint: %v can not get public ip > %v ", testEndpoint, err)
		}
	}

	if strings.HasPrefix(*cfg.Endpoint, bucket+".") {
		cfg.S3ForcePathStyle = aws.Bool(false)
		cfg.Endpoint = aws.String(strings.Replace(*cfg.Endpoint, bucket+".", "", 1))
	}

	sess, err := session.NewSession(cfg)
	if err != nil {
		return nil, err
	}

	svc := s3.New(sess)

	return &s3ObjectLister{
		accessKey: accessKeyID,
		secretKey: secretAccessKey,
		bucket:    bucket,
		endpoint:  endpoint,
		svc:       svc,
		region:    region,
	}, nil
}

// List 遍历 bucket 内的文件, slow/quick list 的逻辑通过 handler 实现
// 循环直到所有对象遍历完或者重试次数超过上限
// nolint
func (l *s3ObjectLister) List(
	ctx context.Context,
	taskId string,
	prefix string,
	marker string,
	fragFileNum int, // 分片文件数上限
	fragMaxSize int, // 分片总大小上限
	handler lister.ResultHandler,
	logger *zap.Logger,
	filters ...lister.ObjectFilter,
) (err error) {

	logger = logger.With(
		zap.String("platform", "S3Protocol"),
		zap.String("Bucket", l.bucket),
		zap.String("endpoint", l.endpoint),
		zap.String("prefix", prefix))

	logger.Info("start listing objects",
		zap.Int("fragFileNum", fragFileNum), zap.Int("fragMaxSize", fragMaxSize))

	result := &lister.Result{TaskId: taskId, Prefix: prefix}

	// loop until all pages processed or max retry reached
	var retry = lister.MaxRetry

	for {
		if retry <= 0 {
			logger.Error("max retry limit reached")
			return
		}

		select {
		case <-ctx.Done():
			// no need to retry on cancellation
			logger.Info("listing objects canceled")
			return ctx.Err()
		default: // no-op
		}

		logger.Info("listing objects", zap.String("marker", marker))

		input := &s3.ListObjectsInput{
			Bucket:       aws.String(l.bucket),
			EncodingType: aws.String("url"),
			Marker:       aws.String(marker),
			MaxKeys:      aws.Int64(int64(lister.DefaultPageSize)),
			Prefix:       aws.String(prefix),
		}
		var page *s3.ListObjectsOutput
		page, err = l.svc.ListObjectsWithContext(ctx, input)

		if err != nil {
			logger.Error(
				"s3.ListObjectsWithContext returned error",
				zap.Error(err),
				zap.Int("retry", retry),
				zap.String("marker", marker))
			retry -= 1
			time.Sleep(lister.RetryInterval)
			continue
		}

		if page == nil {
			logger.Error("s3.ListObjectsWithContext responded nil", zap.String("marker", marker))
			// trigger retry
			err = fmt.Errorf("s3.ListObjectsWithContext responded nil")
			retry -= 1
			time.Sleep(lister.RetryInterval)
			continue
		}

		// consume page
		var lastKey string
		for _, content := range page.Contents {
			object := toListerObject(content)
			lastKey = *content.Key
			if !lister.FilterObject(object, filters...) {
				result.Skip++
			} else {
				result.Contents = append(result.Contents, object)
				result.Num += 1
				result.Size += int(object.Size)
			}
			// 符合条件的文件不足一个分片 && 跳过的文件不足一个分片
			if result.Num < fragFileNum && result.Size < fragMaxSize && result.Skip < fragFileNum {
				continue
			}

			// consume result, handler 自己负责重试
			if page.Marker != nil {
				result.Marker = *page.Marker
			} else {
				result.Marker = ""
				logger.Warn("page marker is nil", zap.Any("marker", marker))
			}
			if handlerErr := handler(taskId, result); handlerErr != nil {
				logger.Error("result handler error", zap.Error(handlerErr), zap.Int("retry", retry))
			}
			// 提交过以后清理, 防止计数重复累加
			result.Rest()
		}

		if page.NextMarker != nil {
			marker, err = url.QueryUnescape(*page.NextMarker)
			if err != nil {
				logger.Error("url.QueryUnescape(page.NextMarker)",
					zap.Error(err),
					zap.Any("page.NextMarker", page.NextMarker))
				err = fmt.Errorf("invalid URL escape in key \"%s\"", *page.NextMarker)
				return
			}
		} else {
			marker, err = url.QueryUnescape(lastKey)
			if err != nil {
				logger.Error("url.QueryUnescape(lastKey)",
					zap.Error(err),
					zap.Any("lastKey", lastKey))
				err = fmt.Errorf("invalid URL escape in key \"%s\"", lastKey)
				return
			}
		}

		// no page left, stop iterating
		if page.IsTruncated == nil {
			err = fmt.Errorf("page.IsTruncated is nil, %s", page.String())
			logger.Error("page.IsTruncated is nil", zap.Error(err))
			return
		}
		if !*page.IsTruncated {
			logger.Info("listing objects finished")
			if len(result.Contents) == 0 && result.Skip == 0 {
				return nil
			}

			// 如果还有剩下的一波提交
			if page.Marker != nil {
				result.Marker = *page.Marker
			} else {
				result.Marker = ""
				logger.Warn("page marker is nil", zap.Any("marker", marker))
			}
			if handlerErr := handler(taskId, result); handlerErr != nil {
				logger.Error(
					"result handler error (last batch)", zap.Error(handlerErr), zap.Int("retry", retry))
			}

			return nil
		}
		// 成功就重置最大重试次数
		retry = lister.MaxRetry
	}
}

// qiniu S3 兼容模式返回的 object metadata 转成统一的 Object 类型
func toListerObject(vo *s3.Object) *lister.Content {
	obj := lister.Content{
		Key:          *vo.Key,
		LastModified: *vo.LastModified,
		Size:         *vo.Size,
	}
	return &obj
}
