package s3protocol

import (
	"context"
	"fmt"

	"git.code.oa.com/cloud-msp/migration-lister/pkg/lister"
	"github.com/prashantv/gostub"
	"go.hlw.tencent.com/alego/components/log"
)

func ExampleS3ProtocolLister_List() {
	stubs := gostub.Stub(&lister.DefaultPageSize, 10)
	defer stubs.Reset()

	accessKey := ""
	secretKey := ""
	bucket := "0-100g-1255980596"
	endpoint := "cos.ap-chengdu.myqcloud.com"
	region := "ap-chengdu"
	jobId := "kodo-s3-quick-list-full"
	logger := log.GetLogger()

	ol, err := NewS3Lister(accessKey, secretKey, bucket, endpoint, region)
	if err != nil {
		fmt.Printf("quick list err=%v", err)
		return
	}

	var size int
	var cnt int
	resultHandler := func(jobId string, result *lister.Result) error {
		size += result.Size
		cnt += result.Num
		return nil
	}

	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()
	err = ol.List(ctx, jobId, "", "", 0, 0, resultHandler, logger)
	if err != nil {
		fmt.Printf("quick list err=%v", err)
	}

	fmt.Printf("size=%d, cnt=%d\n", size, cnt)
	// Output: size=115589266, cnt=62
}
