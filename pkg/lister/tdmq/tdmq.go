package tdmq

import (
	"context"
	"encoding/json"
	"fmt"
	"os"
	"strconv"
	"strings"
	"sync"
	"time"

	"git.code.oa.com/cloud-msp/migration-lister/pkg/defaultCosClient"
	"git.code.oa.com/cloud-msp/migration-lister/tools"
	"git.code.oa.com/pulse-line/pl_boot"
	"github.com/apache/pulsar-client-go/pulsar"
	"github.com/syndtr/goleveldb/leveldb"
	"github.com/syndtr/goleveldb/leveldb/util"
	"github.com/tencentyun/cos-go-sdk-v5"

	"git.code.oa.com/cloud-msp/migration-lister/pkg/lister"
	"go.uber.org/zap"
)

const cosTimeLayout = time.RFC3339
const levelTimeLayout = "200601021504"
const prefix = "migrate/increase/"

type FormatMsg struct {
	MessageId string `json:"messageId"`
	JobId     string `json:"jobId"`
	Key       string `json:"key"`
	Time      string `json:"time"`
	Size      int    `json:"size"`
	Action    string `json:"action"`
	Bucket    string `json:"bucket"`
}

type IncrInfo struct {
	Region   string `json:"region"`
	Endpoint string `json:"endpoint"`
	Topic    string `json:"topic"`
	SubName  string `json:"subName"`

	PulsarServiceURL     string `json:"pulsarServiceURL"`
	PulsarAuthentication string `json:"pulsarAuthentication"`
}

type tdmqObjectLister struct {
	ctx      context.Context
	jobId    string
	client   pulsar.Client
	consumer pulsar.Consumer
	logger   *zap.Logger
	db       *leveldb.DB
	incrInfo *IncrInfo
}

func formatPulsarMessageID(mid pulsar.MessageID) string {
	return fmt.Sprintf("%d:%d:%d:%d", mid.LedgerID(), mid.EntryID(), mid.BatchIdx(), mid.PartitionIdx())
}

// 消费mq, 按时间槽放到写到本地levelDb
func (c *tdmqObjectLister) preList() error {
	go func() {
		var wg sync.WaitGroup
		for i := 0; i < 1; i++ {
			wg.Add(1)
			go func() {
				defer wg.Done()
				tools.RunWithRecover(func() {
					for {
						time_start := time.Now()
						m, err := c.consume()
						if err != nil {
							time.Sleep(time.Second)
							continue
						}
						key := time.Now().Format(levelTimeLayout) + "-" + m.Key
						ms, _ := json.Marshal(m)
						err = c.db.Put([]byte(key), ms, nil)
						// TODO 上报每分钟的消费统计
						if err != nil {
							c.logger.Warn("put to leveldb err", zap.Any("m", m), zap.Error(err))
							continue
						}
						time_cost := time.Now().Sub(time_start).Milliseconds()
						c.logger.Info("consume time cost", zap.Any("msgID", m.MessageId), zap.Any("time(ms)", time_cost))
					}
				})
			}()
		}
		wg.Wait()
	}()

	return nil
}

func (c *tdmqObjectLister) consume() (m *FormatMsg, err error) {
	// 消费消息
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()
	msg, err := c.consumer.Receive(ctx)
	if err == nil {
		c.consumer.Ack(msg) // 确认消息

		m = &FormatMsg{}
		err = json.Unmarshal(msg.Payload(), m)
		if err != nil {
			c.logger.Info("json decode message err", zap.Error(err))
			return
		}
		m.MessageId = formatPulsarMessageID(msg.ID())
		c.logger.Info("c.consume recv-ack success", zap.Any("msgID", m.MessageId))
		return m, nil
	} else {
		c.logger.Info("c.consume recv-ack error", zap.Error(err))
		return
	}
}

// 从preList dump到本地后，遍历出文件
// TODO prefix在这里不使用，改造成前缀正则匹配
func (c *tdmqObjectLister) List(
	ctx context.Context,
	taskId string,
	prefix string,
	marker string,
	fragFileNum int, // 分片文件数上限
	fragMaxSize int, // 分片总大小上限
	handler lister.ResultHandler,
	logger *zap.Logger,
	filters ...lister.ObjectFilter,
) (err error) {
	logger = logger.With(
		zap.String("platform", "COS"), zap.String("prefix", prefix))

	logger.Info("start listing objects",
		zap.Int("fragFileNum", fragFileNum), zap.Int("fragMaxSize", fragMaxSize))

	result := &lister.Result{TaskId: taskId, Prefix: prefix}
	nowMinute := time.Now().Format(levelTimeLayout)
	markerMinute := strings.Split(marker, "-")[0]
	if markerMinute == "" {
		markerMinute = time.Now().Add(-60 * time.Minute).Format(levelTimeLayout) // 最多遍历当前时间的前一小时数据
	}
	logger.Info("tdmq 1",
		zap.Any("marker", marker),
		zap.Any("pre markerMinute", markerMinute),
		zap.Any("nowMinute", nowMinute),
	)
	for {
		select {
		case <-ctx.Done():
			// no need to retry on cancellation
			logger.Info("listing objects canceled")
			return ctx.Err()
		default: // no-op
		}

		logger.Info("listing objects", zap.String("marker", marker))
		nowMinute = time.Now().Format(levelTimeLayout)
		nowTime, _ := time.Parse(levelTimeLayout, nowMinute)
		markTime, _ := time.Parse(levelTimeLayout, markerMinute)
		if nowTime.Before(markTime.Add(time.Minute)) {
			time.Sleep(60 * time.Second)
			continue
		}
		var seekMarker = true
		if marker == "" {
			seekMarker = false
		}
		var markerKey string
		if marker != "" {
			markerKey = strings.Join(strings.Split(marker, "-")[1:], "-")
		}
		logger.Info("tdmq 2",
			zap.Any("marker", marker),
			zap.Any("markerKey", markerKey),
			zap.Any("pre markerMinute", markerMinute),
			zap.Any("nowMinute", nowMinute),
		)
		// marker和时间戳算法
		// 等待时间比较久没写入的话要退出遍历等待
		iter := c.db.NewIterator(util.BytesPrefix([]byte(markerMinute)), nil)
		var timeSlotOver = true
		var totalSize = 0
		var contents = make([]FormatMsg, 0)
		if seekMarker {
			for ok := iter.Seek([]byte(marker)); ok; ok = iter.Next() {
				// Use key/value.
				k := iter.Key()
				v := iter.Value()
				logger.Info("tdmq 3",
					zap.Any("marker", marker),
					zap.Any("markerKey", markerKey),
					zap.Any("pre markerMinute", markerMinute),
					zap.Any("nowMinute", nowMinute),
					zap.Any("iter has key", string(k)),
					zap.Any("iter has v", string(v)),
				)
				vv := FormatMsg{}
				err = json.Unmarshal(v, &vv)
				if err != nil {
					c.logger.Error("json decode err", zap.Error(err),
						zap.Any("key", string(k)),
						zap.Any("value", string(v)))
					continue
				}

				contents = append(contents, vv)
				totalSize += vv.Size
				if len(contents) < fragFileNum && totalSize < fragMaxSize {
					continue
				} else {
					break
				}
			}
		} else {
			for iter.Next() {
				// Use key/value.
				k := iter.Key()
				v := iter.Value()
				logger.Info("tdmq 4",
					zap.Any("marker", marker),
					zap.Any("markerKey", markerKey),
					zap.Any("pre markerMinute", markerMinute),
					zap.Any("nowMinute", nowMinute),
					zap.Any("iter has key", string(k)),
					zap.Any("iter has v", string(v)),
				)
				vv := FormatMsg{}
				err = json.Unmarshal(v, &vv)
				if err != nil {
					c.logger.Error("json decode err", zap.Error(err),
						zap.Any("key", string(k)),
						zap.Any("value", string(v)))
					continue
				}

				contents = append(contents, vv)
				totalSize += vv.Size
				if len(contents) < fragFileNum && totalSize < fragMaxSize {
					continue
				} else {
					break
				}
			}
		}
		if iter.Next() {
			timeSlotOver = false // iter还没把当前时间槽遍历完
		}
		if len(contents) > 0 {
			marker = fmt.Sprintf("%s-%s", markerMinute, contents[len(contents)-1].Key)
			// 更新 marker 到下一字典序, 否则下一轮循环时会重复处理到边界
			markerbuf := []byte(marker)
			markerbuf = append(markerbuf, '\x00')
			marker = string(markerbuf)
		}
		logger.Info("tdmq 5",
			zap.Any("marker", marker),
			zap.Any("markerKey", markerKey),
			zap.Any("pre markerMinute", markerMinute),
			zap.Any("nowMinute", nowMinute),
			zap.Any("iter end", "end"),
		)
		iter.Release()
		err = iter.Error()
		if err != nil {
			logger.Error("failed to list objects",
				zap.Error(err), zap.String("marker", marker))
			time.Sleep(lister.RetryInterval)
			continue
		}
		// TODO minute啥时候遍历到结尾，minute写入阻塞
		// 处理存储服务的响应
		for _, content := range contents {
			object, err := toListerObject(content)
			if err != nil {
				logger.Warn("failed to parse object metadata", zap.Error(err))
				continue
			}

			if !lister.FilterObject(object, filters...) {
				result.Skip++
				continue
			}

			// result.Marker用作定时备份
			result.Marker = fmt.Sprintf("%s-%s", markerMinute, object.Key)
			result.Contents = append(result.Contents, object)
			result.Num += 1
			result.Size += int(object.Size)
			if result.Num < fragFileNum && result.Size < fragMaxSize { // 不足一个分片
				continue
			}

			// consume result, handler 自己负责重试
			if handlerErr := handler(taskId, result); handlerErr != nil {
				logger.Error("result handler error", zap.Error(handlerErr))
			}
			// 提交过以后清理, 防止计数重复累加
			result.Rest()
		}
		// 提交不足一个分片的余量
		if len(result.Contents) > 0 {
			if handlerErr := handler(taskId, result); handlerErr != nil {
				logger.Error("result handler error", zap.Error(handlerErr))
			}
			result.Rest()
		}
		// 只有当前time slot内的消息全部遍历完, 才能遍历下一个time slot
		markerMinuteTime, _ := time.Parse(levelTimeLayout, markerMinute)
		if timeSlotOver {
			markerMinute = markerMinuteTime.Add(time.Minute).Format(levelTimeLayout)
		}
		logger.Info("tdmq 6",
			zap.Any("marker", marker),
			zap.Any("markerKey", markerKey),
			zap.Any("markerMinute", markerMinute),
			zap.Any("nowMinute", nowMinute),
			zap.Any("for end", "end"),
			zap.Any("markerMinuteTime", markerMinuteTime),
			zap.Any("timeSlotOver", timeSlotOver),
		)
	}
}

// list完的存档上传到cos, 并本地删除
func (c *tdmqObjectLister) subList(prefixMinute int64) {
	f, _ := os.OpenFile(fmt.Sprintf("/tmp/%s/%d", c.jobId, prefixMinute), os.O_CREATE, 0777)
	iter := c.db.NewIterator(util.BytesPrefix([]byte(strconv.FormatInt(prefixMinute, 10))), nil)
	for iter.Next() {
		k := iter.Key()
		v := iter.Value()
		vv := FormatMsg{}
		err := json.Unmarshal(v, &vv)
		if err != nil {
			c.logger.Error("json decode err", zap.Error(err),
				zap.Any("key", string(k)),
				zap.Any("value", string(v)))
			continue
		}
		_, _ = f.WriteString(fmt.Sprintf("%s,%d,%s\n", vv.Key, vv.Size, vv.Time))
	}
	_ = f.Close()
	iter.Release()
	_, _, _ = defaultCosClient.GetClient().Object.Upload(context.Background(),
		fmt.Sprintf("%s/%s/%s/%d", prefix, c.jobId, "raw", prefixMinute),
		fmt.Sprintf("/tmp/%s/%d", c.jobId, prefixMinute), &cos.MultiUploadOptions{})
}

// TODO 删除

// COS 存储返回的 object metadata 转成统一的 Object 类型
func toListerObject(vo FormatMsg) (*lister.Content, error) {
	lastModified, err := time.Parse(cosTimeLayout, vo.Time)
	if err != nil {
		return nil, err
	}
	obj := lister.Content{
		Key:          vo.Key,
		LastModified: lastModified,
		Size:         int64(vo.Size),
	}
	return &obj, nil
}

// NewCOSLister 新建一个 lister, 用于 COS 存储 quick/slow 遍历.
func NewTdmqLister(ctx context.Context, jobId, secretID, secretKey string, incrInfo string,
	db *leveldb.DB, isQuick bool) (*tdmqObjectLister,
	error) {

	incr := &IncrInfo{}
	err := json.Unmarshal([]byte(incrInfo), incr)
	if err != nil {
		return nil, err
	}

	tl := &tdmqObjectLister{
		ctx:      ctx,
		jobId:    jobId,
		incrInfo: incr,
		logger:   pl_boot.AppCtx.GetAppLogger().With(zap.String("jobId", jobId)),
		db:       db,
	}

	if isQuick {
		// 创建pulsar客户端和消费者的逻辑要放在 isQuick 代码块里, 否则两个 client/consumer 会出问题

		// 创建pulsar客户端
		client, err := pulsar.NewClient(pulsar.ClientOptions{
			// 服务接入地址
			URL: incr.PulsarServiceURL,
			// URL: "http://pulsar-9x8p4w84o9r7.tdmq-pulsar.ap-sh.public.tencenttdmq.com:8080", // devcloud开发机上使用公网接入地址
			// 授权角色密钥
			Authentication:    pulsar.NewAuthenticationToken(incr.PulsarAuthentication),
			OperationTimeout:  30 * time.Second,
			ConnectionTimeout: 30 * time.Second,
		})
		if err != nil {
			return nil, err
		}

		// 使用客户端创建消费者
		consumer, err := client.Subscribe(pulsar.ConsumerOptions{
			// topic完整路径，格式为persistent://集群（租户）ID/命名空间/Topic名称
			Topic: "persistent://" + incr.Topic,
			// 订阅名称
			SubscriptionName: incr.SubName,
			// 订阅模式
			Type: pulsar.Shared,
		})
		if err != nil {
			return nil, err
		}

		tl.client = client
		tl.consumer = consumer

		err = tl.preList()
		if err != nil {
			return nil, err
		}
	}
	return tl, nil
}
