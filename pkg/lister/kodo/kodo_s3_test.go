package kodo

import (
	"context"
	"fmt"

	"git.code.oa.com/cloud-msp/migration-lister/pkg/lister"
	"github.com/prashantv/gostub"
	"go.hlw.tencent.com/alego/components/log"
)

func ExampleKodoS3Lister_List() {
	stubs := gostub.Stub(&lister.DefaultPageSize, 10)
	defer stubs.Reset()

	accessKey := ""
	secretKey := ""
	bucket := "clientlog"
	region := "z0"
	endpoint := "7xic2q.s3-cn-east-1.qiniucs.com"
	jobId := "kodo-s3-quick-list-full"
	logger := log.GetLogger()

	ol, _ := NewKODOS3Lister(accessKey, secretKey, bucket, region, endpoint)

	var size int
	var cnt int
	resultHandler := func(jobId string, result *lister.Result) error {
		size += result.Size
		cnt += result.Num
		return nil
	}

	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()
	err := ol.List(ctx, jobId, "", "", 0, 0, resultHandler, logger)
	if err != nil {
		fmt.Printf("quick list err=%v", err)
	}

	fmt.Printf("size=%d, cnt=%d\n", size, cnt)
	// Output: size=115589266, cnt=62
}
