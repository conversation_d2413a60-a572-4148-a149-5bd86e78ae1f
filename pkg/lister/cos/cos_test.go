package cos

import (
	"context"
	"fmt"
	"github.com/dlclark/regexp2"
	"io/ioutil"
	"testing"

	"git.code.oa.com/cloud-msp/migration-lister/internal/service/models"
	"git.code.oa.com/cloud-msp/migration-lister/pkg/lister"
	"git.code.oa.com/cloud-msp/migration-lister/tools"
	"github.com/prashantv/gostub"
	"github.com/tencentyun/cos-go-sdk-v5"
	"go.hlw.tencent.com/alego/components/log"
	"go.uber.org/zap"
)

// 初始化客户端参数检查
func TestNewCOSLister(t *testing.T) {
	var bucket = "bucket"
	var region = ""
	_, err := NewCOSLister("", "", bucket, region)
	if err == nil {
		t.Errorf("should return err")
	}
}

// 全量遍历检查 cnt 和 size
// NOTE: 如果桶发生变化会导致测试失败
func Example_cosObjectLister_List() {
	stubs := gostub.Stub(&lister.DefaultPageSize, 2000)
	defer stubs.Reset()

	fragFileNum := 5000
	fragMaxSize := 10 * 1000 * 1000 * 1000 // 10G
	ak := ""
	sk := ""
	bucket := "xl-small-file-1255980596"
	region := "ap-chengdu"
	jobId := "cos-full-list"
	logger := log.GetLogger()

	var hasTimeFilter int32 = 1
	var fileEndTime int32 = 1616342400
	var fileStartTime int32 = 1616169600
	jobInfo := &tools.JobInfo{HasFileTimeFilter: &hasTimeFilter, FileEndTime: &fileEndTime,
		FileStartTime: &fileStartTime}

	task := models.NewTask()
	task.JobInfo = jobInfo
	startT, endT := task.GetTimeSpan()
	filter := func(object *lister.Content) bool {
		ts := object.LastModified
		if ts.Before(endT) && ts.After(startT) {
			return true
		}
		return false
	}

	cl, _ := NewCOSLister(ak, sk, bucket, region)

	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	var size, cnt, fragCnt int
	resultHandler := func(taskId string, result *lister.Result) error {
		size += result.Size
		cnt += result.Num
		fragCnt++
		return nil
	}
	err := cl.List(ctx, jobId, "pdd/types", "", fragFileNum, fragMaxSize, resultHandler, logger, filter)

	if err != nil {
		fmt.Printf("quick list err=%v\n", err)
	}

	fmt.Printf("size=%d, cnt=%d, fragCnt=%d\n", size, cnt, fragCnt)
	// OUTPUT: size=19922944000, cnt=19000, fragCnt=4
}

// 第一次请求之前 ctx 被取消
func Test_cosObjectLister_List_canceled(t *testing.T) {
	stubs := gostub.Stub(&lister.DefaultPageSize, 200)
	defer stubs.Reset()

	ak := ""
	sk := ""
	bucket := "small-1251997323"
	region := "ap-nanjing"
	jobId := "cos-cancel"
	logger := log.GetLogger()

	cl, _ := NewCOSLister(ak, sk, bucket, region)
	ctx, cancel := context.WithCancel(context.Background())
	cancel()

	resultHandler := lister.NopPageHandler

	err := cl.List(ctx, jobId, "", "", 0, 0, resultHandler, logger)
	if err != context.Canceled {
		t.Errorf("expect context.Canceled, got=%v", err)
	}
}

// 遍历中途 ctx 被取消
// NOTE: 如果桶发生变化会导致测试失败
func Test_ossObjectLister_List_canceled_mid(t *testing.T) {

	fragFileNum := 1000
	fragMaxSize := 10 * 1000 * 1000 * 1000 // 10G
	ak := ""
	sk := ""
	bucket := "xl-small-file-1255980596"
	region := "ap-chengdu"
	jobId := "cos-cancel"
	logger := log.GetLogger()

	cl, _ := NewCOSLister(ak, sk, bucket, region)
	ctx, cancel := context.WithCancel(context.Background())

	var size, cnt int
	resultHandler := func(taskId string, result *lister.Result) error {
		size += result.Size
		cnt += result.Num
		logger.Info("progress", zap.Int("size", size), zap.Int("cnt", cnt))
		cancel()
		return nil
	}

	err := cl.List(ctx, jobId, "", "", fragFileNum, fragMaxSize, resultHandler, logger)
	if err != context.Canceled {
		t.Errorf("expect context.Canceled, got=%v", err)
	}
}

// NOTE: head object.key = "" 对象时, COS 响应 status=200, Content-length=0, 响应 body 也是空的
func Test_emptyKey(t *testing.T) {

	ak := ""
	sk := ""
	bucket := "vrlab-public-1254236265"
	region := "ap-beijing"

	cl, _ := NewCOSLister(ak, sk, bucket, region)
	name := ""
	opt := &cos.ObjectHeadOptions{}
	resp, err := cl.client.Object.Head(context.Background(), name, opt)
	if err != nil {
		t.Fatal(err)
	}
	t.Log(resp.StatusCode, resp.ContentLength)
	content, _ := ioutil.ReadAll(resp.Body)
	t.Log(string(content))
}

func Test_reg(t *testing.T) {
	a := `^(?!c\/product\/).*`
	r, err := regexp2.Compile(a, 0)
	if err != nil {
		fmt.Println(err)
	}

	m, err := r.MatchString("c/product/xxx")
	fmt.Println(m, err)
	n, err := r.MatchString("c/pxx")
	fmt.Println(n, err)
}
