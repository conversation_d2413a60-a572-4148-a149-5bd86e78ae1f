package tokenbucket

import (
	"math"
	"sync"
	"time"
)

// TokenBucket
type TokenBucket interface {
	AsyncTakeN(n int64) int64
	UpdateRate(rate int64)
}

type tokenBucket struct {
	mu          sync.Mutex
	tps         int64 //token per second
	max         int64 // 最大
	token       int64 // 当前
	lastUpdated time.Time
}

// AsyncTakeN
func (t *tokenBucket) AsyncTakeN(n int64) (r int64) {
	t.mu.Lock()
	defer t.mu.Unlock()

	duration := time.Now().Sub(t.lastUpdated)
	diff := math.Floor(duration.Seconds() * float64(t.tps))
	// pl_boot.AppCtx.GetAppLogger().Info("AsyncTakeNInfo1",
	// 	zap.Any("tps", t.tps),
	// 	zap.Any("lastUpdated", t.lastUpdated.Nanosecond()),
	// 	zap.Any("duration", duration),
	// 	zap.Any("diff", diff),
	// )
	abs := math.Abs(diff)
	if abs >= 1.0 {
		added := math.Floor(abs)
		rounded := time.Duration(math.Floor(added * float64(time.Second) / math.Abs(float64(t.tps))))
		t.lastUpdated = t.lastUpdated.Add(rounded)
		if diff > 0 {
			t.token += int64(added)
		} else {
			t.token -= int64(added)
		}

		if t.token > t.max {
			t.token = t.max
		}

		if t.token < 0 {
			t.token = 0
		}

		// pl_boot.AppCtx.GetAppLogger().Info("AsyncTakeNInfo2",
		// 	zap.Any("tps", t.tps),
		// 	zap.Any("lastUpdated", t.lastUpdated.Nanosecond()),
		// 	zap.Any("duration", duration),
		// 	zap.Any("diff", diff),
		// 	zap.Any("rounded", rounded),
		// 	zap.Any("n", n),
		// 	zap.Any("token", t.token),
		// 	zap.Any("max", t.max),
		// )

		if t.token > n {
			t.token -= n
			r = n
		} else {
			r = t.token
			t.token = 0
		}

	}

	return
}

// UpdateRate
func (t *tokenBucket) UpdateRate(rate int64) {
	t.mu.Lock()
	defer t.mu.Unlock()

	old := t.tps
	if old == rate {
		return
	}

	t.tps = rate
	t.max = int64(float64(t.tps) * 1.02)
}

// Config
type Config struct {
	MaxTokens       int64
	InitialTokens   int64
	TokensPerSecond int64
}

// New
func New(config *Config) TokenBucket {
	return &tokenBucket{
		tps:         config.TokensPerSecond,
		max:         config.MaxTokens,
		token:       config.InitialTokens,
		lastUpdated: time.Now(),
	}
}
