package tokenbucket

import (
	"sync"
	"testing"
	"time"
)

func TestTokenBucket_AsyncTakeN(t *testing.T) {
	tb := New(&Config{
		MaxTokens:       1,
		InitialTokens:   1,
		TokensPerSecond: 1,
	})

	wg := sync.WaitGroup{}
	for i := 1; i < 100; i++ {
		wg.Add(1)
		go func(i int) {
			defer wg.Done()
			start := time.Now()

			var n int64
			for {
				n = tb.AsyncTakeN(int64(1))
				if n == 0 {
					time.Sleep(1 * time.Second)
				} else {
					break
				}
			}

			t.Logf("go %d take %d cost %f", i, n, time.Now().Sub(start).Seconds())
		}(i)
	}
	wg.Wait()
}
