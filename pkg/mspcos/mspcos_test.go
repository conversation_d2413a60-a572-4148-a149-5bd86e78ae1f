package failfile

import (
	"context"
	"fmt"
	"testing"

	"git.code.oa.com/cloud-msp/migration-lister/pkg/conf"
	"git.code.oa.com/cloud-msp/migration-lister/pkg/lister"
	"github.com/prashantv/gostub"
	"go.hlw.tencent.com/alego/components/log"
	"go.uber.org/zap"
)

// 全量遍历检查 cnt 和 size, 每个分块 1M
// NOTE: 如果任务删除或者账号变化会导致测试失败
func Example_failFileLister_ListFromCosFile_1M() {
	// 每次获取 1M
	stubs := gostub.Stub(&partLimit, 1024*1024)
	stubs.Stub(&conf.ListerConfig.GlobalConf.FragMaxNum, 3000) // 分片大小3000
	stubs.Stub(&conf.ListerConfig.GlobalConf.FragMaxSize, int64(1000*1000*1000*100))

	defer stubs.Reset()

	secretID := ""
	secretKey := ""
	appId := "1255980596"
	bucket := "migrate"
	region := "ap-guangzhou"
	jobId := "fm-aq56kfdv"
	logger := log.GetLogger()

	cl, _ := newMSPCOSLister(secretID, secretKey, bucket, region, appId)

	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	var size, cnt, fragCnt int
	resultHandler := func(taskId string, result *lister.Result) error {
		size += result.Size
		cnt += result.Num
		fragCnt++
		logger.Info("report result", zap.String("marker", result.Marker))
		return nil
	}
	err := cl.ListFromCosFile(ctx, jobId, "", "", resultHandler, logger)

	if err != nil {
		fmt.Printf("quick list err=%v\n", err)
	}
	fmt.Printf("size=%d, cnt=%d, fragCnt=%d\n", size, cnt, fragCnt)
	// Output: size=80518737747, cnt=446063, fragCnt=149
}

// 全量遍历检查 cnt 和 size, 每个分块 50M
// NOTE: 如果任务删除或者账号变化会导致测试失败
func Example_failFileLister_ListFromCosFile_20M() {
	stubs := gostub.Stub(&partLimit, 20*1024*1024)
	stubs.Stub(&conf.ListerConfig.GlobalConf.FragMaxNum, 1000) // 分片大小3000
	stubs.Stub(&conf.ListerConfig.GlobalConf.FragMaxSize, int64(1000*1000*1000*100))

	secretID := ""
	secretKey := ""
	appId := "1255980596"
	bucket := "migrate"
	region := "ap-guangzhou"
	jobId := "fm-aq56kfdv"
	logger := log.GetLogger()

	cl, _ := newMSPCOSLister(secretID, secretKey, bucket, region, appId)

	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	var size, cnt, fragCnt int
	resultHandler := func(taskId string, result *lister.Result) error {
		size += result.Size
		cnt += result.Num
		fragCnt++
		logger.Info("report result", zap.String("marker", result.Marker))
		return nil
	}
	err := cl.ListFromCosFile(ctx, jobId, "", "", resultHandler, logger)

	if err != nil {
		fmt.Printf("quick list err=%v\n", err)
	}
	fmt.Printf("size=%d, cnt=%d, fragCnt=%d\n", size, cnt, fragCnt)
	// Output: size=80518737747, cnt=446063, fragCnt=447
}

func Test_getLatestFailedFile_canceled(t *testing.T) {
	secretID := ""
	secretKey := ""
	appId := "1255980596"
	bucket := "migrate"
	region := "ap-guangzhou"
	jobId := "fm-aq56kfdv"
	cl, _ := newMSPCOSLister(secretID, secretKey, bucket, region, appId)

	ctx, cancel := context.WithCancel(context.Background())
	cancel()

	fileName, totalLen, err := getLatestFailedFile(ctx, cl.client, jobId, 3)
	if err != context.Canceled {
		t.Errorf("expect context.Canceled, got %v", err)
	}
	if fileName != "" {
		t.Errorf("should return empty string, got %s", fileName)
	}
	if totalLen != 0 {
		t.Errorf("should return 0, got %d", totalLen)
	}
}

func Test_getLatestFailedFile_max_retry(t *testing.T) {
	secretID := ""
	secretKey := ""
	appId := "1255980596"
	bucket := "migrate"
	region := "ap-guangzhou"
	jobId := "fm-aq56kfdv"
	cl, _ := newMSPCOSLister(secretID, secretKey, bucket, region, appId)

	ctx := context.Background()

	fileName, totalLen, err := getLatestFailedFile(ctx, cl.client, jobId, 3)
	if err == nil {
		t.Errorf("expect non-nill err")
	}
	t.Log(err)

	if fileName != "" {
		t.Errorf("should return empty string, got %s", fileName)
	}
	if totalLen != 0 {
		t.Errorf("should return 0, got %d", totalLen)
	}
}
