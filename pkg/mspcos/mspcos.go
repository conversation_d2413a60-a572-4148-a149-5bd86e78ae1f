package failfile

import (
	"bytes"
	"context"
	"errors"
	"fmt"
	"io/ioutil"
	"net/http"
	"strconv"
	"strings"
	"time"

	"git.code.oa.com/cloud-msp/migration-lister/pkg/defaultCosClient"

	"git.code.oa.com/cloud-msp/migration-lister/pkg/conf"
	"git.code.oa.com/cloud-msp/migration-lister/pkg/lister"
	"github.com/tencentyun/cos-go-sdk-v5"
	"go.uber.org/zap"
)

// failFileLister 遍历任务的失败文件列表(failFile)
type failFileLister struct {
}

// 分块下载失败列表, 每块大小
var partLimit = lister.DownloadPartLimit

// COS bucket URL 模版
const failFilePathTemp = "failFiles/%s.csv"

// NewFailFilLister 为任务初始化 COS 客户端
func NewFailFilLister() (*failFileLister, error) {
	return newMSPCOSLister()
}

// 方便测试使用
func newMSPCOSLister() (*failFileLister, error) {
	return &failFileLister{}, nil
}

// 获取任务最近的失败文件列表 object.Key, object.Size
func getLatestFailedFile(
	ctx context.Context, client *cos.Client, taskId string, retry int,
) (latestFilePath string, fileSize int, err error) {

	prefix := fmt.Sprintf(failFilePathTemp, taskId)
	opt := &cos.BucketGetOptions{Prefix: prefix, MaxKeys: 1000}
	for retry > 0 {
		select {
		case <-ctx.Done(): // no need to retry on cancellation
			return "", 0, ctx.Err()
		default:
		}

		res, _, bucketErr := client.Bucket.Get(ctx, opt)
		if bucketErr != nil {
			err = bucketErr
			time.Sleep(lister.RetryInterval)
			retry--
			continue
		}

		// 处理成功的响应
		version := 0
		latestVersion := -1
		for _, value := range res.Contents {
			fileNameSplit := strings.Split(value.Key, ".")
			if len(fileNameSplit) > 2 {
				version, _ = strconv.Atoi(fileNameSplit[2])
			}
			if version > latestVersion {
				latestVersion = version
				latestFilePath = value.Key
				fileSize = int(value.Size)
			}
		}
		return latestFilePath, fileSize, nil
	}

	return "", 0, err
}

// 失败文件 CSV 的一行
// NOTE: 为了打桩测试
var toListerObject = func(line []byte) (*lister.Content, error) {
	fail := strings.Split(string(line), ",")
	if len(fail) < 2 {
		return nil, fmt.Errorf("malformed failfile line: %s", line)
	}

	key := fail[0]
	if len(key) == 0 {
		return nil, fmt.Errorf("empty object key: %s", line)
	}

	size, err := strconv.Atoi(fail[1])
	if err != nil {
		return nil, fmt.Errorf("invalid size: %s", line)
	}

	// 新版失败列表里uploadId位于最后一列. 如果是旧版失败列表, 从最后一列取到的uploadId无效, ListParts失败后初始化新的分块
	uploadId := fail[len(fail)-1]

	obj := lister.Content{
		Key:      key,
		Size:     int64(size),
		UploadId: uploadId,
	}
	return &obj, nil
}

// cos 客户端下载文件的一个分块 (Range header)
func fetchPartialContent(
	ctx context.Context,
	client *cos.Client,
	fileName string,
	offset int,
	limit int,
) (content []byte, err error) {

	opt := &cos.ObjectGetOptions{Range: fmt.Sprintf("bytes=%d-%d", offset, offset+limit-1)}
	resp, err := client.Object.Get(ctx, fileName, opt)
	if resp != nil {
		defer resp.Body.Close()
		content, _ = ioutil.ReadAll(resp.Body)
	}
	return
}

// Range 范围无法满足
func isHttp416Error(e error) bool {
	if e == nil {
		return false
	}
	err, ok := e.(*cos.ErrorResponse)
	if !ok {
		return false
	}
	if err.Response != nil && err.Response.StatusCode == http.StatusRequestedRangeNotSatisfiable {
		return true
	}
	return false
}

// 输入分解成行
// 模拟 Python string.splitlines 的行为
func splitLines(input []byte) [][]byte {
	lf := []byte("\n")
	// TODO: 更好的方法是是 bufio.Scanner + 自定义 SplitFunc
	input = bytes.Replace(input, []byte("\r\n"), lf, -1)
	input = bytes.Replace(input, []byte("\r"), lf, -1)
	return bytes.Split(input, lf)
}

// ListFromCosFile 遍历失败文件列表, 分块读取每次 50M
//nolint
func (m *failFileLister) ListFromCosFile(
	ctx context.Context,
	taskId string,
	prefix string,
	marker string,
	handler lister.ResultHandler,
	srcService string,
	logger *zap.Logger,
) (err error) {
	logger = logger.With(zap.String("jobId", taskId), zap.String("platform", "failFile(COS)"))
	var fragFileNum = conf.ListerConfig.GlobalConf.FragMaxNum
	var fragMaxSize = int(conf.ListerConfig.GlobalConf.FragMaxSize)
	logger.Info("start listing objects",
		zap.Int("fragFileNum", fragFileNum), zap.Int("fragMaxSize", fragMaxSize))

	fileName, totalLen, err := getLatestFailedFile(ctx, defaultCosClient.GetClient(), taskId, lister.MaxRetry)
	if err != nil {
		return fmt.Errorf("failed to head failFile, %v", err)
	}
	if totalLen == 0 {
		return errors.New("failed to head failFile, 0 failFile length")
	}

	result := &lister.Result{TaskId: taskId, Prefix: prefix} // 当前分块的处理结果
	// marker 存的是当前分块起始位置和分块处理到第几行
	cntMarker, marshalErr := lister.NewCountMarker([]byte(marker))
	if marshalErr != nil {
		logger.Warn("failed to parse marker, start from head", zap.Error(err))
	}
	offset := cntMarker.Offset
	cnt := cntMarker.Count

	var retry = lister.MaxRetry
	for {
		if retry <= 0 {
			logger.Error("max retry limit reached")
			return err
		}

		select {
		case <-ctx.Done(): // no need to retry on cancellation
			logger.Info("listing objects canceled")
			return ctx.Err()
		default:
		}

		logger.Info("listing objects", zap.Int("offset", offset), zap.Int("cnt", cnt))

		content, err := fetchPartialContent(ctx, defaultCosClient.GetClient(), fileName, offset, partLimit)
		if err != nil {
			if isHttp416Error(err) { // 这种情况下 resp 还有意义
				logger.Info(
					"COS file range not satisfied", zap.Int("offset", offset), zap.Int("cnt", cnt))
			} else {
				logger.Error(
					"failed to get COS file content", zap.Error(err), zap.Int("retry", retry),
					zap.Int("offset", offset), zap.Int("cnt", cnt))
				retry -= 1
				time.Sleep(lister.RetryInterval)
				continue // repeat this part
			}
		}

		partLen := len(content)
		var hasNext = true
		if partLen != partLimit {
			if partLen+offset == totalLen { // 到达文件末尾
				hasNext = false
			} else { // 分块不完整, 重试
				logger.Error("incomplete partial content",
					zap.Int("retry", retry), zap.Int("partLen", partLen),
					zap.Int("offset", offset), zap.Int("cnt", cnt))
				retry -= 1
				time.Sleep(lister.RetryInterval)
				continue // repeat this part
			}
		}

		lines := splitLines(content)
		lastLine := lines[len(lines)-1]                     // 最后一行可能不完整
		nextOffset := offset + len(content) - len(lastLine) // anyway, 调整 offset
		if hasNext {                                        // 根据 hasNext 判断分块最后一行是否保留
			lines = lines[:len(lines)-1]
		}

		// do for
		err1 := do(lines, cnt, logger, result, fragFileNum, fragMaxSize, offset, cntMarker, handler, taskId, srcService, retry)
		if err1 != nil {
			logger.Error("ssrf check error", zap.Error(err1))
			return err1
		}
		// no page left, stop iterating
		if !hasNext {
			logger.Info("listing objects finished")
			if len(result.Contents) == 0 {
				return nil
			}
			// 如果还有剩下的一波提交
			cntMarker.Offset = offset
			cntMarker.Count = cnt
			result.Marker = cntMarker.String()
			if handlerErr := handler(taskId, result); handlerErr != nil {
				logger.Error("result handler error (last batch)", zap.Error(handlerErr), zap.Int("retry", retry))
			}
			return nil
		}
		offset = nextOffset
		cnt = -1 // 第 0 行要放进来
	}
}
func do(lines [][]byte,
	cnt int,
	logger *zap.Logger,
	result *lister.Result,
	fragFileNum int,
	fragMaxSize int,
	offset int,
	cntMarker *lister.CountMarker,
	handler lister.ResultHandler,
	taskId string,
	srcService string,
	retry int) error {
	for index, line := range lines {
		if index <= cnt { // 跳过这个分块已经处理的行数
			continue
		}
		cnt = index

		object, err := toListerObject(line)
		if err != nil {
			logger.Warn("failed to parse object metadata", zap.Error(err))
			continue
		}
		// NOTE: 重试任务不需要 filter
		result.Contents = append(result.Contents, object)
		result.Num += 1
		result.Size += int(object.Size)
		if result.Num < fragFileNum && result.Size < fragMaxSize { // 不足一个分片
			continue
		}

		// consume result, handler 自己负责重试
		cntMarker.Offset = offset
		cntMarker.Count = cnt
		result.Marker = cntMarker.String()
		if handlerErr := handler(taskId, result); handlerErr != nil {
			logger.Error("result handler error", zap.Error(handlerErr), zap.Int("retry", retry))
		}
		// 提交过以后清理, 防止计数重复累加
		result.Rest()
	}
	return nil
}
