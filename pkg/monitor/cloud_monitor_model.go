package monitor

import (
	"sync"
)

/**
监控数据对象
*/
type MData struct {
	Data     map[string]int64
	DataLock *sync.Mutex
	PreData  map[string]int64
}

// BuildM 监控数据 model
type BuildM struct {
	Tags    map[string]string `json:"tags,omitempty"`
	Time    map[string]string `json:"time,omitempty"`
	Fields  map[string]string `json:"fields,omitempty"`
	Options map[string]string `json:"options,omitempty"`
}

// DBError 数据库错误
type DBError struct {
	Status int32             `json:"status,omitempty"`
	Error  map[string]string `json:"error,omitempty"`
}

// DBResult 数据库请求结果
type DBResult struct {
	Result map[string][]string `json:"result,omitempty"`
	Status int32               `json:"status"`
}

// HitInfo 查询命中情况
type HitInfo struct {
	Took         int32        `json:"took,omitempty"`
	TimeOut      bool         `json:"timed_out,omitempty"`
	Share        Share        `json:"_shards,omitempty"`
	HitList      Hits         `json:"hits,omitempty"`
	Aggregations Aggregations `json:"aggregations,omitempty"`
}

// Aggregations 结果聚合
type Aggregations struct {
	TimeAgg TimeAggRes `json:"timeAgg,omitempty"`
}

// TimeAggRes
type TimeAggRes struct {
	Buckets []BucketsInfo `json:"buckets,omitempty"`
}

// BucketsInfo 数据桶信息
type BucketsInfo struct {
	KeyAsString string           `json:"key_as_string,omitempty"`
	Key         int64            `json:"key,omitempty"`
	DocCount    int32            `json:"doc_count,omitempty"`
	AvgCpuUsage map[string]int64 `json:"avgCpuUsage,omitempty"`
}

// Share
type Share struct {
	Total      int32 `json:"total"`
	Successful int32 `json:"successful"`
	Skipped    int32 `json:"skipped"`
	Failed     int32 `json:"failed"`
}

// Hits
type Hits struct {
	Total    int32       `json:"total"`
	MaxScore float32     `json:"max_score"`
	Hits     []HitDetail `json:"hits"`
}

// HitDetail 命中详情
type HitDetail struct {
	Index   string `json:"_index,omitempty"`
	Type    string `json:"_type,omitempty"`
	Id      string `json:"_id,omitempty"`
	Score   string `json:"_score,omitempty"`
	Routing string `json:"_routing,omitempty"`
	Fields  Field  `json:"fields,omitempty"`
}

// go 不支持泛型 新增字段需要改结构体对象
type Field struct {
	Qps       []int32   `json:"qps,omitempty"`
	Mbps      []float32 `json:"mbps,omitempty"`
	Timestamp string    `json:"timestamp,omitempty"`
}

// worker 写操作的需要上报的监控数据
type WriteInfo struct {
	TaskId    string  `json:"taskId,omitempty"`
	Qsp       int64   `json:"Qsp,omitempty"`
	Mbps      float32 `json:"mbps,omitempty"`
	Timestamp int64   `json:"timestamp,omitempty"`
}

// worker 上报监控数据的结果
type WriteRes struct {
	Took   int32         `json:"took,omitempty"`
	Errors bool          `json:"errors,omitempty"`
	Items  []interface{} `json:"items,omitempty"`
}
