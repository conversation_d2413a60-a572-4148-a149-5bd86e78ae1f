package monitor

// Service worker 上报 metrics 数据的接口
type Service interface {
	/**
	work上报监控数据
	@param: taskId 任务Id
	@param: value  指标值
	@param: 指标名称 1）qps；2）tps；3）带宽；4）Qps
	*/
	Report(taskId string, value int64, name string)

	/**
	定时上报到云缉监控
	*/
	ScheduleReport()

	/**
	定时上报逻辑
	*/
	DoReport(taskId string, value int64, name string) error

	/**
	@param: 请求参数json对象
	发送请求
	写入数据到时序数据库
	*/
	DoPut(body string) error
	/**
	定时获取一分种内的请求
	*/
	ScheduleSnapshot()

	DoSnapshot()

	/**
	判断当前taskId是否存在时序数据库
	*/
	ExistMetrics(taskId string) (bool, error)

	/**
	当前taskId
	新建时序数据库
	*/
	BuildMetrics() error

	/**
	写入数据
	*/
	WriterData(taskId string, body string) (bool, error)
}
