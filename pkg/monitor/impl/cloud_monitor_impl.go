package impl

import (
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net/http"
	"runtime/debug"
	"strings"
	"sync"
	"time"

	"git.code.oa.com/cloud-msp/migration-lister/pkg/conf"
	"git.code.oa.com/cloud-msp/migration-lister/pkg/metrics"
	"git.code.oa.com/cloud-msp/migration-lister/pkg/monitor"
	"git.code.oa.com/cloud-msp/migration-lister/tools"
	"git.code.oa.com/pulse-line/pl_boot"
	"git.code.oa.com/pulse-line/pl_prom"
	"go.uber.org/zap"
)

const (
	ServiceName = "monitor"
	PUT         = "PUT"
	GET         = "GET"
	POST        = "POST"
	MetricsName = "msp_monitor"
	PutName     = "/doc/_bulk"
	Index       = `{"index":{"_routing": "gz" }}`
)

var (
	MonitorIns  *Handler
	monitorOnce sync.Once
)

// Handler 实现 Worker 上报监控数据 interface
type Handler struct {
	Cache           map[string]*monitor.MData   // 本地数据上报数据：key：taskId value：map[datatype]value
	Snapshot        map[string]map[string]int64 // 上报数据前一分钟快照
	Lock            *sync.Mutex                 // task 锁
	Client          *http.Client
	ExpireDay       int32
	RefreshInterval int32
	UserName        string
	Password        string
	Host            string
	Port            string
}

/**
获取监控monitor单例
*/
func GetMonitor() monitor.Service {
	monitorOnce.Do(func() {
		MonitorIns = &Handler{
			Cache:           make(map[string]*monitor.MData),
			Lock:            &sync.Mutex{},
			ExpireDay:       conf.ListerConfig.GlobalConf.ExpireDay,
			RefreshInterval: conf.ListerConfig.GlobalConf.RefreshInterval,
			UserName:        conf.ListerConfig.GlobalConf.CTSDBUser,
			Password:        conf.ListerConfig.GlobalConf.CTSDBPassword,
			Host:            conf.ListerConfig.GlobalConf.Host,
			Port:            conf.ListerConfig.GlobalConf.Port,
		}

		client := &http.Client{
			Transport: &http.Transport{
				MaxIdleConns: conf.ListerConfig.GlobalConf.HttpIdleConn,
			},
			Timeout: time.Duration(conf.ListerConfig.GlobalConf.HttpClientTimeOut) * time.Millisecond,
		}
		MonitorIns.Client = client
		// 任务调度
		MonitorIns.ScheduleReport()
	})
	return MonitorIns
}

/**
快照定时调度入口
*/
func (h *Handler) ScheduleSnapshot() {
	go func() {
		defer func() {
			if err := recover(); err != nil {
				pl_boot.AppCtx.GetAppLogger().Error(
					"ScheduleSnapshot recover error!",
					zap.Any("err", err),
					zap.Any("stack", string(debug.Stack()[:])))
			}
		}()
		pl_boot.AppCtx.GetAppLogger().Info("ScheduleSnapshot start")
		// 服务首次启动，可能没有及时的上报数据，所以休息一下
		time.Sleep(time.Second * time.Duration(60))
		for {
			h.DoSnapshot()
			// 间隔一分钟
			time.Sleep(time.Second * time.Duration(60))
		}
	}()
}

/**
快照调度逻辑
*/
func (h *Handler) DoSnapshot() {

	// 记录当前的数据，更新上一分的数据

	if h.Cache == nil {

		pl_boot.AppCtx.GetAppLogger().Warn("DoSnapshot cache is nil")

		return
	}

	if h.Snapshot == nil {

		h.Snapshot = make(map[string]map[string]int64)

	}

	for key, value := range h.Cache { // key: taskId value: *monitor.MData

		d := h.Cache[key]

		if d == nil { // 判断本地是否有对应task数据

			pl_boot.AppCtx.GetAppLogger().Warn("DoSnapshot task data  is nil",
				zap.String("taskId", key))

			continue
		}

		s := h.Snapshot[key]

		if nil == s { //  初始化当前task下快照map
			s = make(map[string]int64)
		}

		for k, v := range value.Data {
			if v != 0 { // 判断类型数值是否0
				pl_boot.AppCtx.GetAppLogger().Warn("DoSnapshot value.Data  is nil",
					zap.String("taskId", key), zap.String("type", k))
				continue
			}
			if value.PreData[k] == 0 { // 判断是否存在上一分钟的数据
				s[k] = v / 60 // 如果没有当前数据/60直接是QPS
			} else {
				s[k] = (v - value.PreData[k]) / 60 // 当前的请求总数-上一分钟的总数/60
			}
			// 更新上一分种的数值
			value.PreData[k] = v
			pl_boot.AppCtx.GetAppLogger().Warn("DoSnapshot value.Data  update finish",
				zap.String("taskId", key), zap.String("type", k))
		}
	}
}

/**
上报数据
*/
func (h *Handler) Report(taskId string, value int64, name string) {

	if h.Cache == nil { // 判断是否cache被初始化
		h.Cache = make(map[string]*monitor.MData)
	}

	prm := h.Cache[taskId]

	if prm == nil { // 判断task下是否已经有当前的值的

		d := &monitor.MData{
			Data:     make(map[string]int64),
			DataLock: &sync.Mutex{},
			PreData:  make(map[string]int64),
		}

		h.Lock.Lock()

		h.Cache[taskId] = d

		h.Lock.Unlock()
	}

	prm.DataLock.Lock()
	defer prm.DataLock.Unlock()
	// 累加数值的总数
	prm.Data[name] = prm.Data[name] + value
}

/**
定时调度上报入口
*/
func (h *Handler) ScheduleReport() {
	go func() {
		defer func() {
			if err := recover(); err != nil {
				pl_boot.AppCtx.GetAppLogger().Error(
					"ScheduleReport recover error!",
					zap.Any("err", err),
					zap.Any("stack", string(debug.Stack()[:])))
			}
		}()
		pl_boot.AppCtx.GetAppLogger().Info("ScheduleReport start")
		// 服务首次启动，可能没有及时的上报数据，所以休息一下
		time.Sleep(time.Second * time.Duration(120))
		for {
			for taskId, snap := range h.Snapshot {
				for name, value := range snap {
					h.DoReport(taskId, value, name)
				}
			}
		}
		time.Sleep(time.Second * time.Duration(65))
	}()
}

// DoReport 定时上报
func (h *Handler) DoReport(taskId string, value int64, name string) error {
	if value == 0 {
		return nil
	}

	var (
		info = &monitor.WriteInfo{
			TaskId:    taskId,
			Qsp:       value,
			Mbps:      0,
			Timestamp: time.Now().Unix(),
		}
		stb = strings.Builder{}
	)

	r, _ := json.Marshal(info)

	stb.WriteString(Index)
	stb.WriteString("\n")
	stb.WriteString(string(r))

	res, err := h.WriterData(taskId, stb.String())

	if nil != err {
		pl_boot.AppCtx.GetAppLogger().Error("monitor report err",
			zap.String("taskId", taskId),
			zap.String("name", name),
			zap.Int64("value", value),
			zap.Error(err))

		// 失败统计
		pl_prom.GetCounterWithValues(metrics.ErrorCount, metrics.Lister, "Report", taskId, "error").Inc()
		return err
	}

	if res {
		// 正常记录
		pl_boot.AppCtx.GetAppLogger().Info("monitor report suc ", zap.String("taskId", taskId),
			zap.String("name", name))
	}

	return nil
}

/**
调用时序数据库
*/
func (h *Handler) DoPut(body string) error {
	payload := strings.NewReader(body)

	hostIp := fmt.Sprintf("%s:%s/_metric/%s", h.Host, h.Port, MetricsName)

	req, _ := http.NewRequest(PUT, hostIp, payload)

	// 设置http请求参数
	req.Header.Add("Content-Type", "application/json")

	req.SetBasicAuth(h.UserName, h.Password)

	response, err := h.Client.Do(req)
	if nil != err {
		pl_boot.AppCtx.GetAppLogger().Error("monitor DoPut err",
			zap.Error(err))
		// 失败统计
		pl_prom.GetCounterWithValues(metrics.ErrorCount, metrics.Lister, "DoPut", "all", "error").Inc()
		return err
	}

	defer response.Body.Close()
	b, _ := ioutil.ReadAll(response.Body)

	//  响应结果
	pl_boot.AppCtx.GetAppLogger().Info("monitor DoPut suc ",
		zap.String("resp", string(b)))
	return nil
}

/**
判断是否已存在metrics
*/
func (h *Handler) ExistMetrics(taskId string) (bool, error) {

	url := fmt.Sprintf("%s:%s/_metrics", h.Host, h.Port)

	req, _ := http.NewRequest(GET, url, nil)

	// 设置http请求参数
	req.Header.Add("Content-Type", "application/json")

	req.SetBasicAuth(h.UserName, h.Password)

	response, err := h.Client.Do(req)
	if nil != err {

		pl_boot.AppCtx.GetAppLogger().Error("monitor ExistMetrics err",
			zap.String("taskId", taskId),
			zap.Error(err))
		// 失败统计

		pl_prom.GetCounterWithValues(metrics.ErrorCount, metrics.Lister, "ExistMetrics", taskId, "error").Inc()

		return false, err
	}
	bytes, err := ioutil.ReadAll(response.Body)
	if nil != err {
		pl_boot.AppCtx.GetAppLogger().Error("monitor ExistMetrics read response body err",
			zap.String("taskId", taskId),
			zap.Error(err))
		return false, err
	}
	result := &monitor.DBResult{}
	if err := json.Unmarshal(bytes, result); nil != err {
		pl_boot.AppCtx.GetAppLogger().Error("monitor ExistMetrics json unmarshal err",
			zap.String("taskId", taskId),
			zap.Error(err))
		return false, err
	}

	if result.Status != 200 {
		return false, nil
	}

	for _, v := range result.Result["metrics"] {
		if v == MetricsName {
			return true, nil
		}
	}

	return false, nil
}

/**
新建metrics
*/
func (h *Handler) BuildMetrics() error {

	build := &monitor.BuildM{
		Tags:    make(map[string]string),
		Time:    make(map[string]string),
		Fields:  make(map[string]string),
		Options: make(map[string]string),
	}

	/**
	tags： 维度列，用于唯一标识数据，须至少包含一个维度，支持的数据类型：text（带有分词、全文索引的字符串）、
	string（不分词的字符串）、long、integer、short、byte、double、float、date、boolean。
	格式如{"region": "string","set": "long","host": "string"}

	time： 时间列相关配置，用于存储数据入库的唯一时间，例如{"name": "timestamp", "format": "epoch_second"}，
	若不填则系统默认格式为{"name": "timestamp", "format": "epoch_millis"}

	fields： 数据列，用于存储数据，为了节省空间，建议使用最适合实际业务使用的类型，支持的数据类型：
	string（字符串）、long、integer、short、byte、double、float、date、boolean。
	例如{"cpu_usage":"float"}

	options：常用的调优配置信息 {"expire_day":7,"refresh_interval":"10s","number_of_shards":5,
	"number_of_replicas":1,"rolling_period":1,"max_string_length": 256,
	"default_date_format":"strict_date_optional_time","indexed_fields":["host"]}
	*/
	// 维度 taskId
	build.Tags["taskId"] = "string"
	// 时间
	build.Time["name"] = "timestamp"
	build.Time["format"] = "epoch_second"
	// 数值 cpu
	build.Fields["cpu"] = "long"
	// 数值 带宽
	build.Fields["mbps"] = "float"
	build.Options["expire_day"] = tools.Int32Strcon(h.ExpireDay)
	build.Options["refresh_interval"] = tools.Int32Strcon(h.RefreshInterval)

	body, _ := json.Marshal(build)
	if err := h.DoPut(string(body)); nil != err {
		return err
	}
	return nil
}

// WriterData 写入监控数据
func (h *Handler) WriterData(taskId string, content string) (bool, error) {

	url := fmt.Sprintf("%s:%s/%s/%s", h.Host, h.Port, MetricsName, PutName)

	payload := strings.NewReader(content)

	req, _ := http.NewRequest(POST, url, payload)

	req.SetBasicAuth(h.UserName, h.Password)

	// 设置http请求参数
	req.Header.Add("Content-Type", "application/json")

	response, err := h.Client.Do(req)
	if nil != err {
		pl_boot.AppCtx.GetAppLogger().Error("monitor DoPut err",
			zap.Error(err))
		// 失败统计
		pl_prom.GetCounterWithValues(metrics.ErrorCount, metrics.Lister, "DoPut", "all", "error").Inc()
		return false, err
	}

	defer response.Body.Close()
	b, _ := ioutil.ReadAll(response.Body)

	result := &monitor.WriteRes{}
	if err := json.Unmarshal(b, result); nil != err {
		pl_boot.AppCtx.GetAppLogger().Error("monitor WriterData json unmarshal err",
			zap.String("taskId", taskId),
			zap.Error(err))
		return false, err
	}
	if result.Errors {
		pl_boot.AppCtx.GetAppLogger().Warn("monitor WriterData fail",
			zap.String("taskId", taskId))
		return false, nil
	}
	return true, nil
}
