package monitor

import (
	"encoding/json"
	"fmt"
	"log"
	"strings"
	"testing"
	"time"

	"git.code.oa.com/cloud-msp/migration-lister/tools"
)

const Index = `{"index":{"_routing": "gz" }}`

// TestJson
func TestJson(t *testing.T) {

	build := &BuildM{
		Tags:    make(map[string]string),
		Time:    make(map[string]string),
		Fields:  make(map[string]string),
		Options: make(map[string]string),
	}

	build.Tags["taskId"] = "string"
	// 时间
	build.Time["name"] = "timestamp"
	build.Time["format"] = "epoch_second"
	// 数值 cpu
	build.Fields["cpu"] = "long"
	// 数值 带宽
	build.Fields["mbps"] = "float"
	build.Options["expire_day"] = tools.Int32Strcon(11)
	build.Options["refresh_interval"] = tools.Int32Strcon(12)

	body, _ := json.Marshal(build)
	fmt.Println(string(body))

	str := `{"took":1,"timed_out":false,"_shards":{"total":5,"successful":5,"skipped":0,"failed":0},"hits":{"total":0,"max_score":null,"hits":[]}}`
	info := &HitInfo{
		Took:    0,
		TimeOut: false,
		Share: Share{
			Total:      0,
			Successful: 0,
			Skipped:    0,
			Failed:     0,
		},
		HitList: Hits{
			Total:    0,
			MaxScore: 0,
			Hits:     nil,
		},
		Aggregations: Aggregations{
			TimeAgg: TimeAggRes{
				Buckets: nil,
			},
		},
	}
	e := json.Unmarshal([]byte(str), info)
	if nil != e {
		log.Fatal(e)
	}

	body, _ = json.Marshal(info)
	fmt.Println(info)
	fmt.Println(string(body))

	m := `{
    "result": 
    {
        "metrics": 
        [
            "ctsdb_test",
            "ctsdb_test1"
        ]
    },
    "status": 200
	}`

	result := &DBResult{}
	e = json.Unmarshal([]byte(m), result)
	fmt.Println(result.Result["metrics"])

	winfo := &WriteInfo{
		TaskId:    "taskId",
		Qsp:       64,
		Mbps:      0,
		Timestamp: time.Now().Unix(),
	}
	stb := strings.Builder{}
	r, _ := json.Marshal(winfo)
	stb.WriteString(Index)
	stb.WriteString("\n")
	stb.WriteString(string(r))
	fmt.Println(stb.String())
}
