package sqlite

import (
	"database/sql"
	"git.code.oa.com/pulse-line/pl_boot"
	_ "github.com/mattn/go-sqlite3"
	"go.uber.org/zap"
)

var (
	waDb *sql.DB
)

func Init() (err error) {
	waDb, err = sql.Open("sqlite3", "file:workerAdmin.db?cache=shared&mode=memory")
	if err != nil {
		pl_boot.AppCtx.GetAppLogger().Fatal("OpenWorkerAdminDbError", zap.Error(err))
		return err
	}
	// 创建两个表
	// 1、worker表
	sqlTable1 := `CREATE TABLE WorkerAdmin (
		"WorkerAddr" varchar(50) NOT NULL,
		"Available" int NOT NULL,
		"IsStke" int NOT NULL,
		"Region" varchar(50) NOT NULL
    )`
	// 2、speed记录表
	sqlTable2 := `CREATE TABLE SpeedAdmin (
		"WorkerAddr" varchar(50) NOT NULL,
		"JobId" varchar(50) NOT NULL,
		"SpeedAllocated" int NOT NULL,
		"SpeedInUse" int NOT NULL,
		"UpdateTime" int NOT NULL
	)`
	_, err = waDb.Exec(sqlTable1)
	if err != nil {
		pl_boot.AppCtx.GetAppLogger().Fatal("CreateTableWorkerAdminError", zap.Error(err),
			zap.String("sqlTable1", sqlTable1))
		return err
	}
	_, err = waDb.Exec(sqlTable2)
	if err != nil {
		pl_boot.AppCtx.GetAppLogger().Fatal("CreateTableSpeedAdminError", zap.Error(err),
			zap.String("sqlTable1", sqlTable1))
		return err
	}
	// TODO 3、 task相关表
	return nil
}

// sql操作全部以函数方式封装，直接调用

// 更新worker记录
func UpdateWorker() {

}

// 删除worker记录
func DelWorker() {

}

// 更新speed分配记录
func UpdateSpeed() {

}

// 删除speed分配记录
func DelSpeed() {

}

// 根据JobId查询速度分配情况
func GetSpeedByJobId() {

}

// 根据workerAddr查询任务速度分配信息
func GetSpeedByWorkerAddr() {

}
