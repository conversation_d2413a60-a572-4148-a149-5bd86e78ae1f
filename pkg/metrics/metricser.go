package metrics

import (
	"git.code.oa.com/pulse-line/pl_prom"
)

const (
	/**
	metrics name
	*/
	TaskQpsCount = "task_qps" // 任务qps

	ErrorCount     = "task_error_count"     // 失败数统计
	ExceptionCount = "exception_task_count" // 异常状态任务统计

	RpcHistogram = "rpc_performance_histogram"

	RunningGauge = "task_run_gauge" // 正在运行的task个数
	/**
	first label
	*/
	Lister = "lister"

	Worker = "work"
)

func init() {

	/**
	业务统计
	@param
	model: 业务模块包括lister和worker
	action: 方法名称
	Id:taskId
	code:接口状态码
	*/
	_ = pl_prom.RegisterCounter(TaskQpsCount, "model", "action", "Id", "Code") // QPS统计

	_ = pl_prom.RegisterGauge(RunningGauge, "model", "action", "Id", "Code") // 运行活动统计

	/**
	失败统计
	初始化不指定label，业务方调用时指定
	如果是第一次访问标签值的组合，则会创建一个新的计数器
	@param
	model: 业务模块包括lister和worker
	action: 方法名称
	Id:taskId
	code:接口状态码
	*/
	_ = pl_prom.RegisterCounter(ExceptionCount, "model", "action", "Id", "Code") // 异常任务状态

	_ = pl_prom.RegisterCounter(ErrorCount, "model", "action", "Id", "Code") // 接口失败统计

	/**
		下游服务:T90,T95,T99,T999性能指标
		初始化不指定label，业务方调用时指定所属业务以及Api名称
		如果是第一次访问标签值的组合，则会创建一个新的计数器
	    @param
		model: 业务模块包括lister和worker
		action: 方法名称
		Id:taskId
		code:接口状态码
	*/
	_ = pl_prom.RegisterHistogram(RpcHistogram, []float64{0.90, 0.95, 0.99, 0.999}, "model", "action", "Id", "Code")

}
