package tools

import (
	"strings"
	"time"

	"git.code.oa.com/cloud-msp/migration-lister/api"
	"git.code.oa.com/pulse-line/pl_boot"
	"go.uber.org/zap"
)

const Layout = "2006/01/02 15:04"

func DumpFailedFile(result *api.FragResult) string {
	var res strings.Builder
	location, _ := time.ParseInLocation(Layout, time.Now().Format("2006/01/02 15:04"),
		time.Local)
	curr := location.Format(Layout)
	pl_boot.AppCtx.GetAppLogger().Info("DumpFailedFile",
		zap.Int("len", len(result.FailedFiles)),
		zap.Int64("fragId", result.FragId),
		zap.String("taskId", result.TaskId),
	)
	for id, file := range result.FailedFiles {
		res.WriteString(file.FullPath)
		res.WriteString(",")
		res.WriteString(Int64Strcon(file.Size))
		res.WriteString(",")
		res.WriteString(curr)
		res.WriteString(",")
		res.WriteString(Escape(file.FailedReason))
		res.WriteString(",")
		res.WriteString(file.UploadId)
		res.WriteString("\n")
		pl_boot.AppCtx.GetAppLogger().Info("DumpFailedFile failFile",
			zap.Int64("fragId", result.FragId),
			zap.Int64("fileId", id),
			zap.String("fileName", file.FullPath),
			zap.String("taskId", result.TaskId),
		)
	}
	return res.String()
}
