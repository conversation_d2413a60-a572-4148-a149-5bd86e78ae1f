package tools

import (
	"archive/tar"
	"bytes"
	"compress/gzip"
	"crypto/md5"
	"encoding/gob"
	"fmt"
	"io"
	"os"
	"path/filepath"
	"strconv"
	"strings"
	"time"
)

const (
	ZeroValue   = 0
	BitSize32   = 32
	BitSize64   = 64
	TwoDecimal  = 2
	DecimalType = 'f' // -ddd.ddd, no exponent
	EmptyStr    = ""
)

func StrConInt(str string) int {
	if "" == str {
		return ZeroValue
	}
	result, err := strconv.Atoi(str)
	if err != nil {
		return ZeroValue
	}
	return result
}

func StrConInt32(str string) int32 {
	if "" == str {
		return ZeroValue
	}
	result, err := strconv.Atoi(str)
	if err != nil {
		return ZeroValue
	}
	return int32(result)
}

func StrconInt64(str string) int64 {
	if "" == str {
		return ZeroValue
	}
	result, err := strconv.Atoi(str)
	if err != nil {
		return ZeroValue
	}
	return int64(result)
}

func Int64Strcon(value int64) string {
	return strconv.Itoa(int(value))
}

func IntStrcon(value int) string {
	return strconv.Itoa(value)
}

func Int32Strcon(value int32) string {
	return strconv.Itoa(int(value))
}

func StrconFloat32(str string) float32 {
	if "" == str {
		return ZeroValue
	}
	result, err := strconv.ParseFloat(str, BitSize32)
	if err != nil {
		return ZeroValue
	}
	return float32(result)
}

func Float32Strcon(float float32) string {
	return strconv.FormatFloat(float64(float), DecimalType, TwoDecimal, BitSize32)
}

func Float64Strcon(float float64) string {
	return strconv.FormatFloat(float, DecimalType, TwoDecimal, BitSize64)
}

func Decimal(value float64) float64 {
	value, _ = strconv.ParseFloat(fmt.Sprintf("%.2f", value), BitSize64)
	return value
}

// map value string 类型断言
func MapAssertString(maps map[string]interface{}, key string) string {
	if nil == maps {
		return EmptyStr
	}
	value, ok := maps[key].(string)
	if !ok {
		return EmptyStr
	}
	return value
}

// 打包tar
func Tar(src, dst string, dirLevel int) (err error) {
	fw, err := os.Create(dst)
	if err != nil {
		return
	}
	defer func() {
		_ = fw.Close()
	}()

	gw := gzip.NewWriter(fw)
	defer func() {
		_ = gw.Close()
	}()

	tw := tar.NewWriter(gw)

	defer func() {
		_ = tw.Close()
	}()

	return filepath.Walk(src, func(fileName string, fi os.FileInfo, err error) error {
		if err != nil {
			return err
		}

		hdr, err := tar.FileInfoHeader(fi, "")
		if err != nil {
			return err
		}

		hdr.Name = strings.TrimPrefix(fileName, string(filepath.Separator))

		levels := strings.Split(hdr.Name, string(filepath.Separator))

		if len(levels) > dirLevel {
			hdr.Name = strings.Join(levels[(len(levels)-dirLevel):], string(filepath.Separator))
		}

		if err := tw.WriteHeader(hdr); err != nil {
			return err
		}

		if !fi.Mode().IsRegular() {
			return nil
		}

		fr, err := os.Open(fileName)
		defer func() {
			_ = fr.Close()
		}()
		if err != nil {
			return err
		}

		_, err = io.Copy(tw, fr)
		if err != nil {
			return err
		}

		return nil
	})
}

func DeepCopy(dst, src interface{}) error {
	var buf bytes.Buffer
	if err := gob.NewEncoder(&buf).Encode(src); err != nil {
		return err
	}
	return gob.NewDecoder(bytes.NewBuffer(buf.Bytes())).Decode(dst)
}

func Escape(str string) string {
	if "" == str {
		return str
	}
	str = strings.Replace(str, "\r", "\\r", -1)
	str = strings.Replace(str, "\n", "\\n", -1)
	str = strings.Replace(str, "\r\n", "\\r\\n", -1)
	return str
}

func Md5ToHex(src []byte) string {
	sum := md5.Sum([]byte(src))
	return fmt.Sprintf("%x", sum)
}

func Md5ToHexUpper(src []byte) string {
	sum := md5.Sum(src)
	return fmt.Sprintf("%X", sum)
}

func UpdateHeart() time.Time {
	return time.Now()
}

func MinInt(a int, b int) int {
	if a <= b {
		return a
	}
	return b
}

func MinInt32(a int32, b int32) int32 {
	if a <= b {
		return a
	}
	return b
}
