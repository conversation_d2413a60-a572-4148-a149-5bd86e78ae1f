package tools

import (
	"context"
	"fmt"
	"testing"
)

// 解码空串加密后的结果
func Test_DecryptSecret_empty(t *testing.T) {
	decoded, err := DecryptSecret("")
	if err != nil {
		t.<PERSON><PERSON><PERSON>("failed to decod, err=%v", err)
	}
	t.Logf("origin input=%q", decoded)
}

func Test_Enc(t *testing.T) {
	enc, err := EncryptSecret("")
	fmt.Println(enc, err)
}

func Test_atomic(t *testing.T) {
	a := make(map[string]int)
	a["xx"] = a["xx"] + 1
	fmt.Println(a)
}

func Test_ctx(t *testing.T) {
	ctx1, cancel1 := context.WithCancel(context.Background())
	go func(ctx context.Context) {
		for {
			select {
			case <-ctx.Done():
				fmt.Println("cancel")
			default:
			}
		}
	}(ctx1)
	cancel1()
	select {}
}
