package tools

import (
	"encoding/json"

	tchttp "github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common/http"
	"go.hlw.tencent.com/goat-genapis/goatapis/msp"
)

// 任务进度 resp
type MAReportJobProgressResponse struct {
	*tchttp.BaseResponse
	Response *struct {
		Result *struct {
			Code    *string `json:"Code,omitempty" name:"Code"`
			Message *string `json:"Message,omitempty" name:"Message"`
		} `json:"Result" name:"Result"`

		// 唯一请求 ID，每次请求都会返回。定位问题时需要提供该次请求的 RequestId。
		RequestId *string `json:"RequestId,omitempty" name:"RequestId"`
	} `json:"Response"`
}

// Marshall to JSON string
func (r *MAReportJobProgressResponse) ToJsonString() string {
	b, _ := json.Marshal(r)
	return string(b)
}

// Unmarshall from JSON string
func (r *MAReportJobProgressResponse) FromJsonString(s string) error {
	return json.Unmarshal([]byte(s), &r)
}

// 更新任务失败文件存储状态
type MAReportJobProgressRequest struct {
	*tchttp.BaseRequest
	// 任务ID
	JobId *string `json:"JobId,omitempty" name:"JobId"`
	// 迁移agent汇总进度
	Summary *WorkerSummary `json:"Summary,omitempty" name:"Summary"`
	// 迁移agent 各个agent信息
	WorkersInfo []*msp.WorkerInfo `json:"WorkersInfo,omitempty" name:"WorkersInfo" list`
}

// Marshall to JSON string
func (r *MAReportJobProgressRequest) ToJsonString() string {
	b, _ := json.Marshal(r)
	return string(b)
}

// Unmarshall from JSON string
func (r *MAReportJobProgressRequest) FromJsonString(s string) error {
	return json.Unmarshal([]byte(s), &r)
}

// worker 下载进度状态汇总
type WorkerSummary struct {
	FileSuccessNumTotal  *int64 `json:"FileSuccessNumTotal,omitempty" name:"FileSuccessNumTotal"`
	FileSuccessSizeTotal *int64 `json:"FileSuccessSizeTotal,omitempty" name:"FileSuccessSizeTotal"`
	FileFailNumTotal     *int64 `json:"FileFailNumTotal,omitempty"  name:"FileFailNumTotal"`
	FileFailSizeTotal    *int64 `json:"FileFailSizeTotal,omitempty" name:"FileFailSizeTotal"`
}

// worker info
type WorkerInfo struct {
	Ip                   *string `json:"Ip,omitempty" name:"Ip"`
	IsLeader             *int    `json:"IsLeader,omitempty" name:"IsLeader"`
	AgentStatus          *int    `json:"AgentStatus,omitempty"  name:"AgentStatus"`
	LimitSpeed           *int64  `json:"LimitSpeed,string,omitempty" name:"LimitSpeed"`
	Port                 *string `json:"Port,omitempty"  name:"Port"`
	IsNew                *int    `json:"IsNew,omitempty" name:"IsNew"`
	FileSuccessNumTotal  *int64  `json:"FileSuccessNumTotal,string,omitempty" name:"FileSuccessNumTotal"`
	FileSuccessSizeTotal *int64  `json:"FileSuccessSizeTotal,string,omitempty" name:"FileSuccessSizeTotal"`
	FileFailNumTotal     *int64  `json:"FileFailNumTotal,string,omitempty"  name:"FileFailNumTotal"`
	FileFailSizeTotal    *int64  `json:"FileFailSizeTotal,string,omitempty" name:"FileFailSizeTotal"`
}

// 更新任务失败文件存储状态 req
type MAReportJobResultStoreStatusReq struct {
	*tchttp.BaseRequest
	// 任务ID
	JobId string `json:"JobId,omitempty" name:"JobId"`
	// 状态，对应msp.StoreStatus
	Status string `json:"Status,omitempty" name:"Status"`
}

// Marshall to JSON string
func (r *MAReportJobResultStoreStatusReq) ToJsonString() string {
	b, _ := json.Marshal(r)
	return string(b)
}

// Unmarshall from JSON string
func (r *MAReportJobResultStoreStatusReq) FromJsonString(s string) error {
	return json.Unmarshal([]byte(s), &r)
}

// 更新任务失败文件存储状态
type MAReportJobResultStoreStatusResponse struct {
	*tchttp.BaseResponse
	Response *struct {
		Result *struct {
			Code    *string `json:"Code,omitempty" name:"Code"`
			Message *string `json:"Message,omitempty" name:"Message"`
		} `json:"Result" name:"Result"`

		// 唯一请求 ID，每次请求都会返回。定位问题时需要提供该次请求的 RequestId。
		RequestId *string `json:"RequestId,omitempty" name:"RequestId"`
	} `json:"Response"`
}

// Marshall to JSON string
func (r *MAReportJobResultStoreStatusResponse) ToJsonString() string {
	b, _ := json.Marshal(r)
	return string(b)
}

// Unmarshall from JSON string
func (r *MAReportJobResultStoreStatusResponse) FromJsonString(s string) error {
	return json.Unmarshal([]byte(s), &r)
}

// 获取任务列表 req
type MAListMigrationJobsV2Request struct {
	*tchttp.BaseRequest

	// 任务ID
	JobId *string `json:"JobId,omitempty" name:"JobId"`
}

// Marshall to JSON string
func (r *MAListMigrationJobsV2Request) ToJsonString() string {
	b, _ := json.Marshal(r)
	return string(b)
}

// Unmarshall from JSON string
func (r *MAListMigrationJobsV2Request) FromJsonString(s string) error {
	return json.Unmarshal([]byte(s), &r)
}

// 获取任务列表
type MAListMigrationJobsV2Response struct {
	*tchttp.BaseResponse
	Response *struct {
		JobsInfo []*JobInfo `json:"JobsInfo" name:"JobsInfo" list`

		// 唯一请求 ID，每次请求都会返回。定位问题时需要提供该次请求的 RequestId。
		RequestId *string `json:"RequestId,omitempty" name:"RequestId"`
	} `json:"Response"`
}

// 迁移任务配置
type JobInfo struct {
	AgentInfo           *string `json:"AgentInfo" name:"AgentInfo"`
	DirectConnectInfo   *string `json:"DirectConnectInfo" name:"DirectConnectInfo"`
	DstBucket           *string `json:"DstBucket"  name:"DstBucket"`
	DstRegion           *string `json:"DstRegion" name:"DstRegion"`
	DstSecretID         *string `json:"DstSecretId" name:"DstSecretId"`
	DstSecretKey        *string `json:"DstSecretKey" name:"DstSecretKey"`
	FileEndTime         *int32  `json:"FileEndTime" name:"FileEndTime"`
	FileOverWrite       *int    `json:"FileOverWrite" name:"FileOverWrite"`
	FileStartTime       *int32  `json:"FileStartTime" name:"FileStartTime"`
	HasFileTimeFilter   *int32  `json:"HasFileTimeFilter" name:"HasFileTimeFilter"`
	JobID               *string `json:"JobId" name:"JobId"`
	JobProgress         *string `json:"JobProgress" name:"JobProgress"`
	JobStatus           *string `json:"JobStatus" name:"JobStatus"`
	LimitQPS            *int    `json:"LimitQPS" name:"LimitQPS"`
	LimitSpeed          *int    `json:"LimitSpeed" name:"LimitSpeed"`
	SpeedLimitInfo      *string `json:"SpeedLimitInfo" name:"SpeedLimitInfo"`
	MigrationHeader     *string `json:"MigrationHeader" name:"MigrationHeader"`
	MigrationHeaderType *int    `json:"MigrationHeaderType" name:"MigrationHeaderType"`
	MigrationRule       *string `json:"MigrationRule" name:"MigrationRule"`
	MigrationRuleType   *int    `json:"MigrationRuleType" name:"MigrationRuleType"`
	PathConf            *int    `json:"PathConf" name:"PathConf"`
	Priority            *int    `json:"Priority" name:"Priority"`
	ChannelType         *int    `json:"ChannelType" name:"ChannelType"`
	SavePath            *string `json:"SavePath" name:"SavePath"`
	SrcBucket           *string `json:"SrcBucket" name:"SrcBucket"`
	SrcFileName         *string `json:"SrcFileName" name:"SrcFileName"`
	SrcFileURL          *string `json:"SrcFileUrl" name:"SrcFileUrl"`
	SrcRegion           *string `json:"SrcRegion" name:"SrcRegion"`
	SrcSecretID         *string `json:"SrcSecretId" name:"SrcSecretId"`
	SrcSecretKey        *string `json:"SrcSecretKey" name:"SrcSecretKey"`
	SrcService          *string `json:"SrcService" name:"SrcService"`
	Status              *int    `json:"Status" name:"Status"`
	StorageType         *int    `json:"StorageType" name:"StorageType"`
	UserAppID           *string `json:"UserAppId" name:"UserAppId"`
	ManifestURL         *string `json:"ManifestURL" name:"ManifestURL"`
	BucketType          *string `json:"BucketType" name:"BucketType"`
	KodoEndpoint        *string `json:"KodoEndpoint" name:"KodoEndpoint"`
	IncrSrcInfo         *string `json:"IncrSrcInfo" name:"IncrSrcInfo"`
	IsIncrease          *int    `json:"IsIncrease" name:"IsIncrease"`
	NewHeaders          *string `json:"NewHeaders" name:"NewHeaders"`
}

// 加入通道的主机 IP
type ChannelIp struct {
	Ip          string `json:"Ip" name:"Ip"`
	ChannelType int    `json:"ChannelType" name:"ChannelType"`
}

// 获取通道中的主机列表 req
type MADescribeChannelIpRequest struct {
	*tchttp.BaseRequest
}

// Marshall to JSON string
func (r *MADescribeChannelIpRequest) ToJsonString() string {
	b, _ := json.Marshal(r)
	return string(b)
}

// Unmarshall from JSON string
func (r *MADescribeChannelIpRequest) FromJsonString(s string) error {
	return json.Unmarshal([]byte(s), &r)
}

// 获取通道中的主机列表
type MADescribeChannelIpResponse struct {
	*tchttp.BaseResponse
	Response *struct {
		Ips []*ChannelIp `json:"Ips" name:"Ips" list`

		// 唯一请求 ID，每次请求都会返回。定位问题时需要提供该次请求的 RequestId。
		RequestId *string `json:"RequestId,omitempty" name:"RequestId"`
	} `json:"Response"`
}

// 更新任务状态 req
type MAReportJobStatusRequest struct {
	*tchttp.BaseRequest
	// 任务ID
	JobId *string `json:"JobId,omitempty" name:"JobId"`
	// 任务状态
	Status *string `json:"Status,omitempty" name:"Status"`
}

// Marshall to JSON string
func (r *MAReportJobStatusRequest) ToJsonString() string {
	b, _ := json.Marshal(r)
	return string(b)
}

// Unmarshall from JSON string
func (r *MAReportJobStatusRequest) FromJsonString(s string) error {
	return json.Unmarshal([]byte(s), &r)
}

// 获取任务状态 resp
type MAReportJobStatusResponse struct {
	*tchttp.BaseResponse
	Response *struct {
		Result *struct {
			Code    *string `json:"Code,omitempty" name:"Code"`
			Message *string `json:"Message,omitempty" name:"Message"`
		} `json:"Result" name:"Result"`

		// 唯一请求 ID，每次请求都会返回。定位问题时需要提供该次请求的 RequestId。
		RequestId *string `json:"RequestId,omitempty" name:"RequestId"`
	} `json:"Response"`
}

// Marshall to JSON string
func (r *MAReportJobStatusResponse) ToJsonString() string {
	b, _ := json.Marshal(r)
	return string(b)
}

// Unmarshall from JSON string
func (r *MAReportJobStatusResponse) FromJsonString(s string) error {
	return json.Unmarshal([]byte(s), &r)
}

/**
 * 检索任务状态 请求参数
 */
type MADescribeJobInfoRequest struct {
	*tchttp.BaseRequest
	// 任务ID
	JobId *string `json:"JobId,omitempty" name:"JobId"`
}

// Marshall to JSON string
func (r *MADescribeJobInfoRequest) ToJsonString() string {
	b, _ := json.Marshal(r)
	return string(b)
}

// Unmarshall from JSON string
func (r *MADescribeJobInfoRequest) FromJsonString(s string) error {
	return json.Unmarshal([]byte(s), &r)
}

/**
 * 检索任务状态返回参数
 */
type MADescribeJobInfoResponse struct {
	*tchttp.BaseResponse
	Response *struct {
		JobInfo *MAJobAgentInfo `json:"JobInfo" name:"JobInfo" `
		// 唯一请求 ID，每次请求都会返回。定位问题时需要提供该次请求的 RequestId。
		RequestId *string `json:"RequestId,omitempty" name:"RequestId"`
	} `json:"Response"`
}

// Marshall to JSON string
func (r *MADescribeJobInfoResponse) ToJsonString() string {
	b, _ := json.Marshal(r)
	return string(b)
}

// Unmarshall from JSON string
func (r *MADescribeJobInfoResponse) FromJsonString(s string) error {
	return json.Unmarshal([]byte(s), &r)
}

// Worker(agent) info
type MAJobAgentInfo struct {
	Status *string `json:"Status,omitempty" name:"Status"`

	JobId *string `json:"JobId,omitempty" name:"JobId"`

	WorkersInfo []*WorkerInfo `json:"WorkersInfo,omitempty" name:"WorkersInfo"`

	JobProgress *string `json:"JobProgress,omitempty" name:"JobProgress"`
}

/**
 * 上报遍历结果 请求参数
 */
type MAReportJobSliceInfoRequest struct {
	*tchttp.BaseRequest
	// 任务ID
	JobId *string `json:"JobId,omitempty" name:"JobId"`
	//任务名称
	JobName string `json:"JobName,omitempty" name:"JobName"`
	//分片数量
	PidNum string `json:"PidNum,omitempty" name:"PidNum"`
	//文件数量
	FileNum string `json:"FileNum,omitempty" name:"FileNum"`
	//总大小（字节）
	Size string `json:"Size,omitempty"  name:"FileNum"`
}

// Marshall to JSON string
func (r *MAReportJobSliceInfoRequest) ToJsonString() string {
	b, _ := json.Marshal(r)
	return string(b)
}

// Unmarshall from JSON string
func (r *MAReportJobSliceInfoRequest) FromJsonString(s string) error {
	return json.Unmarshal([]byte(s), &r)
}

/**
 * 上报遍历结果返回参数
 */
type MAReportJobSliceInfoResponse struct {
	*tchttp.BaseResponse
	Response *struct {
		// 唯一请求 ID，每次请求都会返回。定位问题时需要提供该次请求的 RequestId。
		RequestId *string `json:"RequestId,omitempty" name:"RequestId"`
	} `json:"Response"`
}

// Marshall to JSON string
func (r *MAReportJobSliceInfoResponse) ToJsonString() string {
	b, _ := json.Marshal(r)
	return string(b)
}

// Unmarshall from JSON string
func (r *MAReportJobSliceInfoResponse) FromJsonString(s string) error {
	return json.Unmarshal([]byte(s), &r)
}

// 遍历过程中上报计数
type MAReportJobScanInfoReq struct {
	*tchttp.BaseRequest
	// 任务ID
	JobId string `protobuf:"bytes,1,opt,name=JobId,proto3" json:"JobId,omitempty"`
	// 符合条件数
	FitNum string `protobuf:"bytes,2,opt,name=FitNum,proto3" json:"FitNum,omitempty"`
	// 已扫描数
	ScanNum string `protobuf:"bytes,3,opt,name=ScanNum,proto3" json:"ScanNum,omitempty"`
}

// Marshall to JSON string
func (r *MAReportJobScanInfoReq) ToJsonString() string {
	b, _ := json.Marshal(r)
	return string(b)
}

// Unmarshall from JSON string
func (r *MAReportJobScanInfoReq) FromJsonString(s string) error {
	return json.Unmarshal([]byte(s), &r)
}

// 遍历过程中上报计数的响应
type MAReportJobScanInfoResp struct {
	*tchttp.BaseResponse
	Response *struct {
		// 唯一请求 ID，每次请求都会返回。定位问题时需要提供该次请求的 RequestId。
		RequestId *string `json:"RequestId,omitempty" name:"RequestId"`
	} `json:"Response"`
}

// Marshall to JSON string
func (r *MAReportJobScanInfoResp) ToJsonString() string {
	b, _ := json.Marshal(r)
	return string(b)
}

// Unmarshall from JSON string
func (r *MAReportJobScanInfoResp) FromJsonString(s string) error {
	return json.Unmarshal([]byte(s), &r)
}

/**
 * 上报任务失败原因参数
 */
type ReportJobStopReasonReq struct {
	*tchttp.BaseRequest
	// 任务ID
	JobId      *string `json:"JobId,omitempty" name:"JobId"`
	StopReason *string `json:"StopReason,omitempty" name:"StopReason"`
}

// Marshall to JSON string
func (r *ReportJobStopReasonReq) ToJsonString() string {
	b, _ := json.Marshal(r)
	return string(b)
}

// Unmarshall from JSON string
func (r *ReportJobStopReasonReq) FromJsonString(s string) error {
	return json.Unmarshal([]byte(s), &r)
}

/**
 * 上报任务失败原因返回参数
 */
type ReportJobStopReasonResp struct {
	*tchttp.BaseResponse
	Response *struct {
		// 唯一请求 ID，每次请求都会返回。定位问题时需要提供该次请求的 RequestId。
		RequestId *string `json:"RequestId,omitempty" name:"RequestId"`
	} `json:"Response"`
}

// Marshall to JSON string
func (r *ReportJobStopReasonResp) ToJsonString() string {
	b, _ := json.Marshal(r)
	return string(b)
}

// Unmarshall from JSON string
func (r *ReportJobStopReasonResp) FromJsonString(s string) error {
	return json.Unmarshal([]byte(s), &r)
}

/**
 *  获取临时秘钥
 */
type DescribeTmpSecretReq struct {
	*tchttp.BaseRequest
	// 任务ID
	JobId *string `json:"JobId,omitempty" name:"JobId"`
}

// Marshall to JSON string
func (r *DescribeTmpSecretReq) ToJsonString() string {
	b, _ := json.Marshal(r)
	return string(b)
}

// Unmarshall from JSON string
func (r *DescribeTmpSecretReq) FromJsonString(s string) error {
	return json.Unmarshal([]byte(s), &r)
}

/**
 * 获取临时秘钥返回参数
 */
type DescribeTmpSecretResp struct {
	*tchttp.BaseResponse
	Response *struct {
		// 唯一请求 ID，每次请求都会返回。定位问题时需要提供该次请求的 RequestId。
		RequestId *string `json:"RequestId,omitempty" name:"RequestId"`
		// 任务ID
		JobId *string `json:"JobId,omitempty" name:"JobId"`
		// 临时secretId
		TmpSecretId string `json:"TmpSecretId,omitempty" name:"TmpSecretId"`
		// 临时secretkey
		TmpSecretKey string `json:"TmpSecretKey,omitempty" name:"TmpSecretKey"`
		// 临时token
		SessionToken string `json:"SessionToken,omitempty" name:"SessionToken"`
	} `json:"Response"`
}

// Marshall to JSON string
func (r *DescribeTmpSecretResp) ToJsonString() string {
	b, _ := json.Marshal(r)
	return string(b)
}

// Unmarshall from JSON string
func (r *DescribeTmpSecretResp) FromJsonString(s string) error {
	return json.Unmarshal([]byte(s), &r)
}

/**
 *  获取临时秘钥
 */
type DescribeAccountInfoReq struct {
	*tchttp.BaseRequest
}

// Marshall to JSON string
func (r *DescribeAccountInfoReq) ToJsonString() string {
	b, _ := json.Marshal(r)
	return string(b)
}

// Unmarshall from JSON string
func (r *DescribeAccountInfoReq) FromJsonString(s string) error {
	return json.Unmarshal([]byte(s), &r)
}

/**
 * 获取临时秘钥返回参数
 */
type DescribeAccountInfoResp struct {
	*tchttp.BaseResponse
	Response *struct {
		// 唯一请求 ID，每次请求都会返回。定位问题时需要提供该次请求的 RequestId。
		RequestId *string `json:"RequestId,omitempty" name:"RequestId"`
		// 任务ID
		AccountArea int64 `json:"AccountArea,omitempty" name:"AccountArea"`
	} `json:"Response"`
}

// Marshall to JSON string
func (r *DescribeAccountInfoResp) ToJsonString() string {
	b, _ := json.Marshal(r)
	return string(b)
}

// Unmarshall from JSON string
func (r *DescribeAccountInfoResp) FromJsonString(s string) error {
	return json.Unmarshal([]byte(s), &r)
}

type MAReportJobHeartRequest struct {
	*tchttp.BaseRequest
	//任务ID
	JobId *string `protobuf:"bytes,1,opt,name=JobId,proto3" json:"JobId,omitempty"`
	//agent worker信息
	//eg:[{"Ip":"127.0.0.1","IsLeader":1,"Status":1,""},{"Ip":"*********","IsLeader":0,"Status":1}]
	//WorkersInfo.0.Ip=ddd&WorkersInfo.0.Ip=ddd
	WorkersInfo []*msp.WorkerInfo `protobuf:"bytes,2,rep,name=WorkersInfo,proto3" json:"WorkersInfo,omitempty"`
}

func (r *MAReportJobHeartRequest) ToJsonString() string {
	b, _ := json.Marshal(r)
	return string(b)
}

func (r *MAReportJobHeartRequest) FromJsonString(s string) error {
	return json.Unmarshal([]byte(s), &r)
}

type MAReportJobHeartResponse struct {
	*tchttp.BaseResponse
	Response *struct {
		Result *struct {
			Code    *string `json:"Code,omitempty" name:"Code"`
			Message *string `json:"Message,omitempty" name:"Message"`
		} `json:"Result" name:"Result"`

		// 唯一请求 ID，每次请求都会返回。定位问题时需要提供该次请求的 RequestId。
		RequestId *string `json:"RequestId,omitempty" name:"RequestId"`
	} `json:"Response"`
}

func (r *MAReportJobHeartResponse) ToJsonString() string {
	b, _ := json.Marshal(r)
	return string(b)
}

func (r *MAReportJobHeartResponse) FromJsonString(s string) error {
	return json.Unmarshal([]byte(s), &r)
}
