package tools

import (
	"fmt"
	"math/rand"
	"reflect"
	"strings"
	"time"
)

type reducetype func(interface{}) interface{}
type filtertype func(interface{}) bool

func JoinSlices(slices []interface{}, seperater string) error {

	return nil
}

func SubInt64Slice(sl []int64, from int, to int) []int64 {

	if sl == nil {
		return nil
	}

	le := len(sl)
	if le == 0 {
		return nil
	}

	offset := to - from
	// 防止数组越界
	if from < 0 || to < 0 {
		from = 0
		to = offset
	}

	// 如果超大 返回最后
	// if from > le {
	// 	return nil
	// }

	if from > le {
		mod := le % offset
		off := le - mod
		if mod == 0 {
			off = le - offset
		}
		to = le
		return sl[off:le]
	}

	if to > le {
		return sl[from:le]
	}

	return sl[from:to]

}

// InSlice checks given string in string slice or not.
func InSlice(v string, sl []string) bool {
	for _, vv := range sl {
		if vv == v {
			return true
		}
	}
	return false
}

// InSliceIface checks given interface in interface slice.
func InSliceIface(v interface{}, sl []interface{}) bool {
	for _, vv := range sl {
		if vv == v {
			return true
		}
	}
	return false
}

func InSliceCommon(raw interface{}, rawSlice interface{}) bool {
	sliceValue := reflect.ValueOf(rawSlice)
	if sliceValue.Kind() != reflect.Slice || sliceValue.Len() == 0 {
		return false
	}

	rawValue := reflect.ValueOf(raw)

	for i := 0; i < sliceValue.Len(); i++ {
		element := sliceValue.Index(i)
		if element.Kind() == reflect.Interface {
			element = element.Elem()
		}
		if !rawValue.IsValid() && !element.IsValid() {
			return true
		} else if !rawValue.IsValid() || !element.IsValid() {
			continue
		}
		if rawValue.Kind() != element.Kind() {
			continue
		}
		if reflect.DeepEqual(element.Interface(), rawValue.Interface()) {
			return true
		}
	}

	return false
}

// SliceRandList generate an int slice from min to max.
func SliceRandList(min, max int) []int {
	if max < min {
		min, max = max, min
	}
	length := max - min + 1
	t0 := time.Now()
	rand.Seed(int64(t0.Nanosecond()))
	list := rand.Perm(length)
	for index := range list {
		list[index] += min
	}
	return list
}

// SliceMerge merges interface slices to one slice.
func SliceMerge(slice1, slice2 []interface{}) (c []interface{}) {
	c = append(slice1, slice2...)
	return
}

// SliceReduce generates a new slice after parsing every value by reduce function
func SliceReduce(slice []interface{}, a reducetype) (dslice []interface{}) {
	for _, v := range slice {
		dslice = append(dslice, a(v))
	}
	return
}

// SliceRand returns random one from slice.
func SliceRand(a []interface{}) (b interface{}) {
	randnum := rand.Intn(len(a))
	b = a[randnum]
	return
}

// SliceSum sums all values in int64 slice.
func SliceSum(intslice []int64) (sum int64) {
	for _, v := range intslice {
		sum += v
	}
	return
}

// SliceFilter generates a new slice after filter function.
func SliceFilter(slice []interface{}, a filtertype) (ftslice []interface{}) {
	for _, v := range slice {
		if a(v) {
			ftslice = append(ftslice, v)
		}
	}
	return
}

// SliceDiff returns diff slice of slice1 - slice2.
func SliceDiff(slice1, slice2 []interface{}) (diffslice []interface{}) {
	for _, v := range slice1 {
		if !InSliceIface(v, slice2) {
			diffslice = append(diffslice, v)
		}
	}
	return
}

// SliceIntersect returns slice that are present in all the slice1 and slice2.
func SliceIntersect(slice1, slice2 []interface{}) (diffslice []interface{}) {
	for _, v := range slice1 {
		if InSliceIface(v, slice2) {
			diffslice = append(diffslice, v)
		}
	}
	return
}

// SliceChuck separates one slice to some sized slice.
func SliceChunk(slice []interface{}, size int) (chunkslice [][]interface{}) {
	if size >= len(slice) {
		chunkslice = append(chunkslice, slice)
		return
	}
	end := size
	for i := 0; i <= (len(slice) - size); i += size {
		chunkslice = append(chunkslice, slice[i:end])
		end += size
	}
	return
}

// SliceRange generates a new slice from begin to end with step duration of int64 number.
func SliceRange(start, end, step int64) (intslice []int64) {
	for i := start; i <= end; i += step {
		intslice = append(intslice, i)
	}
	return
}

// SlicePad prepends size number of val into slice.
func SlicePad(slice []interface{}, size int, val interface{}) []interface{} {
	if size <= len(slice) {
		return slice
	}
	for i := 0; i < (size - len(slice)); i++ {
		slice = append(slice, val)
	}
	return slice
}

// SliceUnique cleans repeated values in slice.
func SliceUnique(slice []interface{}) (uniqueslice []interface{}) {
	for _, v := range slice {
		if !InSliceIface(v, uniqueslice) {
			uniqueslice = append(uniqueslice, v)
		}
	}
	return
}

// SliceShuffle shuffles a slice.
func SliceShuffle(slice []interface{}) []interface{} {
	for i := 0; i < len(slice); i++ {
		a := rand.Intn(len(slice))
		b := rand.Intn(len(slice))
		slice[a], slice[b] = slice[b], slice[a]
	}
	return slice
}

//按值删除slice中的元素
func StrSliceDelVal(value string, inSlice []string) []string {
	outSlice := []string{}
	for i := 0; i < len(inSlice); i++ {
		if inSlice[i] != value {
			outSlice = append(outSlice, inSlice[i])
		}
	}
	return outSlice
}

// 性能
func SliceJoin(sep string, args ...interface{}) string {

	cons := make([]string, 0, len(args))
	for _, arg := range args {
		cons = append(cons, fmt.Sprintf("%v", arg))
	}
	return strings.Join(cons, sep)
}
