package tools

import (
	"net/http"

	"github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common"
	tchttp "github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common/http"
	"github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common/profile"
)

// MSP 云API 版本号
const APIVersion = "2018-03-19"

// 云API 客户端
type Client struct {
	common.Client
}

// 初始化 云API 客户端
func NewClient(credential *common.Credential, region string, clientProfile *profile.ClientProfile) (client *Client, err error) {
	client = &Client{}
	client.Init(region).
		WithCredential(credential).
		WithProfile(clientProfile).
		WithHttpTransport(&http.Transport{
			MaxIdleConnsPerHost: 2000,
			MaxConnsPerHost:     2000,
		})
	return
}

// 上报 agent 汇总任务进度 req
func NewMAReportJobProgressRequest() (request *MAReportJobProgressRequest) {
	request = &MAReportJobProgressRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("msp", APIVersion, "MAReportJobProgress")
	return
}

// 上报 agent 汇总任务进度 resp
func NewMAReportJobProgressResponse() (response *MAReportJobProgressResponse) {
	response = &MAReportJobProgressResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 迁移agent上报任务进度
func (c *Client) MAReportJobProgress(request *MAReportJobProgressRequest) (response *MAReportJobProgressResponse, err error) {
	if request == nil {
		request = NewMAReportJobProgressRequest()
	}
	response = NewMAReportJobProgressResponse()
	err = c.Send(request, response)
	return
}

// 上报JobResult存储状态 req
func NewMAReportJobResultStoreStatusRequest() (request *MAReportJobResultStoreStatusReq) {
	request = &MAReportJobResultStoreStatusReq{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("msp", APIVersion, "MAReportJobResultStoreStatus")
	return
}

// 上报JobResult存储状态 resp
func NewMAReportJobResultStoreStatusResponse() (response *MAReportJobResultStoreStatusResponse) {
	response = &MAReportJobResultStoreStatusResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 上报JobResult存储状态
func (c *Client) MAReportJobResultStoreStatus(request *MAReportJobResultStoreStatusReq) (response *MAReportJobResultStoreStatusResponse, err error) {
	if request == nil {
		request = NewMAReportJobResultStoreStatusRequest()
	}
	response = NewMAReportJobResultStoreStatusResponse()
	err = c.Send(request, response)
	return
}

// 获取迁移任务列表列表
func (c *Client) MAListMigrationJobsV2(request *MAListMigrationJobsV2Request) (response *MAListMigrationJobsV2Response, err error) {
	if request == nil {
		request = NewMAListMigrationJobsV2Request()
	}
	response = NewMAListMigrationJobsV2Response()
	err = c.Send(request, response)
	return
}

// 获取迁移任务列表列表 req
func NewMAListMigrationJobsV2Request() (request *MAListMigrationJobsV2Request) {
	request = &MAListMigrationJobsV2Request{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("msp", APIVersion, "MAListMigrationJobsV2")
	return
}

// 获取迁移任务列表列表 resp
func NewMAListMigrationJobsV2Response() (response *MAListMigrationJobsV2Response) {
	response = &MAListMigrationJobsV2Response{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 拉取通道内的主机(Agent)
func (c *Client) MADescribeChannelIp(request *MADescribeChannelIpRequest) (response *MADescribeChannelIpResponse, err error) {
	if request == nil {
		request = NewMADescribeChannelIpRequest()
	}
	response = NewMADescribeChannelIpResponse()
	err = c.Send(request, response)
	return
}

// 拉取通道内的主机(Agent) req
func NewMADescribeChannelIpRequest() (request *MADescribeChannelIpRequest) {
	request = &MADescribeChannelIpRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("msp", APIVersion, "MADescribeChannelIp")
	return
}

// 拉取通道内的主机(Agent) resp
func NewMADescribeChannelIpResponse() (response *MADescribeChannelIpResponse) {
	response = &MADescribeChannelIpResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 上报任务任务进度 req
func NewMAReportJobStatusRequest() (request *MAReportJobStatusRequest) {
	request = &MAReportJobStatusRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("msp", APIVersion, "MAReportJobStatus")
	return
}

// 上报任务任务进度 resp
func NewMAReportJobStatusResponse() (response *MAReportJobStatusResponse) {
	response = &MAReportJobStatusResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 修改任务状态
func (c *Client) MAReportJobStatus(request *MAReportJobStatusRequest) (response *MAReportJobStatusResponse, err error) {
	if request == nil {
		request = NewMAReportJobStatusRequest()
	}
	response = NewMAReportJobStatusResponse()
	err = c.Send(request, response)
	return
}

// 查询任务状态
func (c *Client) MADescribeJobInfo(request *MADescribeJobInfoRequest) (response *MADescribeJobInfoResponse, err error) {
	if request == nil {
		request = NewMADescribeJobInfoRequest()
	}
	response = NewMADescribeJobInfoResponse()
	err = c.Send(request, response)
	return
}

// 查询任务状态 req
func NewMADescribeJobInfoRequest() (request *MADescribeJobInfoRequest) {
	request = &MADescribeJobInfoRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("msp", APIVersion, "MADescribeJobInfo")
	return
}

// 查询任务状态 resp
func NewMADescribeJobInfoResponse() (response *MADescribeJobInfoResponse) {
	response = &MADescribeJobInfoResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 遍历结束上报遍历结果
// NOTE: 更新以下字段
//  detail.FileTotal
//  detail.FileSize
//  detail.PartNum
func (c *Client) MAReportJobSliceInfo(request *MAReportJobSliceInfoRequest) (response *MAReportJobSliceInfoResponse, err error) {
	if request == nil {
		request = NewMAReportJobSliceInfoRequest()
	}
	response = NewMAReportJobSliceInfoResponse()
	err = c.Send(request, response)
	return
}

// 遍历上报遍历结果 req
func NewMAReportJobSliceInfoRequest() (request *MAReportJobSliceInfoRequest) {
	request = &MAReportJobSliceInfoRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("msp", APIVersion, "MAReportJobSliceInfo")
	return
}

// 遍历上报遍历结果 resp
func NewMAReportJobSliceInfoResponse() (response *MAReportJobSliceInfoResponse) {
	response = &MAReportJobSliceInfoResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 遍历过程中上报计数
// NOTE：更新以下字段
//  detail.FitCondFileNum
//  detail.ScanFileNum
func (c *Client) MAReportJobScanInfo(request *MAReportJobScanInfoReq) (response *MAReportJobScanInfoResp, err error) {
	if request == nil {
		request = NewMAReportJobScanInfoReq()
	}
	response = NewMAReportJobScanInfoResponse()
	err = c.Send(request, response)
	return
}

// 遍历过程中上报计数 req
func NewMAReportJobScanInfoReq() (request *MAReportJobScanInfoReq) {
	request = &MAReportJobScanInfoReq{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("msp", APIVersion, "MAReportJobScanInfo")
	return
}

// 遍历过程中上报计数 resp
func NewMAReportJobScanInfoResponse() (response *MAReportJobScanInfoResp) {
	response = &MAReportJobScanInfoResp{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 上报任务失败中止原因
func (c *Client) ReportJobStopReason(request *ReportJobStopReasonReq) (response *ReportJobStopReasonResp, err error) {
	if request == nil {
		request = NewReportJobStopReasonReq()
	}
	response = NewReportJobStopReasonResp()
	err = c.Send(request, response)
	return
}

// 上报任务失败中止原因 req
func NewReportJobStopReasonReq() (request *ReportJobStopReasonReq) {
	request = &ReportJobStopReasonReq{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("msp", APIVersion, "ReportJobStopReason")
	return
}

// 上报任务失败中止原因 resp
func NewReportJobStopReasonResp() (response *ReportJobStopReasonResp) {
	response = &ReportJobStopReasonResp{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 获取临时秘钥
func (c *Client) DescribeTmpSecret(request *DescribeTmpSecretReq) (response *DescribeTmpSecretResp, err error) {
	if request == nil {
		request = NewDescribeTmpSecretReq()
	}
	response = NewDescribeTmpSecretResp()
	err = c.Send(request, response)
	return
}

// 获取临时秘钥 req
func NewDescribeTmpSecretReq() (request *DescribeTmpSecretReq) {
	request = &DescribeTmpSecretReq{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("msp", APIVersion, "MADescribeTmpSecret")
	return
}

// 获取临时秘钥 resp
func NewDescribeTmpSecretResp() (response *DescribeTmpSecretResp) {
	response = &DescribeTmpSecretResp{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 获取账号区域
func (c *Client) DescribeAccountInfo(request *DescribeAccountInfoReq) (response *DescribeAccountInfoResp, err error) {
	if request == nil {
		request = NewDescribeAccountInfoReq()
	}
	response = NewDescribeAccountInfoResp()
	err = c.Send(request, response)
	return
}

// 获取临时秘钥 req
func NewDescribeAccountInfoReq() (request *DescribeAccountInfoReq) {
	request = &DescribeAccountInfoReq{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("msp", APIVersion, "DescribeAccountInfo")
	return
}

// 获取临时秘钥 resp
func NewDescribeAccountInfoResp() (response *DescribeAccountInfoResp) {
	response = &DescribeAccountInfoResp{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

func NewMAReportJobHeartRequest() (request *MAReportJobHeartRequest) {
	request = &MAReportJobHeartRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("msp", APIVersion, "MAReportJobHeart")
	return
}

func NewMAReportJobHeartResponse() (response *MAReportJobHeartResponse) {
	response = &MAReportJobHeartResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

func (c *Client) MAReportJobHeart(request *MAReportJobHeartRequest) (response *MAReportJobHeartResponse, err error) {
	if request == nil {
		request = NewMAReportJobHeartRequest()
	}
	response = NewMAReportJobHeartResponse()
	err = c.Send(request, response)
	return
}
