package tools

import (
	"encoding/base64"
	"git.code.oa.com/cloud-msp/migration-lister/tools/aesTool"
)

const (
	AesKey       = "hlw-msp"
	AesBlockSize = 16
)

func DecryptSecret(encode string) (decryptStr string, err error) {
	decode, _ := base64.StdEncoding.DecodeString(encode)
	tool := aesTool.NewAesTool([]byte(AesKey), AesBlockSize, aesTool.ECB)
	decrypt, err := tool.Decrypt(decode)
	decryptStr = string(decrypt)
	return decryptStr, err
}

func EncryptSecret(src string) (encode string, err error) {
	tool := aesTool.NewAesTool([]byte(AesKey), AesBlockSize, aesTool.ECB)
	encrypt, err := tool.Encrypt([]byte(src))
	encode = base64.StdEncoding.EncodeToString(encrypt)
	return encode, err
}
