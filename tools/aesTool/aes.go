package aesTool

import (
	"bytes"
	"crypto/aes"
	"crypto/cipher"
	"crypto/rand"
	"fmt"
	"io"
)

const (
	ECB = 1
	CBC = 2
)

// AES ECB模式的加密解密
type AesTool struct {
	// 128 192  256位的其中一个 长度 对应分别是 16 24  32字节长度
	Key       []byte
	BlockSize int
	Mode      int
}

func NewAesTool(key []byte, blockSize int, mode int) *AesTool {
	return &AesTool{Key: key, BlockSize: blockSize, Mode: mode}
}

/**
注意：0填充方式
*/
func (this *AesTool) padding(src []byte) []byte {
	// 填充个数
	paddingCount := aes.BlockSize - len(src)%aes.BlockSize
	if paddingCount == 0 {
		return src
	} else {
		// 填充数据
		return append(src, bytes.Repeat([]byte{byte(0)}, paddingCount)...)
	}
}

// unpadding
func (this *AesTool) unPadding(src []byte) []byte {
	for i := len(src) - 1; i >= 0; i-- {
		if src[i] != 0 {
			return src[:i+1]
		}
	}
	return nil
}

func (this *AesTool) Encrypt(src []byte) ([]byte, error) {
	var encryptData []byte
	// key只能是 16 24 32长度
	key := this.padding(this.Key)
	block, err := aes.NewCipher([]byte(key))
	if err != nil {
		return nil, err
	}
	// padding
	src = this.padding(src)

	switch this.Mode {
	case ECB:
		encryptData = make([]byte, len(src))
		mode := NewECBEncrypter(block)
		mode.CryptBlocks(encryptData, src)
		break
	case CBC:
		// The IV needs to be unique, but not secure. Therefore it's common to
		// include it at the beginning of the ciphertext.
		encryptData := make([]byte, aes.BlockSize+len(src))
		iv := encryptData[:aes.BlockSize]
		if _, err := io.ReadFull(rand.Reader, iv); err != nil {
			panic(err)
		}

		mode := cipher.NewCBCEncrypter(block, iv)
		mode.CryptBlocks(encryptData[aes.BlockSize:], src)
		break
	}

	return encryptData, nil

}
func (this *AesTool) Decrypt(src []byte) ([]byte, error) {
	// key只能是 16 24 32长度

	key := this.padding(this.Key)

	block, err := aes.NewCipher([]byte(key))
	if err != nil {
		return nil, err
	}

	switch this.Mode {
	case ECB:
		mode := NewECBDecrypter(block)

		// CryptBlocks can work in-place if the two arguments are the same.
		mode.CryptBlocks(src, src)
		break
	case CBC:
		iv := src[:aes.BlockSize]
		src = src[aes.BlockSize:]
		fmt.Printf("decode iv :%x\n", iv)
		// CBC mode always works in whole blocks.
		if len(src)%aes.BlockSize != 0 {
			panic("ciphertext is not a multiple of the block size")
		}

		mode := cipher.NewCBCDecrypter(block, iv)

		// CryptBlocks can work in-place if the two arguments are the same.
		mode.CryptBlocks(src, src)
		break
	}

	return this.unPadding(src), nil
}
