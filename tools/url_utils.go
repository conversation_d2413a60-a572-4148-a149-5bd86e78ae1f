package tools

import (
	"errors"
	"fmt"
	"net"
	"net/url"
	"strconv"
	"strings"

	secapi "git.code.oa.com/sec-api/go/scurl"
)

func GetUrl(ip, port string) string {
	return fmt.Sprintf("http://%s:%s", ip, port)
}

func GetLocalIp() (ipv4 string, err error) {
	var (
		addrs   []net.Addr
		addr    net.Addr
		ipNet   *net.IPNet // IP地址
		isIpNet bool
	)
	// 获取所有网卡
	if addrs, err = net.InterfaceAddrs(); err != nil {
		return
	}
	// 取第一个非loop的网卡IP
	for _, addr = range addrs {
		// 这个网络地址是IP地址: ipv4, ipv6
		if ipNet, isIpNet = addr.(*net.IPNet); isIpNet && !ipNet.IP.IsLoopback() {
			// 跳过IPV6
			if ipNet.IP.To4() != nil {
				ipv4 = ipNet.IP.String()
				return
			}
		}
	}

	err = errors.New("ERR_NO_LOCAL_IP_FOUND")
	return
}

func SsrfSecurityCheck(l string) error {
	secapi.WithAllowPorts([]string{"allow_all"})
	secCli := secapi.NewSafeClient()
	_, err := secCli.Get(l)
	if err != nil {
		return fmt.Errorf("ssrf security check err[%v]", err)
	}

	u, err := url.Parse(l)
	if err != nil {
		return nil
	}

	host := u.Hostname()
	addrs, err := net.LookupHost(host)
	if err != nil {
		return nil
	}
	for _, addr := range addrs {
		for _, ip := range InnerIp {
			ok, err := IsBelong(addr, ip)
			if err != nil {
				return fmt.Errorf("invalid domain[%s]", host)
			}
			if ok {
				return fmt.Errorf("internal domain not allowed, domain[%s]", host)
			}
		}
	}
	return nil
}

/*func SsrfSecurityCheck(l string) error {
	u, err := url.Parse(l)
	if err != nil {
		return nil
	}
	ips, err := net.LookupIP(u.Hostname())
	if err != nil {
		return nil
	}
	if len(ips) == 0 {
		return nil
	}
	ip := ips[0]
	internal := false

	// https://iwiki.woa.com/pages/viewpage.action?pageId=1325302742
	// ***********/16
	if ip[0] == 192 && ip[1] == 168 {
		internal = true
	}
	// *******/8
	// 10.0.0.0/8
	// *********/8
	// ********/8
	// 30.0.0.0/8
	// ********/8
	// ********/8
	// ********/8
	// ********/8
	// 2*******/8
	if ip[0] == 9 || ip[0] == 10 || ip[0] == 127 || ip[0] == 11 || ip[0] == 30 ||
		ip[0] == 21 || ip[0] == 22 || ip[0] == 26 || ip[0] == 28 || ip[0] == 29 {
		internal = true
	}
	// **********/10
	if ip[0] == 100 && ip[1] >= 64 && ip[1] <= 127 {
		internal = true
	}
	// **********/12
	if ip[0] == 172 && ip[1] >= 16 && ip[1] <= 31 {
		internal = true
	}
	if internal {
		return fmt.Errorf("internal domain not allowed, domain:%s", u.Hostname())
	}
	return nil
}*/

// IsBelong
func IsBelong(ip, cidr string) (bool, error) {
	ipAddrs := strings.Split(ip, `.`)
	if len(ipAddrs) < 4 {
		return false, nil
	}
	cidrArr := strings.Split(cidr, `/`)
	if len(cidrArr) < 2 {
		return false, nil
	}
	int0, err := strconv.Atoi(ipAddrs[0])
	if err != nil {
		return false, err
	}
	int1, err := strconv.Atoi(ipAddrs[1])
	if err != nil {
		return false, err
	}
	int2, err := strconv.Atoi(ipAddrs[2])
	if err != nil {
		return false, err
	}
	int3, err := strconv.Atoi(ipAddrs[3])
	if err != nil {
		return false, err
	}
	ipAddr := (int0 << 24) | (int1 << 16) | (int2 << 8) | int3

	masks, _ := strconv.Atoi(cidrArr[1])
	mask := 0xFFFFFFFF << (32 - masks)
	cidrIp := cidrArr[0]
	cidrIps := strings.Split(cidrIp, ".")

	int00, _ := strconv.Atoi(cidrIps[0])
	int11, _ := strconv.Atoi(cidrIps[1])
	int22, _ := strconv.Atoi(cidrIps[2])
	int33, _ := strconv.Atoi(cidrIps[3])
	cidrIpAddr := (int00 << 24) | (int11 << 16) | (int22 << 8) | int33

	r := (ipAddr & mask) == (cidrIpAddr & mask)
	return r, nil
}

// InnerIp
var InnerIp = []string{
	"10.0.0.0/8",
	"**********/12",
	"***********/16",
	"**********/10",
	"*******/8",
	"********/8",
	"30.0.0.0/8",
	"*********/8",
	"********/8",
	"********/8",
	"********/8",
	"********/8",
	"2*******/8",
	"***********/16",
}
